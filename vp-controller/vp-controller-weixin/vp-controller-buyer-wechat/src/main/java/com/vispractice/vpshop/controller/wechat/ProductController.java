/**
 * 版权所有：版权所有(C) 2016，远行科技
 * 文件编号：M01_ProductController.java
 * 文件名称：ProductController.java
 * 系统编号：远行福利plus
 * 设计作者：彭龙
 * 完成日期：2016年7月20日
 * 设计文档：
 * 内容摘要：TODO
 * 系统用例：
 * 界面原型：
 */
package com.vispractice.vpshop.controller.wechat;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;
import javax.jms.Destination;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.vispractice.vpshop.entity.PurchaseList;
import com.vispractice.vpshop.jstk.JSTKService;
import com.vispractice.vpshop.service.ActProductService;
import com.vispractice.vpshop.service.PurchaseListService;
import com.vispractice.vpshop.solr.SolrUtil;
import com.vispractice.vpshop.tmall.TMallService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.ServletContextAware;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vispractice.vpshop.CommonAttributes;
import com.vispractice.vpshop.Filter;
import com.vispractice.vpshop.Message;
import com.vispractice.vpshop.Order;
import com.vispractice.vpshop.Page;
import com.vispractice.vpshop.Pageable;
import com.vispractice.vpshop.ResourceNotFoundException;
import com.vispractice.vpshop.Setting;
import com.vispractice.vpshop.activemq.ProducerService;
import com.vispractice.vpshop.activemq.log.LogProductMsgBean;
import com.vispractice.vpshop.activemq.log.LogSearchMsgBean;
import com.vispractice.vpshop.api.dangaoss2.response.ProductExpressRangeResponse;
import com.vispractice.vpshop.bnplus.service.BnplusService;
import com.vispractice.vpshop.constant.Constants;
import com.vispractice.vpshop.dangaoss2.Dangaoss2Service;
import com.vispractice.vpshop.eascs.service.EascsSyncService;
import com.vispractice.vpshop.entity.Act;
import com.vispractice.vpshop.entity.Act.ActType;
import com.vispractice.vpshop.entity.ActProduct;
import com.vispractice.vpshop.entity.Area;
import com.vispractice.vpshop.entity.Brand;
import com.vispractice.vpshop.entity.Company;
import com.vispractice.vpshop.entity.Coupon;
import com.vispractice.vpshop.entity.Group;
import com.vispractice.vpshop.entity.GroupProduct;
import com.vispractice.vpshop.entity.Member;
import com.vispractice.vpshop.entity.Product;
import com.vispractice.vpshop.entity.Product.OrderType;
import com.vispractice.vpshop.entity.ProductCategory;
import com.vispractice.vpshop.entity.ProductHot;
import com.vispractice.vpshop.entity.Promotion;
import com.vispractice.vpshop.entity.Receiver;
import com.vispractice.vpshop.entity.SearchHotVo;
import com.vispractice.vpshop.entity.Supplier;
import com.vispractice.vpshop.entity.SysCode;
import com.vispractice.vpshop.entity.Tag;
import com.vispractice.vpshop.jd.model.request.StockVO;
import com.vispractice.vpshop.jd.service.JdService;
import com.vispractice.vpshop.jdHealth.JdHealthService;
import com.vispractice.vpshop.missfresh.service.MissfreshService;
import com.vispractice.vpshop.service.ActProductShareLogService;
import com.vispractice.vpshop.service.ActService;
import com.vispractice.vpshop.service.AreaService;
import com.vispractice.vpshop.service.BrandService;
import com.vispractice.vpshop.service.CompanyService;
import com.vispractice.vpshop.service.CouponCodeService;
import com.vispractice.vpshop.service.CouponService;
import com.vispractice.vpshop.service.GroupOrderService;
import com.vispractice.vpshop.service.GroupProductService;
import com.vispractice.vpshop.service.GroupService;
import com.vispractice.vpshop.service.JdFeedbackService;
import com.vispractice.vpshop.service.MemberFavoriteProductService;
import com.vispractice.vpshop.service.MemberService;
import com.vispractice.vpshop.service.ProductAddPriceService;
import com.vispractice.vpshop.service.ProductCategoryService;
import com.vispractice.vpshop.service.ProductHotService;
import com.vispractice.vpshop.service.ProductService;
import com.vispractice.vpshop.service.PromotionService;
import com.vispractice.vpshop.service.ReceiverService;
import com.vispractice.vpshop.service.ReviewService;
import com.vispractice.vpshop.service.SearchService;
import com.vispractice.vpshop.service.StaticService;
import com.vispractice.vpshop.service.SupplierService;
import com.vispractice.vpshop.service.SysCodeService;
import com.vispractice.vpshop.service.TagService;
import com.vispractice.vpshop.util.AesUtil;
import com.vispractice.vpshop.util.CommUtil;
import com.vispractice.vpshop.util.EscapeUnescape;
import com.vispractice.vpshop.util.GetPinyin;
import com.vispractice.vpshop.util.SettingUtils;
import com.vispractice.vpshop.util.StringUtil;
import com.vispractice.vpshop.util.WebUtils;
import com.vispractice.vpshop.vo.AddPriceProductPageVo;
import com.vispractice.vpshop.vo.CouponCodeCountVO;
import com.vispractice.vpshop.vo.ReceiverVo;
import com.vispractice.vpshop.xy.service.XyService;
import com.vispractice.vpshop.yanxuan.service.YanxuanService;
import com.vispractice.vpshop.zzyc.service.ZhiZhenYunCaiService;

/**
 * 类 编 号：UI_PU010401_ProductController 类 名 称：ProductController.java
 * 内容摘要：Controller - 商品 完成日期：2016年7月20日 下午2:26:03 编码作者: 彭龙
 */
@Controller("wechatProductController")
@RequestMapping("/product")
public class ProductController extends BaseController implements
		ServletContextAware {
	Logger logger= LoggerFactory.getLogger(ProductController.class);
	@Resource(name = "reviewServiceImpl")
	private ReviewService reviewService;
	@Resource(name = "memberServiceImpl")
	private MemberService memberService;
	@Resource(name = "couponCodeServiceImpl")
	private CouponCodeService couponCodeService;
	@Resource(name = "productServiceImpl")
	private ProductService productService;
	@Resource(name = "productCategoryServiceImpl")
	private ProductCategoryService productCategoryService;
	@Resource(name = "brandServiceImpl")
	private BrandService brandService;
	@Resource(name = "promotionServiceImpl")
	private PromotionService promotionService;
	@Resource(name = "tagServiceImpl")
	private TagService tagService;
	@Resource(name = "searchServiceImpl")
	private SearchService searchService;
	@Resource(name = "receiverServiceImpl")
	private ReceiverService receiverService;
	@Resource(name = "staticServiceImpl")
	private StaticService staticService;
	@Resource(name = "jdServiceImpl")
	private JdService jdService;
	@Resource(name = "couponServiceImpl")
	private CouponService couponService;
	@Resource(name = "groupProductServiceImpl")
	private GroupProductService groupProductService;
	@Resource(name = "groupOrderServiceImpl")
	private GroupOrderService groupOrderService;
	@Resource(name = "memberFavoriteProductServiceImpl")
	private MemberFavoriteProductService memberFavoriteProductService;
	@Resource(name = "productHotServiceImpl")
	private ProductHotService productHotService;
	@Resource(name = "sysCodeServiceImpl")
	private SysCodeService sysCodeService;
	@Resource(name = "companyServiceImpl")
	private CompanyService companyService;
	@Resource(name = "areaServiceImpl")
	private AreaService areaService;
	@Resource(name = "supplierServiceImpl")
	private SupplierService supplierService;
	@Resource
	private YanxuanService yanxuanService;
	@Autowired
	private ProducerService producerService;
	@Autowired
	private GroupService groupService;
	@Autowired
	@Qualifier("queueDestinationHitLog")
	private Destination destination;
	@Resource(name = "jdFeedbackServiceImpl")
	private JdFeedbackService jdFeedbackService;
	@Resource
	private MissfreshService missfreshService;
	@Resource(name = "xyServiceImpl")
	private XyService xyService;
	
	@Resource
	private EascsSyncService eascsSyncService;

	@Resource
	private BnplusService bnplusService;

	// 发送搜索日志队列
	@Autowired
	@Qualifier("queueDestinationSearchLog")
	private Destination destinationSearch;

	@Resource
	private ZhiZhenYunCaiService zhiZhenYunCaiService;
	@Resource
	private Dangaoss2Service dangaoss2Service;
	@Resource(name = "actServiceImpl")
	private ActService actService;
	@Resource
	private ActProductShareLogService actProductShareLogService;
	@Resource
	private ProductAddPriceService productAddPriceService;
	@Resource
	private JdHealthService jdHealthService;
	@Resource
	private ActProductService actProductService;
	@Resource
	private PurchaseListService purchaseListService;
	
	@Resource
	private JSTKService jstkService;

	@Resource
	private TMallService tMallService;

	public void setServletContext(ServletContext servletContext) {
	}

	/**
	 * 方法:京东商城首页 autor :penglong
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping("/index")
	public String index(ModelMap model, HttpSession session) {

//		model.addAttribute("coupons", couponService.getCouponsOrderByAmount());
		Member member = getCurrent();
		Company company=null;
		if(member !=null){
			company=member.getCompanyId();
		}else{
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		model.addAttribute("company", company);
		model.addAttribute("companyId",company.getId());
		model.addAttribute("member", member);
		if(company!=null){
			//		model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		List<Coupon> coupons =couponService.getCouponsOrderByAmount();

		if (member != null) {
			if (coupons != null && coupons.size() > 0) {
				Coupon coupon = null;
				for (int i = 0; i < coupons.size(); i++) {
					coupon = coupons.get(i);
					Long count = couponCodeService.getReceivedCoupon(coupon, member);
					if (count != null && count > 0) {// 已经领取过
						coupon.setIsRecived(true);
					}
				}
			}
		}
		model.addAttribute("coupons", coupons);

		if(company != null && company.getCompanyType() == 3){//商旅企业
			return "redirect:/travelHome/index.jhtml";
		}
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if(!StringUtil.isEmpty(excludeSuppliers)){
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		return "/wechat/product/index1";
	}

	/**
	 * 方法:折扣商城楼层首页 autor :huhui
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping("/discountShop")
	public String discountShop(ModelMap model, HttpSession session) {
		model.addAttribute("coupons", couponService.getCouponsOrderByAmount());
		Long[] hotProductCategoryIds = productHotService
				.getHotProductCategoryOne(6, 0);
		List<ProductCategory> productCategorys = productCategoryService
				.findList(hotProductCategoryIds);
		List<ProductHot> hotProducts = new ArrayList<ProductHot>();
		if(productCategorys.size() > 0){
			hotProducts = productHotService.getHotProduct(productCategorys.get(0)
					.getId(), 6, 0);
		}
		model.addAttribute("hotProducts", hotProducts);
		Member member = memberService.getCurrent();
		model.addAttribute("member", member);
		Company company=null;
		if(member !=null){
			company=member.getCompanyId();
		}else{
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		if(company!=null){
//			model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if(!StringUtil.isEmpty(excludeSuppliers)){
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		model.addAttribute("company", company);
		return "/wechat/product/discountShop";
	}
	/**
	 *
	 * @param model
	 * @param session
	 * @return
	 */
	@RequestMapping("/selfShop")
	public String selfShop(ModelMap model,HttpSession session) {
		Member member=getCurrent();
		Company company=null;
		if(member!=null){
			company=member.getCompanyId();
		}else{
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		//获取自营的分类
		SysCode sc=sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_ROOT_PRODUCT_CATEGORY_IDS_KEY);
		if(sc!=null&&StringUtils.isNotBlank(sc.getValue())){
			String[] array=sc.getValue().split(",");
			List<Long> productCategoryIds=new ArrayList<Long>();
			for (int i = 0; i < array.length; i++) {
				productCategoryIds.add(Long.parseLong(array[i]));
			}
			List<Filter> filters=new ArrayList<Filter>();
			List<Order> orders=new ArrayList<Order>();
			filters.add(Filter.in("id", productCategoryIds));
			orders.add(Order.asc("order"));
			model.addAttribute("rootProductCategories", productCategoryService.findList(12, filters, orders));

		}
		//获取自营供应商
		SysCode supplierSc=sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_SUPPLIER_IDS_KEY);
		if(supplierSc!=null&&StringUtils.isNotBlank(supplierSc.getValue())){
			model.addAttribute("supplierIds",supplierSc.getValue());
		}
		model.addAttribute("company",company);
		if(company!=null){
			//		model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if(!StringUtil.isEmpty(excludeSuppliers)){
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		return "/wechat/product/self_shop";
	}


	/**
	 *
	 * @param model
	 * @param session
	 * @return
	 */
	@RequestMapping("/selfShopNew")
	public String selfShopNew(ModelMap model,HttpSession session) {
		Member member=getCurrent();
		Company company=null;
		if(member!=null){
			company=member.getCompanyId();
		}else{
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}

		//获取自营的分类
		SysCode eascsSysCode=sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_EASCS_ROOT_PRODUCT_CATEGORY_IDS_KEY);
		if(eascsSysCode!=null&&StringUtils.isNotBlank(eascsSysCode.getValue())){
			String[] array=eascsSysCode.getValue().split(",");
			List<Long> productCategoryIds=new ArrayList<Long>();
			for (int i = 0; i < array.length; i++) {
				productCategoryIds.add(Long.parseLong(array[i]));
			}
			List<Filter> filters=new ArrayList<Filter>();
			List<Order> orders=new ArrayList<Order>();
			filters.add(Filter.in("id", productCategoryIds));
			orders.add(Order.asc("order"));
			model.addAttribute("eascsRootProductCategories", productCategoryService.findList(12, filters, orders));
		}

		//获取自营的分类
		SysCode sc=sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_ROOT_PRODUCT_CATEGORY_IDS_KEY);
		if(sc!=null&&StringUtils.isNotBlank(sc.getValue())){
			String[] array=sc.getValue().split(",");
			List<Long> productCategoryIds=new ArrayList<Long>();
			for (int i = 0; i < array.length; i++) {
				productCategoryIds.add(Long.parseLong(array[i]));
			}
			List<Filter> filters=new ArrayList<Filter>();
			List<Order> orders=new ArrayList<Order>();
			filters.add(Filter.in("id", productCategoryIds));
			orders.add(Order.asc("order"));
			model.addAttribute("rootProductCategories", productCategoryService.findList(12, filters, orders));
		}
		//获取自营供应商
		SysCode supplierSc=sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_SUPPLIER_IDS_KEY);
		if(supplierSc!=null&&StringUtils.isNotBlank(supplierSc.getValue())){
			model.addAttribute("supplierIds",supplierSc.getValue());
		}
		model.addAttribute("company",company);
		if(company!=null){
			//		model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		List<Coupon> coupons =couponService.getCouponsOrderByAmount();

		if (member != null) {
			if (coupons != null && coupons.size() > 0) {
				Coupon coupon = null;
				for (int i = 0; i < coupons.size(); i++) {
					coupon = coupons.get(i);
					Long count = couponCodeService.getReceivedCoupon(coupon, member);
					if (count != null && count > 0) {// 已经领取过
						coupon.setIsRecived(true);
					}
				}
			}
		}
		model.addAttribute("coupons", coupons);
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if(!StringUtil.isEmpty(excludeSuppliers)){
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		return "/wechat/product/self_shop_new";
	}

	//自营商城
	@RequestMapping("/selfShopNew2")
	public String selfShopNew2(ModelMap model,HttpSession session) {
		Member member=getCurrent();
		Company company=null;
		if(member!=null){
			company=member.getCompanyId();
		}else{
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}

		//公告通知
		String noticeStr = sysCodeService.getValue(SysCode.SYS_GROUP, "selfShopNotice");
		if (!StringUtil.isEmpty(noticeStr)) {
			model.addAttribute("shopNotice", noticeStr);
		}

		//获取自营供应商
		SysCode supplierSc=sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_SUPPLIER_IDS_KEY);
//		Long supplierId = null;
		if(supplierSc!=null&&StringUtils.isNotBlank(supplierSc.getValue())){
			model.addAttribute("supplierIds",supplierSc.getValue());
			//取配置的首个供应商ID作为分类的供应商
//			supplierId = Long.valueOf(supplierSc.getValue().split(",")[0]);
		}

		//获取自营分类
//		List<Filter> filters=new ArrayList<Filter>();
//		List<Order> orders=new ArrayList<Order>();
//		filters.add(Filter.eq("supplierId", supplierId));
//		filters.add(Filter.eq("grade", 0));
//		filters.add(Filter.eq("enabledFlag", true));
//		orders.add(Order.asc("order"));
//		model.addAttribute("productCategories", productCategoryService.findList(null, filters, orders));

		//查询团购信息
		String code=null;
		if(company!=null) {
			code=company.getGroupCode();
		}
		Group group=groupService.findValidGroup(code);
		if(group!=null) {
			Long time=group.getEndDate().getTime()-new Date().getTime();
			model.addAttribute("time", time);
			List<GroupProduct> groupProducts=groupProductService.findListByGoods(8, group.getId());
			model.addAttribute("groupProducts", groupProducts);//根据商品规格合并返回
			if (groupProducts!=null && !groupProducts.isEmpty()){//查询已拼件数
				for (GroupProduct groupProduct:groupProducts){
					groupProduct.setTotal(groupProductService.getGroupPurchaseCount(group,groupProduct.getId()));
				}
			}
			model.addAttribute("group", group);
		}

		model.addAttribute("company",company);
		if(company!=null){
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		List<Coupon> coupons =couponService.getCouponsOrderByAmount();

		if (member != null) {
			if (coupons != null && coupons.size() > 0) {
				Coupon coupon = null;
				for (int i = 0; i < coupons.size(); i++) {
					coupon = coupons.get(i);
					Long count = couponCodeService.getReceivedCoupon(coupon, member);
					if (count != null && count > 0) {// 已经领取过
						coupon.setIsRecived(true);
					}
				}
			}
			model.addAttribute("member",member);
		}
		model.addAttribute("coupons", coupons);
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if(!StringUtil.isEmpty(excludeSuppliers)){
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		
		return "/wechat/product/self_shop_new2";
	}

	/**
	 * 生鲜商品首页
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping("/freshShop")
	public String freshShop(ModelMap model,HttpSession session) {
        Member member=getCurrent();
        Company company=null;
        if(member!=null){
            company=member.getCompanyId();
        }else{
            String companyIdStr = (String) session.getAttribute("companyIdStr");
            if (companyIdStr != null) {
                Long companyId = Long.valueOf(companyIdStr);
                company = companyService.find(companyId);
            }else{
                company = companyService.getDefultCompany();
            }
        }
		//查询标签ID集合
		SysCode sysCode = sysCodeService.findbyCode("freshType");
		String[] freshTypes = StringUtils.split(sysCode.getValue(), ",");
		Long[] tagIds = new Long[freshTypes.length];;
		for (int i = 0; i < freshTypes.length; i++) {
			tagIds[i] = Long.valueOf(freshTypes[i]);
		}

//		List<Filter> filters1=new ArrayList<Filter>();
//		List<Order> orders1=new ArrayList<Order>();
//		//11061：生鲜顶级分类ID
//		filters1.add(Filter.in("id", tagIds));
//		filters1.add(Filter.eq("enabledFlag", true));
//		orders1.add(Order.asc("orders"));
		List<Tag> tags = tagService.findList(tagIds);

		model.addAttribute("freshTypes", tags);
		model.addAttribute("types", sysCode.getValue());

		//68：京东供应商
		model.addAttribute("supplierIds","68");
		List<Filter> filters=new ArrayList<Filter>();
		List<Order> orders=new ArrayList<Order>();
		//11061：生鲜顶级分类ID
		filters.add(Filter.eq("parent", 11061));
		filters.add(Filter.eq("enabledFlag", true));
		orders.add(Order.asc("order"));
		List<ProductCategory> companyChildrenTwo = productCategoryService.findList(null, filters, orders);

		if(company!=null){
			// 获取企业限制二级分类
			if (!company.getCategoryAll()) {
				// 生鲜一级分类
				ProductCategory productCategory = productCategoryService.find(11061L);
				List<Long> companyCategoryId = productCategoryService.findCompanyCategory(company);
				companyChildrenTwo = productCategoryService.findCompanyCategoryChildren(productCategory, null, companyCategoryId);
			}
		}
		model.addAttribute("productCategories", companyChildrenTwo);

		return "/wechat/product/fresh_shop";
	}

	/**
	 * 商城楼层首页-自有商城 方法: 实现流程: 1. autor :sunjianwen
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping("/index_shop")
	public String indexShop(ModelMap model,HttpSession session) {
		Member member = memberService.getCurrent();
		model.addAttribute("member", member);
		Company company=null;
		if(member !=null){
			company=member.getCompanyId();
		}else{
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		if(company!=null){
			model.addAttribute("company", company);

//			model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if(!StringUtil.isEmpty(excludeSuppliers)){
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		return "/wechat/product/index_shop";
	}

	/**
	 * 行云商城首页
	 * add by hzq
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/xingyun_index", method = {RequestMethod.GET, RequestMethod.POST})
	public String xingyunIndexShop(ModelMap model,HttpSession session) {
		Member member = memberService.getCurrent();
		model.addAttribute("member", member);
		Company company = null;
		if(member!=null) {
			company = member.getCompanyId();
		}else{
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}
		}
		model.addAttribute("company", company);
		model.addAttribute("supplierId",94L);
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if(!StringUtil.isEmpty(excludeSuppliers)){
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		return "/wechat/xingyun/xingyun_index";
	}

	/**
	 * 显示所有分类
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping("/allCate")
	public String allCate(ModelMap model, Boolean isProductCategory,Long supplierId) {
		model.addAttribute("isProductCategory", isProductCategory);
		Member member = memberService.getCurrent();
		model.addAttribute("member", member);
		model.addAttribute("supplierId", supplierId);
		return "/wechat/product/allCate";
	}



	/**
	 * 根据分类显示商品信息
	 *
	 * @param productCategoryId
	 *            分类
	 * @param productCategoryId
	 *            分页
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/catelist", method = {RequestMethod.GET, RequestMethod.POST})
	public String catelist(Long productCategoryId, ModelMap model,HttpSession session,Long supplierId) {
		ProductCategory productCategory = productCategoryService.find(productCategoryId);
		if (productCategory == null) {
			throw new ResourceNotFoundException();
		}
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}

		if (!company.getCategoryAll()) { // 如果非所有商品分类开发查询相应有权限分类 做in查询
			// 动态获取公司下分类
			List<Long> companyCategory = productCategoryService.findCompanyCategory(company);

			// 二级分类企业个性化
			productCategory.getChildren().clear();
			productCategory.setChildren(new HashSet<ProductCategory>(
					productCategoryService.findCompanyCategoryChildren(
							productCategory,null, companyCategory)));

			// 三级分类企业个性化
//			Set<ProductCategory> productCategories = company.getProductCategories();
			List<ProductCategory> thirdProductCategoryList = new ArrayList<>();
			for (ProductCategory twoProductCategory : productCategory.getChildren()) {
				List<ProductCategory> companyChildrenThird = productCategoryService.findCompanyCategoryChildren(twoProductCategory, null, companyCategory);
				thirdProductCategoryList.addAll(companyChildrenThird);
			}
			model.addAttribute("thirdProductCategoryList", thirdProductCategoryList);
		}
		model.addAttribute("productCategory", productCategory);
		if (productCategory.getGrade().intValue() == 1) {
			productCategory = productCategory.getParent();
		}
		if (productCategory.getGrade().intValue() == 2) {
			productCategory = productCategory.getParent().getParent();
		}
		model.addAttribute("productCategoryp", productCategory);//获取一级分类
		model.addAttribute("productCategoryId", productCategoryId);
		model.addAttribute("member", member);
		model.addAttribute("supplierId", supplierId);
		return "/wechat/product/cate_list";

	}
	
	
	/**
	 *方法:工业品商城分类
	 *作者:sangyj
	 * @param model
	 * @param session
	 * @param supplierId
	 * @return
	 */
	@RequestMapping(value = "/industrialCates", method = {RequestMethod.GET, RequestMethod.POST})
	public String industrialCates(Long categoryId,ModelMap model,HttpSession session) {
		Long cateId = CommonAttributes.INDUSTRIAL_CATEGORY_ID;
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		//获取所有一级分类
        List<ProductCategory> productCategories = productCategoryService.findIndustrialCate(company);
        ProductCategory productCategory = null;
        //默认取第一个
        if(productCategories==null || productCategories.isEmpty()) {
        	return ERROR_VIEW;
        }
        if(categoryId!=null) {
        	productCategory = productCategoryService.find(categoryId);
        }else {
        	productCategory = productCategories.get(0);
        }
        //二级分类
        List<Filter> filters=new ArrayList<Filter>();
		filters.add(Filter.eq("supplierId", CommonAttributes.JD_SUPPLY_ID));
		filters.add(Filter.eq("parent.id", cateId));
		filters.add(Filter.eq("enabledFlag", true));
		filters.add(Filter.eq("seoKeywords", productCategory.getId()));
		List<Order> orders=new ArrayList<Order>();
		orders.add(Order.asc("order"));
		List<ProductCategory> productCategories1 = productCategoryService.findList(null, filters, orders);
		productCategory.setChildren(new HashSet<ProductCategory>(productCategories1));
		
		if (!company.getCategoryAll()) { // 如果非所有商品分类开发查询相应有权限分类 做in查询
			// 动态获取公司下分类
			List<Long> companyCategory = productCategoryService.findCompanyCategory(company);

			// 二级分类企业个性化
			productCategory.getChildren().clear();
			productCategory.setChildren(new HashSet<ProductCategory>(
					productCategoryService.findCompanyCategoryChildren(
							productCategory,null, companyCategory)));

			// 三级分类企业个性化
			List<ProductCategory> thirdProductCategoryList = new ArrayList<>();
			for (ProductCategory twoProductCategory : productCategory.getChildren()) {
				List<ProductCategory> companyChildrenThird = productCategoryService.findCompanyCategoryChildren(twoProductCategory, null, companyCategory);
				thirdProductCategoryList.addAll(companyChildrenThird);
			}
			model.addAttribute("thirdProductCategoryList", thirdProductCategoryList);
		}
		
		model.addAttribute("productCategoryp", productCategory);//获取一级分类
		model.addAttribute("productCategories", productCategories);
		model.addAttribute("member", member);
		model.addAttribute("supplierId", CommonAttributes.JD_SUPPLY_ID);
		return "/wechat/product/industrial_cate_list";

	}
	
	
	/**
	 *方法:异步获取工业品分类列表
	 *作者:sangyj
	 * @param productCategoryId
	 * @param session
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/industrialCatesAsyn", method = {RequestMethod.GET, RequestMethod.POST})
	public Map<String, Object> industrialCatesAsyn(Long productCategoryId,HttpSession session) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Long cateId = CommonAttributes.INDUSTRIAL_CATEGORY_ID;
		ProductCategory productCategory = productCategoryService.find(productCategoryId);
		if (productCategory == null) {
			throw new ResourceNotFoundException();
		}
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		//二级分类
        List<Filter> filters=new ArrayList<Filter>();
		filters.add(Filter.eq("supplierId", CommonAttributes.JD_SUPPLY_ID));
		filters.add(Filter.eq("parent.id", cateId));
		filters.add(Filter.eq("enabledFlag", true));
		filters.add(Filter.eq("seoKeywords", productCategoryId));
		List<Order> orders=new ArrayList<Order>();
		orders.add(Order.asc("order"));
		List<ProductCategory> productCategories1 = productCategoryService.findList(null, filters, orders);
		
		if (!company.getCategoryAll()){ // 如果非所有商品分类开发查询相应有权限分类 做in查询
			// 动态获取公司下分类
			List<Long> companyCategory = productCategoryService.findCompanyCategory(company);

			// 二级分类
			productCategory.setChildren(new HashSet<ProductCategory>(
					productCategoryService.findCompanyCategoryChildren(
							productCategory, null, companyCategory)));
			List<ProductCategory> thirdProductCategoryList = new ArrayList<>();
			// 三级分类
			for (ProductCategory twoProductCategory : productCategory.getChildren()) {
				List<ProductCategory> companyChildrenThird = productCategoryService.findCompanyCategoryChildren(twoProductCategory, null, companyCategory);
				thirdProductCategoryList.addAll(companyChildrenThird);
			}
			resultMap.put("productCategoryThird", thirdProductCategoryList);
		}
		resultMap.put("productCategoryTwo", productCategories1);

		return resultMap;
	}


	/**
	 * <AUTHOR>
	 * @Description 异步获取分类列表
	 * @Date 2022/3/17 9:15
	 * @param productCategoryId
	 * @param session
	 * @return java.lang.Object
	 **/
	@ResponseBody
	@RequestMapping(value = "/catelistAsyn", method = {RequestMethod.GET, RequestMethod.POST})
	public Map<String, Object> catelistAsyn(Long productCategoryId,HttpSession session) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		ProductCategory productCategory = productCategoryService.find(productCategoryId);
		if (productCategory == null) {
			throw new ResourceNotFoundException();
		}
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}

		if (!company.getCategoryAll()) { // 如果非所有商品分类开发查询相应有权限分类 做in查询
			// 动态获取公司下分类
			List<Long> companyCategory = productCategoryService.findCompanyCategory(company);

			// 二级分类
			productCategory.getChildren().clear();
			productCategory.setChildren(new HashSet<ProductCategory>(
					productCategoryService.findCompanyCategoryChildren(
							productCategory, null, companyCategory)));
			List<ProductCategory> thirdProductCategoryList = new ArrayList<>();
			// 三级分类
			for (ProductCategory twoProductCategory : productCategory.getChildren()) {
				List<ProductCategory> companyChildrenThird = productCategoryService.findCompanyCategoryChildren(twoProductCategory, null, companyCategory);
				thirdProductCategoryList.addAll(companyChildrenThird);
			}
			resultMap.put("productCategoryThird", thirdProductCategoryList);
		}
		resultMap.put("productCategoryTwo", productCategory.getChildren());

		return resultMap;
	}

	/**
	 * 方法： 商品列表 实现流程： 1.根据商会员所在企业权限获取相应分类权限和供应商权限 2.根据平台促销标签等信息获取查询结果 autor
	 * :penglong
	 *
	 * @param productCategoryId
	 *            商品分类
	 * @param brandId
	 *            品牌id
	 * @param promotionId
	 *            促销id
	 * @param tagIds
	 *            标签id
	 * @param startPrice
	 *            最低价
	 * @param endPrice
	 *            最高价
	 * @param orderType
	 *            排序类型
	 * @param pageNumber
	 *            第几页
	 * @param pageSize
	 *            每页大小
	 * @param attributeValue5
	 * 			  5-精品商城商品
	 * @param attributeValue11 (同程配送、今日达、次日达)蛋糕叔叔商品标签
	 * @param sourceType dangaoss2--从蛋糕商城跳转而来
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/list/{productCategoryId}", method = {RequestMethod.GET, RequestMethod.POST})
	public String list(@PathVariable Long productCategoryId, Long brandId,Long searchProductCategoryId,Long freshProductCategoryId,
					   Long promotionId, Long[] tagIds, BigDecimal startPrice, String attributeValue5,String attributeValue11,
					   BigDecimal endPrice, OrderType orderType, Integer pageNumber, String specificationTag,
					   Integer pageSize, HttpServletRequest request, ModelMap model,HttpSession session,String sourceType) {
		ProductCategory productCategory = productCategoryService.find(productCategoryId);
		if (productCategory == null) {
			throw new ResourceNotFoundException();
		}

		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		if (!company.getCategoryAll()) { // 如果非所有商品分类开发查询相应有权限分类 做in查询
			// 动态获取公司下分类
			List<Long> companyCategory = productCategoryService.findCompanyCategory(company);

			productCategory.getChildren().clear();
			productCategory.setChildren(new HashSet<ProductCategory>(
					productCategoryService.findCompanyCategoryChildren(
							productCategory, null, companyCategory)));
		}

		/*
		 * add by hzq
		 * 搜索增加供应商
		 */
		if(productCategory.getSupplierId()!=null ){
			// 自营商城和怡亚通共用自营商城分类
			SysCode supplierSc=sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_SUPPLIER_IDS_KEY);
			Boolean isSelf = Arrays.asList(supplierSc.getValue().split(",")).contains(String.valueOf(productCategory.getSupplierId()));
			if(isSelf && company.getCategoryAll()){
				model.addAttribute("supplierId", supplierSc.getValue());
			}else{
				model.addAttribute("supplierId", productCategory.getSupplierId());
			}
		}
		String cityId = "";
		List<Brand> brandList = null;
		//蛋糕叔叔商品列表
		if(productCategory.getSupplierId()!=null&&productCategory.getSupplierId()==CommonAttributes.DANGAOSS2_SUPPLIERID.longValue()) {
			// 获取城市Id
			cityId = areaService.getCurrentCityId(false);
			//获取指定分类下分类下及城市内所有品牌
			if (productCategory.getGrade() == 1) {
				// 二级分类所有蛋糕品牌
				brandList = brandService.findBrandByCategoryIdAndCityId(cityId, productCategory.getId(), null, null);
			} else if (productCategory.getGrade() == 2) {
				// 三级分类下品牌
				brandList = brandService.findBrandByCategoryIdAndCityId(cityId, null, productCategory.getId(), null);
			}
		}else {
			//获取该分类下的品牌
			Set<Brand> brands = new HashSet<>();
			if(!productCategory.getChildren().isEmpty()){//如果有子分类
				for (ProductCategory category : productCategory.getChildren()) {
					if(!category.getChildren().isEmpty()){//如果有子分类
						for (ProductCategory child : category.getChildren()) {
							brands.addAll(child.getBrands());
						}
					}
					brands.addAll(category.getBrands());
				}
			}else {
				brands.addAll(productCategory.getBrands());
			}
			brandList = new ArrayList<>(brands);
		}
		model.addAttribute("brandList",brandList);
		model.addAttribute("supplier", supplierService.find(productCategory.getSupplierId()));
		model.addAttribute("orderTypes", OrderType.values());
		model.addAttribute("freshProductCategoryId", freshProductCategoryId);
		model.addAttribute("productCategory", productCategory);
		model.addAttribute("formURL", "/list/" + productCategoryId);
		model.addAttribute("startPrice", startPrice);
		model.addAttribute("attributeValue5", attributeValue5);
		model.addAttribute("endPrice", endPrice);
		model.addAttribute("orderType", orderType);
		model.addAttribute("pageNumber", pageNumber);
		model.addAttribute("searchProductCategoryId", searchProductCategoryId);
		model.addAttribute("brandId",brandId);
		model.addAttribute("pageSize", pageSize);
		model.addAttribute("specificationTag", specificationTag);
		model.addAttribute("attributeValue11", attributeValue11);
		model.addAttribute("sourceType", sourceType);
		if(productCategory!=null){
			if( productCategory.getParent()!=null){
				model.addAttribute("productCategoryTwo", productCategory.getParent());
				model.addAttribute("productCategoryOne",productCategory.getParent().getParent());
			}
		}
		model.addAttribute("member", member);
		//蛋糕叔叔商品列表
		if((productCategory!=null && productCategory.getSupplierId()!=null && productCategory.getSupplierId()==CommonAttributes.DANGAOSS2_SUPPLIERID.longValue()) ||
				(CommonAttributes.DANGAOSS2_BIZ_TYPE.equals(sourceType))) {
			if (member!=null){
				//获取用户公司是否开启兑换模式 用户是否存在蛋糕兑换券
				CouponCodeCountVO countVo = couponCodeService.countBySupplier(member.getCompanyId().getId(),member.getId(), Coupon.CONVERSION_COUPON_SN, CommonAttributes.DANGAOSS2_SUPPLIERID);
				model.addAttribute("couponCountVo", countVo);
				// 校验用户是否配置生日汇模块
				String dangaoModuleFlag = dangaoss2Service.checkDangaoModule(member);
				model.addAttribute("dangaoModuleFlag", dangaoModuleFlag);
				return "/wechat/dangaoss2/list";
			}else {
				return "redirect:/login.jhtml";	
			}
		}
		return "/wechat/product/list";

	}


	/**
	 * 方法： 商品列表 实现流程： 1.根据商会员所在企业权限获取相应分类权限和供应商权限 2.根据平台促销标签等信息获取查询结果 autor
	 * :penglong
	 *
	 * @param productCategoryId
	 *            商品分类
	 * @param brandId
	 *            品牌id
	 * @param promotionId
	 *            促销id
	 * @param tagIds
	 *            标签id
	 * @param startPrice
	 *            最低价
	 * @param endPrice
	 *            最高价
	 * @param orderType
	 *            排序类型
	 * @param pageNumber
	 *            第几页
	 * @param pageSize
	 *            每页大小
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/multiList", method = {RequestMethod.GET, RequestMethod.POST})
	public String multiList( Long productCategoryId, Long brandId,
							 Long promotionId, Long[] tagIds, BigDecimal startPrice,
							 BigDecimal endPrice, OrderType orderType, Integer pageNumber,
							 Integer pageSize, HttpServletRequest request,Long supplierId,String multiInfo, ModelMap model) {

		String [] multiArr = multiInfo.split(";");
		List<Supplier> supplierList = new ArrayList<Supplier>();
		List<ProductCategory> categoryList = new ArrayList<ProductCategory>();
		List<ProductCategory> oldCategoryList = new ArrayList<ProductCategory>();
		Long sId = 0L;
		for(String multiStr : multiArr){
			String [] infoArr = multiStr.split(",");
			Supplier itemSupplier = new Supplier();
			for(String item : infoArr){
				String [] temp = item.split("=");
				if("supplier".equals(temp[0])){
					sId = Long.parseLong(temp[1].split("/")[0]);
					supplierList.add(supplierService.find(sId));
				}else if("category".equals(temp[0]) ){
					for(String catId : temp[1].split("/")){
						categoryList.add(productCategoryService.find(Long.parseLong(catId)));
					}
				}

			}
		}

		oldCategoryList.addAll(categoryList);

		Boolean firstFlag = false;

		//List<Supplier> suppliers = new ArrayList<Supplier>();

		if( supplierId ==null){
			supplierId = supplierList.get(0).getId();
			//suppliers.add(supplierList.get(0));
		}
		if(productCategoryId == null){
			firstFlag = true;
			for(ProductCategory temp : categoryList){
				if(temp.getSupplierId() == supplierId){
					productCategoryId = temp.getId();
					break;
				}
			}

		}

		ProductCategory productCategory = productCategoryService
				.find(productCategoryId);
		if (productCategory == null) {
			throw new ResourceNotFoundException();
		}

		if(!firstFlag){
			//list：1.当前供应商子分类  2.其他供应商配置分类
			/*for(ProductCategory pc : categoryList){
				if(pc.getSupplierId() == supplierId){
					categoryList.remove(pc);
				}
			}*/
			categoryList.removeAll(categoryList);
			categoryList.addAll(productCategory.getChildren());
		}else{
			//过滤供应商
			for(ProductCategory pc : oldCategoryList){
				if(pc.getSupplierId() != supplierId){
					categoryList.remove(pc);
				}
			}
		}

		/*
		 * add by hzq
		 * 搜索增加供应商
		 */
		model.addAttribute("supplierId", productCategory.getSupplierId());
		model.addAttribute("orderTypes", OrderType.values());
		model.addAttribute("productCategory", productCategory);
		model.addAttribute("formURL", "/list/" + productCategoryId);
		model.addAttribute("startPrice", startPrice);
		model.addAttribute("endPrice", endPrice);
		model.addAttribute("orderType", orderType);
		model.addAttribute("pageNumber", pageNumber);
		model.addAttribute("pageSize", pageSize);

		model.addAttribute("supplierList", supplierList);
		model.addAttribute("categoryList", categoryList);
		model.addAttribute("multiInfo", multiInfo);
		model.addAttribute("brandId", brandId);
		model.addAttribute("oldCategoryList", oldCategoryList);

		if(productCategory!=null){
			if( productCategory.getParent()!=null){
				model.addAttribute("productCategoryTwo", productCategory.getParent());
				model.addAttribute("productCategoryOne",productCategory.getParent().getParent());
			}
		}
		Member member = memberService.getCurrent();
		model.addAttribute("member", member);
		return "/wechat/product/multi_list";

	}

	/**
	 * 方法:异步分类分页 实现流程: 1. autor :彭龙
	 *
	 * @param productCategoryId
	 * @param brandId
	 * @param promotionId
	 * @param tagIds
	 * @param startPrice
	 * @param endPrice
	 * @param orderType
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/multiListpage", method = { RequestMethod.GET,
			RequestMethod.POST })
	public String multiListpage(Long productCategoryId, Long brandId,
								Long promotionId, Long[] tagIds, BigDecimal startPrice,
								BigDecimal endPrice, OrderType orderType, Pageable pageable,
								HttpServletRequest request,Long supplierId,String multiInfo, ModelMap model,HttpSession session) {
		ProductCategory productCategory = productCategoryService
				.find(productCategoryId);
		if (productCategory == null) {
			throw new ResourceNotFoundException();
		}
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		List<Supplier> suppliers = new ArrayList<Supplier>();
		suppliers.add(supplierService.find(supplierId));

		model.addAttribute("page", productService.findPage(productCategory,
				brandService.find(brandId), null, suppliers, null, null, "3",
				startPrice,endPrice, true, true, null, false, orderType,
				pageable,company,true, null, null));
		model.addAttribute("member", member);
		model.addAttribute("company", company);
		return "/wechat/product/list_page";
	}

	/**
	 * 方法:异步分类分页 实现流程: 1. autor :彭龙
	 *
	 * @param productCategoryId
	 * @param brandId
	 * @param promotionId
	 * @param tagIds
	 * @param startPrice
	 * @param endPrice
	 * @param orderType
	 * @param request
	 * @param model
	 * @param attributeValue5 5-精选商城商品
	 * @return
	 */
	@RequestMapping(value = "/listpage", method = { RequestMethod.GET,
			RequestMethod.POST })
	public String listpage(Long productCategoryId, Long brandId, String specificationTag, String attributeValue5,
						   Long promotionId, Long[] tagIds, BigDecimal startPrice,String attributeValue11,
						   BigDecimal endPrice, OrderType orderType, Pageable pageable,
						   HttpServletRequest request, ModelMap model) {

		ProductCategory productCategory = productCategoryService.find(productCategoryId);
		if (productCategory == null) {
			throw new ResourceNotFoundException();
		}
		List<Brand> brandList = null;
		String cityId = null;
		//蛋糕叔叔商品列表
		if(productCategory.getSupplierId()==CommonAttributes.DANGAOSS2_SUPPLIERID.longValue()) {
			// 获取城市Id
			cityId = areaService.getCurrentCityId(false);
			//获取指定分类下分类下及城市内所有品牌
			if (productCategory.getGrade() == 1) {
				// 二级分类所有蛋糕品牌
				brandList = brandService.findBrandByCategoryIdAndCityId(cityId, productCategory.getId(), null, null);
			} else if (productCategory.getGrade() == 2) {
				// 三级分类下品牌
				brandList = brandService.findBrandByCategoryIdAndCityId(cityId, null, productCategory.getId(), null);
			}
		}else {
			//获取该分类下的品牌
			Set<Brand> brands = new HashSet<>();
			if(!productCategory.getChildren().isEmpty()){//如果有子分类
				for (ProductCategory category : productCategory.getChildren()) {
					if(!category.getChildren().isEmpty()){//如果有子分类
						for (ProductCategory child : category.getChildren()) {
							brands.addAll(child.getBrands());
						}
					}
					brands.addAll(category.getBrands());
				}
			}else {
				brands.addAll(productCategory.getBrands());
			}
			brandList = new ArrayList<>(brands);
		}
		model.addAttribute("brandList",brandList);
		model.addAttribute("brandId",brandId);

		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			HttpSession session=request.getSession();
			String companyIdStr=(String) session.getAttribute("companyIdStr");
			if(companyIdStr!=null){
				Long companyId=Long.valueOf(companyIdStr);
				company=companyService.find(companyId);
				if (company != null) {
					logger.info("companyId="+company.getId());
				}
			}else{
				company = companyService.getDefultCompany();
			}
		}
		List<Supplier> suppliers = new ArrayList<Supplier>();
		if (!company.getSupplierAll()) { // 如果非所有供应商开放获取相应有权限供应商 做in查询
			suppliers = supplierService.findCompanySupplier(company.getId());
			//suppliers = new ArrayList<Supplier>(company.getSuppliers());
		}
		if (!company.getCategoryAll()) { // 如果非所有商品分类开发查询相应有权限分类 做in查询
			// 动态获取公司下分类
			List<Long> companyCategory = productCategoryService.findCompanyCategory(company);

			productCategory.getChildren().clear();
			productCategory.setChildren(new HashSet<ProductCategory>(
					productCategoryService.findCompanyCategoryChildren(
							productCategory, null, companyCategory)));
		}
		if (orderType == null) {
			//京东商品默认不排序
			if(productCategory.getSupplierId()==null ||
					!(productCategory.getSupplierId()==68
							|| productCategory.getSupplierId()==181)){
				orderType = Product.OrderType.tagDesc;// 默认综合排序
			}
		}
		Supplier supplier = null;
		if(productCategory.getSupplierId()!=null ){
			// 自营商城和怡亚通共用自营商城分类
			SysCode supplierSc=sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_SUPPLIER_IDS_KEY);
			String[] supplierArr = supplierSc.getValue().split(",");
			Boolean isSelf = Arrays.asList(supplierArr).contains(String.valueOf(productCategory.getSupplierId()));
			if(isSelf && company.getCategoryAll()){
				Long [] supplierIds = new Long[supplierArr.length];
				for(int i=0;i<supplierArr.length;i++) {
					supplierIds[i]=Long.parseLong(supplierArr[i]);
				}
				suppliers = supplierService.findList(supplierIds);
			}else{
				supplier = supplierService.find(productCategory.getSupplierId());
			}
		}

		Brand brand = null;
		if(brandId!=null){
			brand = brandService.find(brandId);
		}
		//获取区域id,用于区域限制筛选
		Area area = null;
		if(cityId!=null) {
			area = areaService.find(Long.valueOf(cityId));
		}

		model.addAttribute("page", productService.findPage(productCategory,
				brand, supplier, suppliers, null, null, "3", startPrice,
				endPrice, true, true, null, false, orderType,
				pageable,company,true, null ,area, specificationTag,attributeValue5, attributeValue11));

		//如果是第一层，则查询改分类下商品关联的所有brand
//		if(productCategory.getGrade() == 0 && productCategory.getSupplierId() != CommonAttributes.DANGAOSS2_SUPPLIERID.longValue()) {
//			List<Long> brandIds = productService.findBrandIdsByCategory(productCategoryId, supplier);
//			List<Brand> brands = brandService.findList(brandIds.toArray(new Long[brandIds.size()]));
//			model.addAttribute("brandList",brands);
//		}

		model.addAttribute("member", member);
		if(company!=null){
			//		model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		model.addAttribute("company",company);

		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if(!StringUtil.isEmpty(excludeSuppliers)){
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		if(productCategory.getSupplierId()==CommonAttributes.DANGAOSS2_SUPPLIERID.longValue()) {
			//获取用户公司是否开启兑换模式 用户是否存在蛋糕兑换券
			CouponCodeCountVO countVo = couponCodeService.countBySupplier(member.getCompanyId().getId(),member.getId(), Coupon.CONVERSION_COUPON_SN, CommonAttributes.DANGAOSS2_SUPPLIERID);
			model.addAttribute("couponCountVo", countVo);
			return "/wechat/dangaoss2/list_page";
		}
		return "/wechat/product/list_page";
	}

	/**
	 * 方法： 商品列表 autor :penglong
	 *
	 * @param brandId
	 * @param promotionId
	 * @param tagIds
	 * @param startPrice
	 * @param endPrice
	 * @param orderType
	 * @param pageNumber
	 * @param pageSize
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/list", method = {RequestMethod.GET, RequestMethod.POST})
	public String list(Long brandId, Long promotionId, Long[] tagIds,
					   BigDecimal startPrice, BigDecimal endPrice, OrderType orderType,
					   Integer pageNumber, Integer pageSize, HttpServletRequest request,
					   ModelMap model) {
		Company company = memberService.getCurrent().getCompanyId();
		List<Supplier> suppliers = new ArrayList<Supplier>();
		if (!company.getSupplierAll()) {
			suppliers = supplierService.findCompanySupplier(company.getId());
			//suppliers = new ArrayList<Supplier>(company.getSuppliers());
		}
		Brand brand = brandService.find(brandId);
		Promotion promotion = promotionService.find(promotionId);
		List<Tag> tags = tagService.findList(tagIds);
		Pageable pageable = new Pageable(pageNumber, pageSize);
		model.addAttribute("orderTypes", OrderType.values());
		model.addAttribute("brand", brand);
		model.addAttribute("promotion", promotion);
		model.addAttribute("tags", tags);
		model.addAttribute("startPrice", startPrice);
		model.addAttribute("endPrice", endPrice);
		model.addAttribute("orderType", orderType);
		model.addAttribute("pageNumber", pageNumber);
		model.addAttribute("pageSize", pageSize);
		model.addAttribute("formURL", "list");
		model.addAttribute("page", productService.findPage(null, brand, null,
				suppliers, promotion, tags, "3",startPrice, endPrice,
				true, true, null, false, orderType, pageable,company,true, null, null));
		Member member = memberService.getCurrent();
		model.addAttribute("member", member);
		model.addAttribute("company", company);
		return "/wechat/product/search";
	}

	/**
	 * 方法：跳转折扣页面 autor :huhui
	 *
	 * @param discountRate
	 * @param isProductCategory
	 *            是否从一级分类跳转
	 * @param startPrice
	 * @param endPrice
	 * @param orderType
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/discount", method = {RequestMethod.GET, RequestMethod.POST})
	public String discount(Long discountRate, Boolean isProductCategory,
						   BigDecimal startPrice, BigDecimal endPrice, OrderType orderType,
						   Long productCategoryId, ModelMap model,HttpSession session) {
		if (isProductCategory != null) {
			discountRate = null;
		}
		model.addAttribute("isProductCategory", isProductCategory);
		model.addAttribute("discountRate", discountRate);
		model.addAttribute("startPrice", startPrice);
		model.addAttribute("endPrice", endPrice);
		model.addAttribute("orderType", orderType);
		model.addAttribute("productCategoryId", productCategoryId);
		// 获取分类
		ProductCategory productCategory = null;
		if (productCategoryId == null) {
			Long[] CategoryIds = productService
					.findProductCategoryByDiscountRate(discountRate);
			List<ProductCategory> list = productCategoryService
					.findList(CategoryIds);
			model.addAttribute("productCategories", list);
		} else {
			productCategory = productCategoryService.find(productCategoryId);
			model.addAttribute("productCategories",
					productCategory.getChildren());
		}
		model.addAttribute("filterProductCategory", productCategory);
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		model.addAttribute("member", member);
		model.addAttribute("company",company);
		return "wechat/product/discount";
	}

	/**
	 * 方法:异步折扣商品分页 实现流程: 1. autor :huhui
	 *
	 * @param discountRate
	 * @param startPrice
	 * @param endPrice
	 * @param orderType
	 * @param pageNumber
	 * @param pageSize
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/discountPage", method = RequestMethod.POST)
	public String discountPage(Long discountRate, Boolean isProductCategory,
							   BigDecimal startPrice, BigDecimal endPrice, OrderType orderType,
							   Long productCategoryId, Integer pageNumber, Integer pageSize,
							   HttpServletRequest request, ModelMap model,HttpSession session) {
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		Supplier supplier = supplierService.find(68L);
		List<Supplier> suppliers = new ArrayList<Supplier>();
		suppliers.add(supplier);
		ProductCategory productCategory = productCategoryService
				.find(productCategoryId);
		Pageable pageable = new Pageable(pageNumber, pageSize);
		if (isProductCategory != null) {
			discountRate = null;
			pageable.getFilters().add(Filter.le("attributeValue14", 80));
		} else {
			if(discountRate !=null){
				if(discountRate == 3){
					pageable.getFilters().add(Filter.le("attributeValue14", 30));
				}else if(discountRate == 5){
					pageable.getFilters().add(Filter.ge("attributeValue14", 30));
					pageable.getFilters().add(Filter.le("attributeValue14", 50));
				}else if(discountRate == 8){
					pageable.getFilters().add(Filter.ge("attributeValue14", 50));
					pageable.getFilters().add(Filter.le("attributeValue14", 80));
				}
			}
		}
		List<Order> orders = pageable.getOrders();
		Page<Product> page = productService.findListByDiscountRate(
				discountRate, productCategory, null, null, suppliers, null,
				null, "3", startPrice, endPrice, true, true, null, false
				, orderType, pageable,company);
		model.addAttribute("page", page);
		if(company!=null){
			//		model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		model.addAttribute("company",company);
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if(!StringUtil.isEmpty(excludeSuppliers)){
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		return "wechat/product/discount_page";
	}



	/**
	 * 方法：商品搜索 实现流程： 1.获取该会员公司商品分类权限、供应商权限 2.调用hibernate search 索引搜索方法获取搜索结果
	 * autor :penglong
	 *
	 * @param keyword
	 * @param startPrice
	 * @param endPrice
	 * @param orderType
	 * @param pageNumber
	 * @param pageSize
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/search", method = {RequestMethod.GET,RequestMethod.POST})
	public String search(String keyword, BigDecimal startPrice, BigDecimal endPrice, OrderType orderType,
						 String productCategoryId, Integer pageNumber, Integer pageSize, String specificationTag,
						 ModelMap model, Boolean isDiscount,Long minDiscount,Long maxDiscount, String attributeValue5, String attributeValue11,String sourceType,
						 String supplierId,String productCategoryName,String brandName,Integer type,Long companyId,HttpSession session) {

		model.addAttribute("isDiscount", isDiscount);
		model.addAttribute("attributeValue5", attributeValue5);
		model.addAttribute("attributeValue11", attributeValue11);
		model.addAttribute("sourceType", sourceType);
		model.addAttribute("orderTypes", OrderType.values());
		if(keyword!=null){
			keyword=keyword.replaceAll("\\\\", " ");
		}
		logger.info("---search---  keyword="+keyword);
		model.addAttribute("productKeyword", keyword);
		model.addAttribute("startPrice", startPrice);
		model.addAttribute("endPrice", endPrice);
		model.addAttribute("orderType", orderType);
		model.addAttribute("formURL", "search");
		model.addAttribute("endPrice", endPrice);
		model.addAttribute("isDiscount", isDiscount);
		model.addAttribute("minDiscount", minDiscount);
		model.addAttribute("maxDiscount", maxDiscount);
		model.addAttribute("specificationTag", specificationTag);
		/*ProductCategory productCategory = productCategoryService
				.find(productCategoryId);

		Integer grade = null;
		if (productCategory != null) {
			grade = productCategory.getGrade();
		}*/
		/*Page<Product> page = searchService.search(keyword, startPrice,
				endPrice, orderType, null, null, productCategoryId, grade,
				null, isDiscount,minDiscount,maxDiscount,supplierId);*/
		keyword=StringEscapeUtils.unescapeHtml(keyword);
		//keyword=ClientUtils.escapeQueryChars(keyword);
		if(productCategoryName!=null){
			productCategoryName=StringEscapeUtils.unescapeHtml(productCategoryName);
		}
		if(brandName!=null){
			brandName=StringEscapeUtils.unescapeHtml(brandName);
		}


		/*Page<Product> page = searchService.searchBySolr(keyword, startPrice, endPrice, orderType, null, null, productCategoryId, grade, null,
				isDiscount, minDiscount, maxDiscount, supplierId,productCategoryName, brandName, type, pageNumber, pageSize);
		 */
		//---每日优鲜商品过滤城市
		String cityId=null;
		if(supplierId!=null&&supplierId.indexOf(",")==-1&&supplierService.exists(Filter.eq("id", Long.parseLong(supplierId)),Filter.eq("isOweOrder", 13))){
			cityId=areaService.getCurrentCityId(true);
		}
		/*String attributeValue5=null;
		if (companyId != null && companyId==CommonAttributes.ZAN_MU_SHI_COMPANY_ID) {
			attributeValue5="1";
		}*/
		//Page<Product> page =searchService.searchByOpenSearch(keyword, startPrice, endPrice, orderType, new Pageable(pageNumber, pageSize), isDiscount, supplierId, productCategoryName, productCategoryId,brandName, minDiscount, maxDiscount,cityId,null);


		// 返回分类
		/*		List<ProductCategory> productCatelist = new ArrayList<ProductCategory>();
		productCatelist = productCategoryService.findList(page.getFacets());

		model.addAttribute("brandNames", page.getBrandNames());
		model.addAttribute("productCategories", productCatelist);*/
		//model.addAttribute("productCategories", page.getData().get("productCategoryNames"));
		//model.addAttribute("brandNames", page.getData().get("brandNames"));
		/*	model.addAttribute("filterProductCategory", productCategory);

		if (productCategory != null) {
			if( productCategory.getParent()!=null){
				model.addAttribute("productCategoryTwo", productCategory.getParent());
				model.addAttribute("productCategoryOne",productCategory.getParent().getParent());
			}

		}*/

		model.addAttribute("supplierId", supplierId);
		model.addAttribute("productCategoryId", productCategoryId);
		model.addAttribute("productCategoryName", productCategoryName);
		model.addAttribute("brandName", brandName);
		model.addAttribute("type", type);
		Member member = memberService.getCurrent();
		model.addAttribute("member", member);
		Company company=null;
		if(member !=null){
			company=member.getCompanyId();
			model.addAttribute("companyId", company.getId());
		}else{
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		model.addAttribute("company", company);

		//蛋糕叔叔商品列表
		if(((supplierId!=null&&supplierId.indexOf(",")==-1
				&& Long.parseLong(supplierId) == CommonAttributes.DANGAOSS2_SUPPLIERID.longValue())||
				(CommonAttributes.DANGAOSS2_BIZ_TYPE.equals(sourceType)))) {
			if (member!=null){
				//获取用户公司是否开启兑换模式 用户是否存在蛋糕兑换券
				CouponCodeCountVO countVo = couponCodeService.countBySupplier(member.getCompanyId().getId(),member.getId(), Coupon.CONVERSION_COUPON_SN, CommonAttributes.DANGAOSS2_SUPPLIERID);
				model.addAttribute("couponCountVo", countVo);
				// 校验用户是否配置生日汇模块
				String dangaoModuleFlag = dangaoss2Service.checkDangaoModule(member);
				model.addAttribute("dangaoModuleFlag", dangaoModuleFlag);
				return "/wechat/dangaoss2/search";
			}else {
				return "redirect:/login.jhtml";
			}
		}
		return "wechat/product/search";
	}


	/**
	 * 方法:异步搜索分页 实现流程: 1. autor :彭龙
	 *
	 * @param keyword
	 * @param startPrice
	 * @param endPrice
	 * @param orderType
	 * @param pageable
	 * @param model
	 * @param attributeValue5 用于筛选精品商城的商品
	 * @return
	 */
	@RequestMapping(value = "/searchPage", method = {RequestMethod.GET,RequestMethod.POST})
	public String searchPage(String keyword, BigDecimal startPrice, String specificationTag,
							 BigDecimal endPrice, OrderType orderType, String productCategoryId,
							 Pageable pageable, HttpServletRequest request, ModelMap model,
							 Boolean isDiscount,Long minDiscount,Long maxDiscount,String supplierId, String attributeValue5,String attributeValue11,
							 String productCategoryName,String brandName,Integer type,Long companyId,HttpSession session) {
		if (StringUtils.isEmpty(keyword)) {
			return ERROR_VIEW;
		}
		model.addAttribute("brandName", brandName);
		model.addAttribute("productCategoryName", productCategoryName);
		/*	List<Supplier> suppliers = new ArrayList<Supplier>();
		List<ProductCategory> categories = new ArrayList<ProductCategory>();
		Company company = null;*/
		Member member = memberService.getCurrent();
		logger.info("---searchPage---  keyword="+keyword);
		/*if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			company = companyService.getDefultCompany();
		}
		if (!company.getSupplierAll()) {
			suppliers = new ArrayList<Supplier>(company.getSuppliers());
		}
		if (!company.getCategoryAll()) {
			categories = new ArrayList<ProductCategory>(
					company.getProductCategories());
		}

		ProductCategory productCategory = productCategoryService
				.find(productCategoryId);
		Integer grade = null;
		if (productCategory != null) {
			grade = productCategory.getGrade();
		}*/
		/*Page<Product> page = searchService.search(keyword, startPrice,
				endPrice, orderType, suppliers, categories, productCategoryId,
				grade, pageable, isDiscount,minDiscount,maxDiscount,supplierId);*/

		keyword=StringEscapeUtils.unescapeHtml(keyword);
		//keyword=ClientUtils.escapeQueryChars(keyword);
		if(productCategoryName!=null){
			productCategoryName=StringEscapeUtils.unescapeHtml(productCategoryName);
		}
		if(brandName!=null){
			brandName=StringEscapeUtils.unescapeHtml(brandName);
		}

		/*Page<Product> page = searchService.searchBySolr(keyword, startPrice, endPrice, orderType, suppliers, categories, productCategoryId, grade, pageable,
				isDiscount, minDiscount, maxDiscount, supplierId,productCategoryName, brandName, type, pageable.getPageNumber(), pageable.getPageSize());
		 */
		//---每日优鲜商品过滤城市
		String cityId=null;
		if(supplierId!=null&&supplierId.indexOf(",")==-1&&supplierService.exists(Filter.eq("id", Long.parseLong(supplierId)),Filter.eq("isOweOrder", 13))){
			cityId=areaService.getCurrentCityId(true);
		}

		if (supplierId!=null&&supplierId.indexOf(",")==-1 && Long.parseLong(supplierId) == CommonAttributes.DANGAOSS2_SUPPLIERID.longValue()) {
			cityId = areaService.getCurrentCityId(false);
		}

		Company company=null;
		if(member !=null){
			company=member.getCompanyId();
		}else{
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		if(company!=null){
			//		model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		/*        if(company.getCategoryAll()!=null && !company.getCategoryAll()){
            List<ProductCategory>categories = new ArrayList<ProductCategory>(
                    company.getProductCategories());
            if(CollectionUtils.isNotEmpty(categories)){
                if(StringUtils.isEmpty(productCategoryId)){
                    productCategoryId="";
                }
                for(int i=0;i<categories.size();i++){
                    productCategoryId+=categories.get(i).getId()+",";
                }
                productCategoryId=productCategoryId.substring(0,productCategoryId.length()-1);
            }
        }*/
		Page<Product> page =searchService.searchByOpenSearch(keyword, startPrice, endPrice, orderType, pageable, isDiscount, supplierId, productCategoryName,productCategoryId, brandName, minDiscount, maxDiscount,cityId,attributeValue11,company, specificationTag, attributeValue5,null);

		// 返回分类
		/*List<ProductCategory> productCatelist = new ArrayList<ProductCategory>();
		productCatelist = productCategoryService.findList(page.getFacets());*/

		// 新增搜索日志记录
		if (page.getPageNumber() == 1) {

			try {
				LogSearchMsgBean logSearchMsgBean = new LogSearchMsgBean();
				if (member != null) {
					logSearchMsgBean.setMemberId(member.getId());
				}
				logSearchMsgBean.setKeyword(keyword);
				logSearchMsgBean.setDeviceType(1);
				logSearchMsgBean.setTotal(page.getTotal());
				logSearchMsgBean.setIp(CommUtil.getIpAddr(request));
				logSearchMsgBean.setCreateDate(new Date());

				/*producerService
						.sendMessage(destinationSearch, logSearchMsgBean);// 发送消息
				 */			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		// 搜索日志记录完成

		model.addAttribute("page", page);
		model.addAttribute("productCategories", page.getData().get("productCategoryNames"));
		model.addAttribute("brandNames", page.getData().get("brandNames"));
		/*model.addAttribute("filterProductCategory", productCategory);*/
		model.addAttribute("company", company);
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if(!StringUtil.isEmpty(excludeSuppliers)){
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}

		//蛋糕叔叔商品列表
		if(supplierId!=null&&supplierId.indexOf(",")==-1
				&& Long.parseLong(supplierId) == CommonAttributes.DANGAOSS2_SUPPLIERID.longValue()) {
			//获取用户公司是否开启兑换模式 用户是否存在蛋糕兑换券
			CouponCodeCountVO countVo = couponCodeService.countBySupplier(member.getCompanyId().getId(),member.getId(), Coupon.CONVERSION_COUPON_SN, CommonAttributes.DANGAOSS2_SUPPLIERID);
			model.addAttribute("couponCountVo", countVo);
			return "/wechat/dangaoss2/search_page";
		}
		return "wechat/product/search_page";
	}

	/**
	 * 方法:异步获取商品点击量 autor :penglong
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/hits/{id}", method = {RequestMethod.GET, RequestMethod.POST})
	public @ResponseBody Long hits(@PathVariable Long id,
								   HttpServletRequest request) {
		// 新增 20170317 记录登录日志 发送MQ异步进行保存操作
		Member member = memberService.getCurrent();

		try {
			LogProductMsgBean logProductMsgBean = new LogProductMsgBean();
			if (member != null) {
				logProductMsgBean.setCompanyId(member.getCompanyId().getId());
				logProductMsgBean.setMemberId(member.getId());
			}
			logProductMsgBean.setHitIp(CommUtil.getIpAddr(request));
			logProductMsgBean.setHitType(1);
			logProductMsgBean.setProductId(id);
			logProductMsgBean.setCreateDate(new Date());
			producerService.sendMessage(destination, logProductMsgBean);// 发送消息
		} catch (Exception e) {
			e.printStackTrace();
		}

		// 异步发送登录日志结束

		return 1L;
	}

	/**
	 * 方法:商品详情页 实现流程： 1.获取该商品静态路径判断是否存在，存在直接返回静态页面 2.不存在则查询商品信息返回动态页面 autor
	 * :penglong
	 *
	 * @param id
	 * @param sn
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/content/{sn}/{id}", method = {RequestMethod.GET, RequestMethod.POST})
	public String content(@PathVariable Long id, @PathVariable Long sn, String inviter, String enterCode, String mid,
						  ModelMap model,HttpServletRequest request, HttpServletResponse response,Long actId,Long referrer, Long companyId ) {
		Company company=null;
		Product product = productService.find(id);
		if (product == null) {
			throw new ResourceNotFoundException();
		}
		model.addAttribute("product", product);

		Member member = memberService.getCurrent();
		model.addAttribute("member", member);
		HttpSession session=request.getSession();
		if(member!=null){
			company=member.getCompanyId();
			model.addAttribute("companyId", company.getId());
			try {
				String memberId = URLEncoder.encode(AesUtil.encode(String.valueOf(member.getId())),"UTF-8");
				model.addAttribute("memberId", memberId);
			} catch (UnsupportedEncodingException e) {
				logger.error("memberId编码失败",e);
			}
			//判断是否是集采账号
			if(purchaseListService.getPurchaseFlagSession(request)){
				if (!purchaseListService.isPurchaseAccount(member.getId())){
					request.getSession().setAttribute(CommonAttributes.PURCHASE_MEMBER, false);
				}else {
					//是否有清单
					List<Filter> filters=new ArrayList<Filter>();
					filters.add(Filter.ne("status",-1));
					filters.add(Filter.eq("enabledFlag",true));
					filters.add(Filter.eq("createUser",member.getId()));
					List<PurchaseList> purchaseLists = purchaseListService.findList(null, filters, null);
					model.addAttribute("purchaseLists", purchaseLists);
				}
			}
		}else{
			String companyIdStr=(String) session.getAttribute("companyIdStr");
			if(StringUtils.isBlank(companyIdStr)) {
				companyIdStr= request.getParameter("companyIdStr");
			}
			logger.info("session中保存的companyIdStr="+companyIdStr);
			if(StringUtils.isNotEmpty(companyIdStr)){
				company=companyService.find(Long.valueOf(companyIdStr));
			} else {
				if(companyId != null){//带了企业id
					company = companyService.find(companyId);
					if(company != null){//获取到指定的企业
						companyIdStr = company.getId().toString();
						model.addAttribute("companyId", company.getId());
						model.addAttribute("company", company);
					}
				}
			}
			model.addAttribute("companyId", companyIdStr);
		}
		model.addAttribute("company", company);
		if(company!=null){
			//		model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}

		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if(!StringUtil.isEmpty(excludeSuppliers)){
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}

		Long memberId = -1L;
		if (member != null) {
			memberId = member.getId();
		}


		//发送mq同步商品价格
		if(product.getSupplierId()!=null && product.getSupplierId().getIsOweOrder()!=null){ //京东商品
			if(product.getSupplierId().getIsOweOrder()==1 || product.getSupplierId().getIsOweOrder()==18 || product.getSupplierId().getIsOweOrder() == 32
			|| product.getSupplierId().getIsOweOrder() == 2){
				try {
					jdFeedbackService.sendMq(product.getId(), memberId, 2);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}

		if(actId==null&&company!=null) {
			actId = actProductService.findConvertAct(product.getId(), company.getId());
			/*Set<ActProduct> actProducts = product.getActProducts();
			if (actProducts != null && actProducts.size() > 0) {
				for (ActProduct actProduct : actProducts) {
					if (actProduct.getAct().getType() == Act.ActType.convert) {
						if (actProduct.getActProductCompanyIds() != null && actProduct.getActProductCompanyIds().length() > 0) {
							String[] companyIds = actProduct.getActProductCompanyIds().split(",");
							if (Arrays.asList(companyIds).contains(company.getId().toString())) {
								//取满足条件的最大ID
								if(actId==null || (actId!=null && actProduct.getAct().getId()>actId)){
									actId = actProduct.getAct().getId();
								}
							}
						}
					}
				}
			}*/
		}
		List<Coupon> convertCoupons=couponService.findConvertCoupon(actId, member,product,1);
		if(CollectionUtils.isNotEmpty(convertCoupons)){
			model.put("convertCoupon",convertCoupons.get(0) );
		}
		if(actId!=null) {
			Act act = actService.find(actId);
			if(act!=null && act.getType()==ActType.convert) {
				model.put("convertFlag",true);
			}
		}
		model.put("actId",actId);
		if(!StringUtil.isEmpty(mid)) {//分享人
			try {
				model.put("mid", URLEncoder.encode(mid,"UTF-8"));
			} catch (UnsupportedEncodingException e) {
				logger.error("memberId编码失败",e);
			}
		}
		if(referrer!=null) {
			session.setAttribute(Constants.PRODUCT_REFERRER_KEY, referrer);
		}
		//将当前用户作为邀请人保存到session中
		try {
			if (inviter!=null){
				session.setAttribute("inviter",URLEncoder.encode(inviter, "UTF-8"));
			}
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		}

		//将当前用户的企业暗号保存到session中
		session.setAttribute("enterCode", enterCode);
		
		groupService.putGroupInfo(model, product, company);
		//小程序分享成功标识
		String share_success = request.getParameter("share_success");
		model.addAttribute("share_success", share_success);

		try{
			if(product.getSupplierId()!=null && product.getSupplierId().getIsOweOrder()!=null){ //京东商品
				if(product.getSupplierId().getIsOweOrder()==1 || product.getSupplierId().getIsOweOrder()==18){
					jdFeedbackService.sendMq(product.getId(), memberId,4);
				}
			}
		}catch (Exception e){
			e.printStackTrace();
		}
		// 查询商品促销活动
		model.addAttribute("defaultPromotion", promotionService.getPromotionByProduct(product.getId()));
		model.addAttribute("defaultGroup", groupService.getGroupByProduct(product.getId()));
		model.addAttribute("defaultAct", actService.getActByProduct(product.getId()));
		if (member!=null){
			//查询用户收货地址
			List<ReceiverVo> receivers = receiverService.findShippAddressByMember(member);
			model.addAttribute("receivers",receivers);
		}
		//判断供应商是否是蛋糕叔叔
		if(CommonAttributes.DANGAOSS2_SUPPLIERID.longValue()==product.getSupplierId().getId()){
			//获取cookie中城市ID
			String cityId = areaService.getCurrentCityId(false);
			ProductExpressRangeResponse expressRange = new ProductExpressRangeResponse();
			if(!cityId.equals("-1")) {
				//查询城市
				Area area = areaService.find(Long.valueOf(cityId));
				if(area!=null) {
					//调用蛋糕叔叔商品“配送范围接口”获取是否可配送
					expressRange = dangaoss2Service.getProductExpressRange(area,product);
					//dangaoss2Service.syncProductPrice(area, product);
				}
			}
			// 同步商品状态
			Boolean statusFlag = dangaoss2Service.syncProductStatus(product);
			if (statusFlag!=null && statusFlag){
				try {
					//消息发送给RDS，同步商品
					SolrUtil.sendMQMsg(2, product);
				} catch (Exception e) {
					logger.error("同步蛋糕叔叔商品:{}异常",id, e);
				}
			}
			
			model.addAttribute("productExpressRange", expressRange);
			ProductCategory productCategoryTwo = productCategoryService.findByName("蛋糕", CommonAttributes.DANGAOSS2_SUPPLIERID, 1);
			model.addAttribute("productCategoryTwo", productCategoryTwo);
			//获取用户公司是否开启兑换模式 用户是否存在蛋糕兑换券
			if (member!=null){
				CouponCodeCountVO countVo = couponCodeService.countBySupplier(member.getCompanyId().getId(),member.getId(), Coupon.CONVERSION_COUPON_SN, CommonAttributes.DANGAOSS2_SUPPLIERID);
				model.addAttribute("couponCountVo", countVo);

				// 校验用户是否配置生日汇模块
				String dangaoModuleFlag = dangaoss2Service.checkDangaoModule(member);
				model.addAttribute("dangaoModuleFlag", dangaoModuleFlag);
			}
			return "/wechat/dangaoss2/content";
		}
		return "/wechat/product/content";
	}

	/**
	 * 方法:异步获取商品评分信息 实现流程： 1.分别查询商品各分数评分数量 2.计算好评中评差评比例 autor :penglong
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/reviewCount", method = RequestMethod.POST)
	public @ResponseBody Map reviewCount(Long id) {
		Map<String, Float> map = new HashMap<String, Float>();
		Product product = productService.find(id);
		Long graph1 = reviewService.count(null, product, 1, true);
		Long graph2 = reviewService.count(null, product, 2, true);
		Long graph3 = reviewService.count(null, product, 3, true);
		Long graph4 = reviewService.count(null, product, 4, true);
		Long graph5 = reviewService.count(null, product, 5, true);
		Long total = reviewService.count(null, product, null, true);
		map.put("score2", (float) (Math.round(graph1.longValue() * 100)) / 100);
		map.put("score3", (float) (Math.round((graph2.longValue() + graph3
				.longValue()) * 100)) / 100);
		map.put("score5", (float) (Math.round((graph5.longValue() + graph4
				.longValue()) * 100)) / 100);
		;
		map.put("total", (float) (Math.round(total * 100)) / 100);
		float a = ((graph5.floatValue() + graph4.floatValue()) / total) * 100;
		map.put("scoregood", (float) (Math.round(a * 100)) / 100);

		return map;
	}

	/**
	 * 方法:异步刷新商品信息 实现流程： 1.根据用户切好城市id获取商品是否支持销售 autor :penglong
	 *
	 * @param id
	 * @param areaId
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/sale_area", method = RequestMethod.POST)
	public @ResponseBody Map<String, Object> saleArea(Long id, Long areaId,
					HttpServletRequest request, HttpServletResponse response) {
		Map<String, Object> data = new HashMap<String, Object>();
		Product product = productService.find(id);
		if (product != null && !product.getIsMarketable()) {
			data.put("message", Message.error("此商品已下架"));
			return data;
		}
		// 调用京东校验接口
		if (product.getSupplierId().getIsOweOrder() == 1) {
			if (jdService.checkProductsForList(product, areaId)) {
				data.put("message", Message.error("此地区缺货"));
				return data;
			}

			List<StockVO> stockVOs = new ArrayList<>();
			StockVO stockVO = new StockVO();
			stockVO.setSkuId(product.getJdSku());
			stockVO.setNum(1);
			stockVOs.add(stockVO);
			if (!jdService.checkAreaLimitForProductDetail(stockVOs, product, areaId)) { //校验区域购买限制
				data.put("message", Message.error("此地区缺货"));
				return data;
			}

		}
		// 调用优鲜校验接口
		/*if (product.getSupplierId().getIsOweOrder() == 13) {
			if (!missfreshService.isSaleArea(product, areaId)) {
				data.put("message", Message.error("此地区缺货"));
				return data;
			}
		}
		 * modify by hzq 20170915
		 * 添加行云供应商校验
		 */
		else if(product.getSupplierId().getIsOweOrder() == 2){
			if (!productService.checkStock(product,0)) {
				data.put("message", Message.error("此地区缺货"));
				return data;
			}
			if (!productService.isSaleArea(product, areaId)) {
				data.put("message", Message.error("暂不支持此地区销售"));
				return data;
			}
		}
		//蛋糕叔叔
		else if(product.getSupplierId().getIsOweOrder() == 47){
			if (!productService.isSaleArea(product, areaId)) {
				data.put("message", Message.error("当前地址不可配送"));
				return data;
			}

			Area cityArea = null;
			Member member = this.getCurrent();
			String addrId = null;// 收货地址id
			Integer dgCityID = null;
			if (member!=null){
				Map<String, String> addrIdAndCityId = dangaoss2Service.getAddrIdAndCityId(member);
				addrId = addrIdAndCityId.get("addrId");// 收货地址id
				//查询保存的Map集合获取蛋糕城市ID Map<我们的城市名称,蛋糕的城市id>
				dgCityID = Integer.valueOf(addrIdAndCityId.get("cityId"));
			}else {
				cityArea = areaService.find(areaId);
				dgCityID = dangaoss2Service.getDangaossCityIdByCityName(cityArea.getName());
			}
			if (!dangaoss2Service.isDeliveryProduct(product, dgCityID, addrId)){
				data.put("message", Message.error("当前地址不可配送"));
				return data;
			}
			
		}// 调用fliPlus校验规则
		else if (product.getSupplierId().getIsOweOrder() == 0) {
			if (!productService.checkStock(product,0)) {
				data.put("message", Message.error("缺货"));
				return data;
			}
			if (!productService.isSaleArea(product,areaId)) {
				data.put("message", Message.error("暂不支持此地区销售"));
				return data;
			}
		}
		// 京东健康调用库存查询接口
		else if(product.getSupplierId().getIsOweOrder()==61){
			Member member = getCurrent();
			Company company=null;
			if(member !=null){
				company=member.getCompanyId();
			}else{
				String companyIdStr = (String) request.getSession().getAttribute("companyIdStr");
				if (companyIdStr != null) {
					Long companyId = Long.valueOf(companyIdStr);
					company = companyService.find(companyId);
				}else{
					company = companyService.getDefultCompany();
				}
			}
			List<Product> list = new ArrayList<>();
			list.add(product);
			Map<Long, Boolean> map = jdHealthService.checkStock(company.getId(), list, areaId);
			if (map.get(product.getId())) {
				data.put("message", Message.error("此地区缺货"));
				return data;
			}
		}

		// 苏州通卡
		else if (product.getSupplierId().getIsOweOrder() == 65){
			if (!jstkService.checkStock(product,0)) {
				data.put("message", Message.error("缺货"));
				return data;
			}
		}
		// 天猫
		else if (product.getSupplierId().getIsOweOrder() == CommonAttributes.TMALL_SUPPLIER_ISOWEORDER) {
			List<Product> list = new ArrayList<>();
			list.add(product);
			Map<Long, Boolean> map = tMallService.checkStock(list, areaId);
			if (map.get(product.getId())) {
				data.put("message", Message.error("此地区缺货"));
				return data;
			}
		}
		data.put("message", SUCCESS_MESSAGE);
		return data;
	}

	/**
	 * 方法：异步刷新静态页面商品动态信息 实现流程： 1。刷新用户默认收货地址 2.刷新商品库存等信息 3.刷新企业简称信息 autor
	 * :penglong
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/getMemberCus", method = RequestMethod.POST)
	public @ResponseBody Map<String, String> getDateTime(Long id,
														 HttpServletRequest request) {
		Map<String, String> data = new HashMap<String, String>();
		Member member = memberService.getCurrent();
		Company company=null;
		if(member!=null){
			company=member.getCompanyId();
		}else{
			HttpSession session = request.getSession();
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		Product product = productService.find(id);
		BigDecimal supplierPriceRate = new BigDecimal(1);//根据供应商id获取加价策略
		BigDecimal supplierMarketPriceRate = new BigDecimal(1);//根据供应商id获取市场价加价策略
		String supplierPriceRateJson = company.getSupplierPriceRuleBySupplierId(product.getSupplierId().getId());
		JSONObject jsonObject = JSONObject.parseObject(supplierPriceRateJson);
		if(jsonObject!=null){
			supplierPriceRate = jsonObject.getBigDecimal("price");
			supplierMarketPriceRate=jsonObject.getBigDecimal("market_price");
		}

		if(supplierPriceRate!=null){
			BigDecimal productPrice=product.getPrice().multiply(supplierPriceRate). setScale( 2, BigDecimal.ROUND_HALF_UP );
			BigDecimal marketPrice=product.getMarketPrice().multiply(supplierMarketPriceRate). setScale( 2, BigDecimal.ROUND_HALF_UP );
			data.put("productPrice", productPrice.toString());
			data.put("productSavePrice",
					marketPrice.subtract(productPrice).toString());
			data.put("marketPrice", marketPrice.toString());
		}else{
			data.put("productPrice", product.getPrice().toString());
			data.put("productSavePrice",
					product.getMarketPrice().subtract(product.getPrice())
							.toString());
			data.put("marketPrice", product.getMarketPrice().toString());
		}


		String companyAbbreviation = "福利PLUS专享";

		if (member != null && member.getCompanyId() != null) {
			companyAbbreviation = member.getCompanyId()
					.getCompanyAbbreviation() + "专享";
		} else {
			//企业个性化域名标志
			Boolean isComPersonConf = (Boolean)request.getSession().getAttribute(Constants.COM_PERSON_CONF);
			if(isComPersonConf == null) isComPersonConf = Boolean.FALSE;
			if(isComPersonConf){
				companyAbbreviation = (String)request.getSession().getAttribute(Constants.COM_PERSON_ABBREVIATION) + "专享";
			}
		}

		String areaId = WebUtils.getCookie(request, "areaIdCookie");
		String areaName = WebUtils.getCookie(request, "cityCookie");
		String treePath = "";
		if(StringUtil.notEmpty(areaId)){
			Area area = areaService.find(Long.valueOf(areaId));
			if(area!=null){
				areaName = area.getFullName();
				treePath = area.getTreePath()+areaId;
			}
		}
		else if(StringUtil.notEmpty(areaName)) {
			JSONObject obj=JSON.parseObject(areaName);
			areaId=obj.getString("areaId");
			if(StringUtils.isNotBlank(areaId)) {
				Area area = areaService.find(Long.parseLong(areaId));
				if(area!=null){
					areaName = area.getFullName();
					treePath = area.getTreePath()+areaId;
				}
			}
		}else if(member != null){
			Receiver receiver = receiverService.findDefault(member);
			if (receiver != null) {
				areaId = receiver.getArea().getId().toString();
				areaName = receiver.getAreaName();
				treePath = receiver.getArea().getTreePath()+areaId;
			}
		}
		if (StringUtil.isEmpty(areaId) || StringUtil.isEmpty(areaName)
				|| StringUtil.isEmpty(treePath)) {
			if (member != null) {//用户不为空时，应取用户所在企业地址
				company = member.getCompanyId();
				Area area = company.getArea();
				treePath = area.getTreePath();
				if (treePath != null) {
					treePath = treePath.substring(1, treePath.length() - 1);
				}
				areaId = String.valueOf(area.getId());
				areaName = area.getFullName();
			} else {
				areaId = "3155";
				areaName = "广东深圳市南山区";
				treePath = ",19,1607,3155";
			}
		}
		//每日优鲜商品地址显示
		if(product.getSupplierId()!=null&&product.getSupplierId().getIsOweOrder()!=null&&product.getSupplierId().getIsOweOrder()==13){
			try {
				//获取所选详细地址
				String addressJson=WebUtils.getCookie(request, "selectAddressCookie");
				if(StringUtils.isNotBlank(addressJson)){
					addressJson=EscapeUnescape.unescape(addressJson);
					JSONObject obj=JSON.parseObject(addressJson);
					areaName=obj.getString("province")+obj.getString("city");
					if(obj.getString("province").equals(obj.getString("city"))){
						areaName=obj.getString("city");
					}
					areaName+=obj.getString("township")+obj.getString("address");
					if(!obj.getString("address").equals(obj.getString("name"))){
						areaName+=obj.getString("name");
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		data.put("hits", String.valueOf(product.getHits() + 20L));
		data.put("companyAbbreviation", companyAbbreviation);
		data.put("areaName", areaName);
		data.put("areaId", areaId);
		data.put("treePath", treePath);
		return data;

	}

	/**
	 * 方法：异步刷新静态页面商品动态信息-检查是否缺货 实现流程： 1。刷新用户默认收货地址 2.刷新商品库存等信息 3.刷新企业简称信息 autor
	 * :penglong
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/checkIsOutOfStock", method = RequestMethod.POST)
	public @ResponseBody Map<String, String> checkIsOutOfStock(Long id,
															   HttpServletRequest request) {
		Map<String, String> data = new HashMap<String, String>();
		Member member = memberService.getCurrent();
		Product product = productService.find(id);
		if (product != null &&( product.getIsMarketable()==false || product.getEnabledFlag()==false)) {
			data.put("isOutOfStock", "此商品已下架");
			return data;
		}

		if (!productService.checkStock(product,0)) {
			data.put("isOutOfStock", "缺货");
			return data;
		}

		Long areaId = null;
		//解析cityCookie
		String areaName = WebUtils.getCookie(request, "cityCookie");
		if(StringUtil.isEmpty(areaName)) {
			Area area = null;
			if (member != null) {
				Receiver receiver = receiverService.findDefault(member);
				if (receiver != null) {
					area = receiver.getArea();
				}
			}
			if (area == null) {
				areaId=3155L;
			}else {
				areaId=area.getId();
			}
		}else {
			JSONObject obj=JSON.parseObject(areaName);
			String areaIdStr=obj.getString("areaId");
			if(StringUtil.notEmpty(areaIdStr)) {
				areaId=Long.valueOf(areaIdStr);
			}
		}

		//xl20171204网易严选校验库存
		if(product.getSupplierId().getIsOweOrder() == 8){
			Long stock=yanxuanService.getYanxuanProductStock(product);
			if(stock==null || stock<1){
				data.put("isOutOfStock", "缺货");
				return data;
			}
		}

		//怡亚通校验库存
		else if (product.getSupplierId().getIsOweOrder() == 32) {
			Long  stock = eascsSyncService.getEascsProductStock(product);
			if (stock==-1) { // 1-商品已下架
				data.put("isOutOfStock", "此商品已下架");
				return data;
			} else if(stock==0) { // 0-上架状态
				data.put("isOutOfStock", "缺货");
				return data;
			}
		}

		// 调用京东校验接口
		else if (product.getSupplierId().getIsOweOrder() == 1 || product.getSupplierId().getIsOweOrder() == 18) {
			if(!jdService.getBizProductSkuCheck(product)){ //校验是否可售
				data.put("isOutOfStock", "此商品已下架");
			}
			if (jdService.checkProductsForList(product,areaId)) {
				data.put("isOutOfStock", "此地区缺货");
			}

			List<StockVO> stockVOs = new ArrayList<>();
			StockVO stockVO = new StockVO();
			stockVO.setSkuId(product.getJdSku());
			stockVO.setNum(1);
			stockVOs.add(stockVO);
			if (!jdService.checkAreaLimitForProductDetail(stockVOs, product, areaId)) {//校验区域购买限制
				data.put("isOutOfStock", "此地区缺货");
			}

		}
		// 调用优鲜校验接口
		else if (product.getSupplierId().getIsOweOrder() == 13) {
			if (!missfreshService.isSaleArea(product, areaId)) {
				data.put("isOutOfStock", "此地区缺货");
			}

		}
		else if (product.getSupplierId().getIsOweOrder() == 2) {
			xyService.checkPrice(product, null);
			if(product.getIsMarketable()==false || (product.getStock()!=null && product.getStock()==0)){
				data.put("isOutOfStock", "缺货");
				return data;
			}
		}
		else if(product.getSupplierId().getIsOweOrder() == 38){//bnplus库存校验
			bnplusService.checkPriceStock(product, null);
			if(product.getIsMarketable()==false || (product.getStock()!=null && product.getStock()==0)){
				data.put("isOutOfStock", "缺货");
				return data;
			}
		}
		//智臻云采
		else if(product.getSupplierId().getIsOweOrder() == 40){
			if(!zhiZhenYunCaiService.checkStock(product, null, areaId)) {
				data.put("isOutOfStock", "缺货");
				return data;
			}
		}		//蛋糕叔叔
		else if(product.getSupplierId().getIsOweOrder() == 47){
			if (!productService.isSaleArea(product, areaId)) {
				data.put("isOutOfStock", "当前地址不可配送");
				return data;
			}
		}

		// 调用fliPlus校验规则
		else if (product.getSupplierId().getIsOweOrder() == 0) {
			if (!productService.isSaleArea(product, areaId)) {
				data.put("isOutOfStock", "暂不支持此地区销售");
			}
		}

		// 京东健康
		else if (product.getSupplierId().getIsOweOrder() == 61){
			Company company=null;
			if(member !=null){
				company=member.getCompanyId();
			}else{
				String companyIdStr = (String) request.getSession().getAttribute("companyIdStr");
				if (companyIdStr != null) {
					Long companyId = Long.valueOf(companyIdStr);
					company = companyService.find(companyId);
				}else{
					company = companyService.getDefultCompany();
				}
			}
			List<Product> list = new ArrayList<>();
			list.add(product);
			Map<Long, Boolean> map = jdHealthService.checkStock(company.getId(), list, areaId);
			if (map.get(product.getId())) {
				data.put("isOutOfStock", "此地区缺货");
			}
		}
		// 苏州通卡
		else if (product.getSupplierId().getIsOweOrder() == 65){
			if (!jstkService.checkStock(product,0)) {
				data.put("isOutOfStock", "缺货");
				return data;
			}
		}
		// 天猫
		else if (product.getSupplierId().getIsOweOrder() == CommonAttributes.TMALL_SUPPLIER_ISOWEORDER) {
			List<Product> list = new ArrayList<>();
			list.add(product);
			Map<Long, Boolean> map = tMallService.checkStock(list, areaId);
			if (map.get(product.getId())) {
				data.put("isOutOfStock", "缺货");
				return data;
			}
		}
		return data;
	}

	/**
	 * 促销活动商品信息 方法: 实现流程: 1. autor :刘于燕
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/getPromotionAct", method = {RequestMethod.POST,RequestMethod.GET})
	@ResponseBody
	public Map<String, Object> getPromotionAct(Long id,
											   HttpServletRequest request) {
		Map<String, Object> data = new HashMap<String, Object>();
		Product product = productService.find(id);
		List<Act> acts = new ArrayList<Act>(product.getStatusActs());
		if (acts != null && acts.size() > 0) {
			Date date = new Date();
			Act act = acts.get(0);
			List<Act> actsValid = new ArrayList<Act>(product.getValidActs());
			if (actsValid.size() > 0) {
				act = actsValid.get(0);
				data.put("act", "1"); // 促销正在进行
			}
			if(act.getActRuleId()!=null) {
				if (act.getActRuleId().hasEnded()) { // 促销结束
					data.put("act", "4");
				} else if (!act.getActRuleId().hasBegun()
						&& CommUtil.isSameDate(act.getActRuleId().getBeginDate(),
								date)) {
					data.put("act", "2"); // 开始倒计时
				} else if (!act.getActRuleId().hasBegun()) {
					data.put("act", "3"); // 促销提醒
				}
				data.put("beginDate", act.getActRuleId().getBeginDate());
				data.put("endDate", act.getActRuleId().getEndDate());
			}
		}
		return data;
	}

	/**
	 * 每日促销商品信息 方法: 实现流程: 1. autor :刘于燕
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/getPromotion", method = RequestMethod.POST)
	@ResponseBody
	public Map<String, Object> getPromotion(Long id, HttpServletRequest request) {
		Map<String, Object> data = new HashMap<String, Object>();
		Product product = productService.find(id);
		List<Promotion> promotions = new ArrayList<Promotion>(
				product.getStatusPromotions());
		if (promotions.size() > 0) {
			Date date = new Date();
			Promotion promotion = promotions.get(0);
			List<Promotion> promotionsValid = new ArrayList<Promotion>(
					product.getValidPromotions());
			if (promotionsValid.size() > 0) {
				promotion = promotionsValid.get(0);
				data.put("promotion", "1"); // 促销正在进行
			}
			if (promotion.hasEnded()) { // 促销结束
				data.put("promotion", "4");
			} else if (!promotion.hasBegun()
					&& CommUtil.isSameDate(promotion.getBeginDate(), date)) {
				data.put("promotion", "2"); // 开始倒计时
			} else if (!promotion.hasBegun()) {
				data.put("promotion", "3"); // 促销提醒
			}
			data.put("beginDate", promotion.getBeginDate());
			data.put("endDate", promotion.getEndDate());
		}
		return data;

	}

	/**
	 * 团购商品已购买数量 方法: 实现流程: 1. autor :刘于燕
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/getQuantityGroupPurchase", method = RequestMethod.POST)
	@ResponseBody
	public Map<String, Object> getQtyGroupPurchase(Long id,
												   HttpServletRequest request) {
		Map<String, Object> data = new HashMap<String, Object>();
		Product product = productService.find(id);
		List<Group> groups = new ArrayList<Group>(product.getStatusGroups());
		if (groups != null && groups.size() > 0) {
			Date date = new Date();
			Group group = groups.get(0);
			List<Group> groupsValid = new ArrayList<Group>(
					product.getValidGroups());
			if (groupsValid.size() > 0) {
				group = groupsValid.get(0);
				data.put("group", "1"); // 促销正在进行
			}
			if (group.hasEnded()) { // 促销结束
				data.put("group", "4");
			} else if (!group.hasBegun()
					&& CommUtil.isSameDate(group.getBeginDate(), date)) {
				data.put("group", "2"); // 开始倒计时
			} else if (!group.hasBegun()) {
				data.put("group", "3"); // 促销提醒
			}
			data.put("beginDate", group.getBeginDate());
			data.put("endDate", group.getEndDate());
			data.put("groupId", group.getId());

			GroupProduct groupProduct = groupProductService
					.findByGroupIdAndProductId(group.getId(), id);
			data.put("productPrice", groupProduct.getGroupPrice());
			data.put(
					"productSavePrice",
					product.getMarketPrice()
							.subtract(groupProduct.getGroupPrice()).toString());
			Integer successCount = groupOrderService
					.getSuccessCount(groupProduct.getId());// 已购买数量
			if (groupProduct.getGroupCount()!=null) {//团购数量不为空时
				Integer diff = Math.max(groupProduct.getGroupCount() - successCount, 0);
				// Integer
				// diff=groupProduct.getGroupCount()-groupProduct.getSuccessCount()>0?groupProduct.getGroupCount()-groupProduct.getSuccessCount():0;
				data.put("qtyMsg", "已购" + successCount + "份,还差" + diff + "份成团");
			}
		}

		return data;
	}

	/**
	 * 方法：异步返回该会员是否收藏商品 autor :penglong
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody Message check(Long id) {
		Product product = productService.find(id);
		Member member = memberService.getCurrent();
		if (product == null || member == null) {
			return ERROR_MESSAGE;
		}
		if (!memberFavoriteProductService.exists(product, member)) {
			return Message.warn("");
		}

		return Message.success("");
	}

	/**
	 * 方法:列表判断商品是否缺货 true 缺货 实现流程: 1.如果地址为空取深圳南山区地址 autor :彭龙
	 *
	 * @param response
	 * @param ids
	 * @param areaId
	 * @return
	 */
	@RequestMapping(value = "/listStock", method = RequestMethod.POST)
	public @ResponseBody Map<Long, Boolean> listStock(HttpServletRequest request,
			HttpServletResponse response, Long[] ids, Long areaId) {
		if (areaId == null) {
			areaId = CommonAttributes.DEFAULT_AREA_ID;
		}
		Map<Long, Boolean> outMsg = new HashMap<Long, Boolean>();
		List<Product> products = productService.findList(ids);
		List<Product> productjd = new ArrayList<Product>();
		List<Product> jdHealthProducts = new ArrayList<>();
		List<Product> dangaoProducts = new ArrayList<>();
		List<Product> tmallProducts = new ArrayList<>();
//		List<Product> productMissfresh = new ArrayList<Product>();
		//用来存储行云的skus和商品Id
//		Map<String,Long> xyMap=new HashMap<>();
		for (Product product : products) {
			if (product != null && !product.getIsMarketable()) {
				outMsg.put(product.getId(), true);
			} else {
				// 调用京东校验接口
				if (product.getSupplierId().getIsOweOrder() == 1
						|| product.getSupplierId().getIsOweOrder() == 18 ) {
					productjd.add(product);
				}//校验优鲜商品
				/*	else if(product.getSupplierId().getIsOweOrder() == 13){
					productMissfresh.add(product);
				}*/
				// 调用fliPlus校验规则
				else if (product.getSupplierId().getIsOweOrder() == 0) {
					// 库存是否不足
					if (!productService.checkStock(product,0)) {
						outMsg.put(product.getId(), true);
						continue;
					}
					// 该区域是否销售
					if (!productService.isSaleArea(product, areaId)) {
						outMsg.put(product.getId(), true);
						continue;
					}
					// 两者都满足(有货)
					outMsg.put(product.getId(), false);
				}
				// 苏州通卡
				else if (product.getSupplierId().getIsOweOrder() == 65){
					if (!jstkService.checkStock(product,0)) {
						outMsg.put(product.getId(), true);
						continue;
					}
				}
				
				// 调用fliPlus校验规则  modify by hzq   添加行云供应商校验
				/*else if(product.getSupplierId().getIsOweOrder() == 2){
					// 库存是否不足
					//将海外购商品更改为实时查询接口
					xyMap.put(product.getJdSku(), product.getId());
				}*/
				else if (product.getSupplierId().getIsOweOrder() == 61){// 京东健康
					jdHealthProducts.add(product);
				}
				
				// 蛋糕叔叔
				else if (product.getSupplierId().getIsOweOrder()==47){
					dangaoProducts.add(product);
				}

				// 天猫
				else if (product.getSupplierId().getIsOweOrder() == CommonAttributes.TMALL_SUPPLIER_ISOWEORDER){
					tmallProducts.add(product);
				}
			}
		}
		//批量查询行云库存
	/*	Map<Long, Boolean> xyList = xyService.getProductStockList(xyMap);
		if(xyList!=null){
			outMsg.putAll(xyList);
		}

		if(missfreshService.checkProductsForList(productMissfresh, areaId)!=null){
			outMsg.putAll(missfreshService.checkProductsForList(productMissfresh, areaId));
		}
	*/
		Map<Long, Boolean> jdList = jdService.checkProductsForList(productjd, areaId);
		if(jdList!=null){
			outMsg.putAll(jdList);
		}
		if(jdHealthProducts!=null && !jdHealthProducts.isEmpty()) {
			Member member = memberService.getCurrent();
			Company company=null;
			if(member !=null){
				company=member.getCompanyId();
			}else{
				String companyIdStr = (String) request.getSession().getAttribute("companyIdStr");
				if (companyIdStr != null) {
					Long companyId = Long.valueOf(companyIdStr);
					company = companyService.find(companyId);
				}else{
					company = companyService.getDefultCompany();
				}
			}
			Map<Long, Boolean> jdHealthMap = jdHealthService.checkStock(company.getId(),jdHealthProducts, areaId);
			if(jdHealthMap!=null){
				outMsg.putAll(jdHealthMap);
			}
		}
		//蛋糕叔叔
		if (!dangaoProducts.isEmpty()){
			List<Product> dgProducts = new ArrayList<>();
			for (Product product : dangaoProducts) {
				if (!productService.isSaleArea(product, areaId)){// 不支持配送
					outMsg.put(product.getId(), true);
				}else {// 支持该城市配送,再根据消息地址配送规则判断是否可以配送
					dgProducts.add(product);
				}
			}
			if (!dgProducts.isEmpty()){
				Area cityArea = null;
				Member member = this.getCurrent();
				Map<Long, Boolean> dgMap = dangaoss2Service.isDelivery(dgProducts, member);
				if (dgMap!=null){
					outMsg.putAll(dgMap);
				}
			}
		}
		// 天猫
		else if (!tmallProducts.isEmpty()) {
			Map<Long, Boolean> tnallMap = tMallService.checkStock(tmallProducts, areaId);
			if (tnallMap != null) {
				outMsg.putAll(tnallMap);
			}
		}
		return outMsg;

	}




	/**
	 * 价高反馈信息 方法: 实现流程: 1. autor :sunjianwen
	 *
	 * @param productId
	 * @return
	 */
	@RequestMapping(value = "/priceFeedback", method = RequestMethod.POST)
	@ResponseBody
	public Boolean priceFeedback(Long productId) {
		Member member = memberService.getCurrent();
		Long memberId = -1L;
		if (member != null) {
			memberId = member.getId();
		}
		jdFeedbackService.sendMq(productId, memberId,2);
		return true;
	}



	/**
	 * 价高反馈信息 方法: 实现流程: 1. autor :sunjianwen
	 *
	 * @param productId
	 * @return
	 */
	@RequestMapping(value = "/getProductById", method = {RequestMethod.POST,RequestMethod.GET})
	@ResponseBody
	public Product getProductById(Long productId,HttpServletRequest request) {
		Member member = getCurrent();
		Product product = null;
		if (productId != null) {
			product = productService.find(productId);
		}

		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			HttpSession session = request.getSession();
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			logger.info("session中保存的companyIdStr=" + companyIdStr);
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}

		Map supplierPriceMap = null;
		String supplierPriceRate = company.getSupplierPriceRate();
		if (StringUtils.isNotEmpty(supplierPriceRate)) {
			supplierPriceMap = JSONObject.parseObject(supplierPriceRate);
			if (supplierPriceMap != null) {
				if (supplierPriceMap.get(product.getSupplierId().getId().toString()) != null) {
					String priceRate = JSON.parseObject(supplierPriceMap.get(product.getSupplierId().getId().toString()).toString()).getString("price");
					String marketPriceRate = JSON.parseObject(supplierPriceMap.get(product.getSupplierId().getId().toString()).toString()).getString("market_price");
					if (StringUtils.isNotEmpty(priceRate)) {
						product.setPrice(new BigDecimal(priceRate).multiply(product.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
					}
					if (StringUtils.isNotEmpty(marketPriceRate)) {
						product.setMarketPrice(new BigDecimal(marketPriceRate).multiply(product.getMarketPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
					}
				}
			}
		}

		return product;
	}


	/**
	 * 自动模糊查询搜索词 autor :刘于燕
	 *
	 * @param response
	 * @throws IOException
	 */
	@RequestMapping(value = "/autoCom", method = { RequestMethod.POST,
			RequestMethod.GET })
	public @ResponseBody Map<String, Object> AutoCom(
			HttpServletResponse response, String query) throws IOException {
		List<Map<String, String>> list=searchService.suggest(query);
		List<SearchHotVo> autoVos = new ArrayList<SearchHotVo>();
		if(list!=null && list.size()>0){
			for(Map<String, String> map:list){
				SearchHotVo searchHotVo = new SearchHotVo();
				searchHotVo.setValue(map.get("code"));
				searchHotVo.setData(map.get("code"));
				autoVos.add(searchHotVo);
			}
		}

		Map<String, Object> outMsg = new HashMap<String, Object>();
		outMsg.put("query", query);

		//关键词放入集合第一位
		SearchHotVo searchHotVo = new SearchHotVo();
		searchHotVo.setValue(query);
		searchHotVo.setData(query);
		autoVos.add(0, searchHotVo);

		outMsg.put("suggestions", autoVos);
		return outMsg;

	}


	/**
	 * <AUTHOR>
	 * @Description 精品商城首页
	 * @Date 2022/4/8 14:41
	 * @param hotProductPageSize 热销商品显示数量
	 * @param commendProductsPageSize 推荐商品显示数量
	 **/
	@RequestMapping("/boutiqueShop")
	public String toBoutiqueShop(@RequestParam(defaultValue = "6") Integer hotProductPageSize,
								 @RequestParam(defaultValue = "6") Integer commendProductsPageSize ,
								 ModelMap model){
		Member member = this.getCurrent();
		Company company = null;
		if(member != null){
			company = member.getCompanyId();
		}else{
			company = companyService.getDefultCompany();
		}

		Long boutiqueShopHotTagId = Long.parseLong(sysCodeService.getValue(SysCode.SYS_GROUP, Constants.BOUTIQUE_HOT_PRODUCT_TAG_ID));
		Long boutiqueShopRecommendTagId = Long.parseLong(sysCodeService.getValue(SysCode.SYS_GROUP, Constants.BOUTIQUE_RECOMMEND_TAG_ID));
		model.addAttribute("boutiqueShopHotTagId",boutiqueShopHotTagId);//热销商品tagId
		model.addAttribute("boutiqueShopRecommendTagId",boutiqueShopRecommendTagId);//推荐商品tagId


		Pageable pageable = new Pageable();
		pageable.setPageNumber(1);
		pageable.setPageSize(hotProductPageSize);
		model.addAttribute("hotProducts", productService.findPageByTagId(pageable, boutiqueShopHotTagId, null));//热销商品

		pageable.setPageSize(commendProductsPageSize);
		model.addAttribute("commendProducts", productService.findPageByTagId(pageable, boutiqueShopRecommendTagId, null));//推荐商品

		//获取分类
		//获取数据字典中的配置的id
		String idsStsr = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.BOUTIQUE_CATEGORY_IDS);
		String[] split = idsStsr.split(",");
		Long[] ids = new Long[split.length];
		for(int i = 0; i < split.length; i++) {
			ids[i] = Long.parseLong(split[i]);
		}
		// 根据数据字典查询到一级分类
		List<ProductCategory> productCategories = productCategoryService.findList(ids);

		// 精选商城分类属于京东商城 根据企业配置做筛选
		if(company!=null) {
			List<ProductCategory> companyCategory = productCategoryService.findCompanyCategory(null, company, CommonAttributes.JD_SUPPLY_ID);

			for (Iterator<ProductCategory> iterator = productCategories.iterator(); iterator.hasNext();) {
				ProductCategory productCategory = iterator.next();
				if (!companyCategory.contains(productCategory)) {
					iterator.remove();
				}
			}
		}

		model.addAttribute("productCategories", productCategories);

		//查询活动
		List<Act> actList = actService.findBoutiqueShopList();

		model.addAttribute("actList", actList);
		model.addAttribute("company", company);

		return "/wechat/product/boutique_shop";
	}

	/**
	 * <AUTHOR>
	 * @Description 跳转到精品商城-热销商品、精品推荐
	 * @Date 2022/4/13 14:48
	 * @param tagId 标签，用于区分热销商品、精品推荐的商品
	 * @param model
	 * @return java.lang.String
	 **/
	@RequestMapping("/boutiqueShopMore")
	public String toBoutiqueShopMore(Long tagId, OrderType orderType, ModelMap model){
		model.addAttribute("tagId", tagId);
		model.addAttribute("orderType", orderType);
		return "/wechat/product/boutique_shop_more";
	}

	/**
	 * <AUTHOR>
	 * @Description 精品商城-热销商品、精品推荐 的分页数据
	 * @Date 2022/4/13 15:21
	 * @param tagId 标签，用于区分热销商品、精品推荐的商品
	 * @param orderType
	 * @param pageable
	 * @param model
	 * @return java.lang.String
	 **/
	@RequestMapping(value = "/boutiqueShopMorePage", method = {RequestMethod.GET, RequestMethod.POST})
	public String toBoutiqueShopMorePage(Long tagId, OrderType orderType, Pageable pageable, ModelMap model){
		model.addAttribute("tagId", tagId);
		model.addAttribute("page", productService.findPageByTagId(pageable, tagId, orderType));

		return "/wechat/product/boutique_shop_more_page";
	}
	
	/**
	 *方法:工业品商城
	 *作者:sangyj
	 * @param model
	 * @param session
	 * @return
	 */
	@RequestMapping("/industrialShop")
	public String industrialShop(ModelMap model,HttpSession session) {
        Member member=getCurrent();
        Long cateId = CommonAttributes.INDUSTRIAL_CATEGORY_ID;
        Company company=null;
        if(member!=null){
            company=member.getCompanyId();
        }else{
            String companyIdStr = (String) session.getAttribute("companyIdStr");
            if (companyIdStr != null) {
                Long companyId = Long.valueOf(companyIdStr);
                company = companyService.find(companyId);
            }
        }
        model.addAttribute("company",company);
        //获取所有一级分类
        List<ProductCategory> productCategories = productCategoryService.findIndustrialCate(company);
        model.addAttribute("productCategories", productCategories);
        //68：京东供应商
		model.addAttribute("supplierIds",CommonAttributes.JD_SUPPLY_ID);
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if(!StringUtil.isEmpty(excludeSuppliers)){
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		//查询品牌
		ProductCategory productCategory = productCategoryService.find(cateId);
		Set<Brand> brandSet = productCategory.getBrands();
		List<Brand> brandList = new ArrayList<Brand>(brandSet);
		Collections.sort(brandList, new Comparator<Brand>() {
			@Override
			public int compare(Brand o1, Brand o2) {
				return o1.getOrder()-o2.getOrder();
			}
		});
		model.addAttribute("brands",brandList);
		
		//推荐分类
		List<ProductCategory> recommendCategories = productCategoryService.findIdstRecommendCate(company);
		model.addAttribute("recommendCategories", recommendCategories);
		//热推商品
		Page<Product> hostProducts = productService.getPageBytagId(new Pageable(1,8), 8L, null, null, CommonAttributes.JD_SUPPLY_ID, cateId, null);
		model.put("hostProducts", hostProducts.getContent());
		//分类推荐商品
		Map<Long,List<Product>> recommendProducts = new HashMap<Long, List<Product>>();
		for(ProductCategory cate : recommendCategories) {
			Page<Product> recProducts = productService.getPageBytagId(new Pageable(1,8), 10L, null, null, CommonAttributes.JD_SUPPLY_ID, null, cate.getId());
			recommendProducts.put(cate.getId(), recProducts.getContent());
		}
		model.put("recommendProducts", recommendProducts);
		model.put("productCategoryId", cateId);
		return "/wechat/product/industrial_shop";
	}
	
	
	/**
	 *方法:工业品商城-品牌列表
	 *作者:sangyj
	 * @param model
	 * @param session
	 * @return
	 */
	@RequestMapping("/industrialBrands")
	public String industrialBrands(ModelMap model,HttpSession session) {
        Long cateId = CommonAttributes.INDUSTRIAL_CATEGORY_ID;
		ProductCategory productCategory = productCategoryService.find(cateId);
		Set<Brand> brandList = productCategory.getBrands();
		List<String> keyList = new ArrayList<String>();
        //组装品牌首字母和品牌的对应关系
        Map<String,List<Brand>> brandMap = new HashMap<String,List<Brand>>();
        if(brandList!=null) {
        	for(Brand brand : brandList) {
        		String key = GetPinyin.getFirstPinYinHeadCharUpper(brand.getName());
        		List<Brand> list = brandMap.get(key);
        		if(list==null) {
        			list = new ArrayList<Brand>();
        			brandMap.put(key, list);
        			keyList.add(key);
        		}
        		list.add(brand);
        	}
        }
        Collections.sort(keyList);
        model.addAttribute("brandKeys", keyList);
        model.addAttribute("allBrand", brandMap);
        model.addAttribute("productCategoryId",cateId);
		return "/wechat/product/brandlist";
	}

	/**
	 *方法:检查商品分类限制
	 *作者:sangyj
	 * @param type
	 * @param jdSku
	 * @return
	 */
	@RequestMapping(value = "/checkCateLimit", method = RequestMethod.POST)
	public @ResponseBody Message checkCateLimit(Long productId,String jdSku) {
		Product product = null;
		if(productId!=null) {
			product = productService.find(productId);
		}else if(!StringUtil.isEmpty(jdSku)) {
			product = productService.findByJdSku(jdSku);
		}
		Member member = memberService.getCurrent();
		if (product == null || member == null) {
			return ERROR_MESSAGE;
		}
		//企业分类限制
		if(!member.getCompanyId().getCategoryAll()){ // 企业包含部分分类
			if(!productCategoryService.checkCompanyCategory(member.getCompanyId().getId(),product.getProductCategory().getId())){
				logger.info("检查商品分类限制失败{}--{}",member.getCompanyId().getId(),product.getProductCategory().getId());
				return Message.success("0");
			}
		}
		return Message.success("1");
	}
	
	/**
	 *方法:活动分享日志
	 *作者:sangyj
	 * @param id
	 * @param type
	 * @param actId
	 * @param mid
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/share/{id}", method = RequestMethod.POST)
	public Message share(@PathVariable Long id,Integer type,Long groupId,Long actId,String mid) {
		Member member=getCurrent();
		if(id==null || member==null || type==null) {
			return Message.error("参数错误");
		}
		Product product = productService.find(id);
		if(product==null) {
			return Message.error("参数错误");
		}
		String actType = null;
		if(actId!=null) {//普通活动
			Act act = actService.find(actId);
			if(act==null) {
				return Message.error("参数错误");
			}
			actType = act.getType().name();
		}
		if(groupId!=null) {//拼团活动
			actId = groupId;
			actType = "groupAct";
		}
		Long parentId = null;
		if(!StringUtil.isEmpty(mid)){
			try {
				parentId = Long.valueOf(AesUtil.decode(URLDecoder.decode(mid,"UTF-8")));
			} catch (Exception e) {
				logger.error("memberId解码失败",e);
				return Message.error("参数错误");
			}
		}
		return actProductShareLogService.save(type,actId,actType,id,parentId,member);
	}

	@RequestMapping(value = "/listPage", method = { RequestMethod.GET,RequestMethod.POST })
	@ResponseBody
	public Page<Product> listPage(Long productCategoryId, Long brandId, String specificationTag, String attributeValue5,String attributeValue11,
						    	BigDecimal startPrice, BigDecimal endPrice, OrderType orderType, Pageable pageable,
						   		HttpServletRequest request, ModelMap model) {

		ProductCategory productCategory = productCategoryService.find(productCategoryId);
		if (productCategory == null) {
			throw new ResourceNotFoundException();
		}
		String cityId = null;
		//蛋糕叔叔商品列表
		if(productCategory.getSupplierId()==CommonAttributes.DANGAOSS2_SUPPLIERID.longValue()) {
			// 获取城市Id
			cityId = areaService.getCurrentCityId(false);
		}
		model.addAttribute("brandId",brandId);

		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			HttpSession session=request.getSession();
			String companyIdStr=(String) session.getAttribute("companyIdStr");
			if(companyIdStr!=null){
				Long companyId=Long.valueOf(companyIdStr);
				company=companyService.find(companyId);
				if (company != null) {
					logger.info("companyId="+company.getId());
				}
			}else{
				company = companyService.getDefultCompany();
			}
		}
		List<Supplier> suppliers = new ArrayList<Supplier>();
		if (!company.getSupplierAll()) { // 如果非所有供应商开放获取相应有权限供应商 做in查询
			suppliers = supplierService.findCompanySupplier(company.getId());
			//suppliers = new ArrayList<Supplier>(company.getSuppliers());
		}
		if (!company.getCategoryAll()) { // 如果非所有商品分类开发查询相应有权限分类 做in查询
			// 动态获取公司下分类
			List<Long> companyCategory = productCategoryService.findCompanyCategory(company);

			productCategory.getChildren().clear();
			productCategory.setChildren(new HashSet<ProductCategory>(
					productCategoryService.findCompanyCategoryChildren(
							productCategory, null, companyCategory)));
		}
		if (orderType == null) {
			//京东商品默认不排序
			if(productCategory.getSupplierId()==null ||
					!(productCategory.getSupplierId()==68
							|| productCategory.getSupplierId()==181)){
				orderType = Product.OrderType.tagDesc;// 默认综合排序
			}
		}
		Supplier supplier = null;
		if(productCategory.getSupplierId()!=null ){
			// 自营商城和怡亚通共用自营商城分类
			SysCode supplierSc=sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_SUPPLIER_IDS_KEY);
			String[] supplierArr = supplierSc.getValue().split(",");
			Boolean isSelf = Arrays.asList(supplierArr).contains(String.valueOf(productCategory.getSupplierId()));
			if(isSelf && company.getCategoryAll()){
				Long [] supplierIds = new Long[supplierArr.length];
				for(int i=0;i<supplierArr.length;i++) {
					supplierIds[i]=Long.parseLong(supplierArr[i]);
				}
				suppliers = supplierService.findList(supplierIds);
			}else{
				supplier = supplierService.find(productCategory.getSupplierId());
			}
		}

		Brand brand = null;
		if(brandId!=null){
			brand = brandService.find(brandId);
		}
		//获取区域id,用于区域限制筛选
		Area area = null;
		if(cityId!=null) {
			area = areaService.find(Long.valueOf(cityId));
		}
		Setting setting = SettingUtils.get();
		String siteUrlImg = setting.getSiteUrlImg();
		String defaultThumbnailProductImage = setting.getDefaultThumbnailProductImage();
		String productDefaultImg = sysCodeService.getValue(Constants.COM_PERSON_CONF_GROUP_PREFIX + company.getId(), Constants.COM_PERSON_PRODUCT_DEFAULT_IMG);
		Page<Product> page = productService.findPage(productCategory,
				brand, supplier, suppliers, null, null, "3", startPrice,
				endPrice, true, true, null, false, orderType,
				pageable, company, true, null, area, specificationTag, attributeValue5, attributeValue11);
		if (page!=null&&!page.getContent().isEmpty()){
			for (Product product:page.getContent()){
				AddPriceProductPageVo addPriceProductPageVo = productAddPriceService.getProductAddPrice(product, company);
				if(addPriceProductPageVo != null) {
					product.setPrice(addPriceProductPageVo.getPrice());
					product.setMarketPrice(addPriceProductPageVo.getMarketPrice());//设置市场价企业加价
				}
				String imgUrl="";
				if (product.getSupplierId().getId()==68 || product.getSupplierId().getId()==181){
					if(product.getAttributeValue8()!=null){
						imgUrl = product.getAttributeValue8();
					}else if(product.getImage()!=null){
						if (!product.getImage().contains("http")){
							imgUrl = siteUrlImg+product.getImage();
						}else {
							imgUrl = product.getImage();
						}
					}else {
						if(StringUtils.isNotBlank(productDefaultImg)){
							imgUrl = productDefaultImg;
						}else {
							imgUrl = siteUrlImg+defaultThumbnailProductImage;
						}
					}
				}else{
					if(product.getImage()!=null){
						if (!product.getImage().contains("http")){
							imgUrl = siteUrlImg+product.getImage();
						}else {
							imgUrl = product.getImage();
						}
					}else {
						if(StringUtils.isNotBlank(productDefaultImg)){
							imgUrl = productDefaultImg;
						}else {
							imgUrl =siteUrlImg+defaultThumbnailProductImage;;
						}
					}
				}
				product.setImage(imgUrl);
			}//商品for循环结束
		}
		Map<String, Object> data = page.getData();
		data.put("company",company);
		data.put("productDefaultImg",productDefaultImg);
		return page;
	}

	@RequestMapping(value = "/discountPageData", method = RequestMethod.POST)
	public @ResponseBody Page<Product> discountPageData(Long discountRate, Boolean isProductCategory,
							   BigDecimal startPrice, BigDecimal endPrice, OrderType orderType,
							   Long productCategoryId, Integer pageNumber, Integer pageSize,
							   HttpSession session) {
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		Supplier supplier = supplierService.find(68L);
		List<Supplier> suppliers = new ArrayList<Supplier>();
		suppliers.add(supplier);
		ProductCategory productCategory = productCategoryService
				.find(productCategoryId);
		Pageable pageable = new Pageable(pageNumber, pageSize);
		if (isProductCategory != null) {
			discountRate = null;
			pageable.getFilters().add(Filter.le("attributeValue14", 80));
		} else {
			if(discountRate !=null){
				if(discountRate == 3){
					pageable.getFilters().add(Filter.le("attributeValue14", 30));
				}else if(discountRate == 5){
					pageable.getFilters().add(Filter.ge("attributeValue14", 30));
					pageable.getFilters().add(Filter.le("attributeValue14", 50));
				}else if(discountRate == 8){
					pageable.getFilters().add(Filter.ge("attributeValue14", 50));
					pageable.getFilters().add(Filter.le("attributeValue14", 80));
				}
			}
		}
		Page<Product> page = productService.findListByDiscountRate(
				discountRate, productCategory, null, null, suppliers, null,
				null, "3", startPrice, endPrice, true, true, null, false
				, orderType, pageable,company);
		Setting setting = SettingUtils.get();
		String siteUrlImg = setting.getSiteUrlImg();
		String defaultThumbnailProductImage = setting.getDefaultThumbnailProductImage();
		String productDefaultImg = sysCodeService.getValue(Constants.COM_PERSON_CONF_GROUP_PREFIX + company.getId(), Constants.COM_PERSON_PRODUCT_DEFAULT_IMG);
		if (page!=null&&!page.getContent().isEmpty()){
			for (Product product:page.getContent()){
				AddPriceProductPageVo addPriceProductPageVo = productAddPriceService.getProductAddPrice(product, company);
				if(addPriceProductPageVo != null) {
					product.setPrice(addPriceProductPageVo.getPrice());
					product.setMarketPrice(addPriceProductPageVo.getMarketPrice());//设置市场价企业加价
				}
				String imgUrl="";
				if (product.getSupplierId().getId()==68 || product.getSupplierId().getId()==181){
					if(product.getAttributeValue8()!=null){
						imgUrl = product.getAttributeValue8();
					}else if(product.getImage()!=null){
						imgUrl = siteUrlImg+product.getImage();
					}else {
						if(StringUtils.isNotBlank(productDefaultImg)){
							imgUrl = productDefaultImg;
						}else {
							imgUrl = siteUrlImg+defaultThumbnailProductImage;
						}
					}
				}else{
					if(product.getImage()!=null){
						imgUrl = siteUrlImg+product.getImage();;
					}else {
						if(StringUtils.isNotBlank(productDefaultImg)){
							imgUrl = productDefaultImg;
						}else {
							imgUrl =siteUrlImg+defaultThumbnailProductImage;;
						}
					}
				}
				product.setImage(imgUrl);
			}//商品for循环结束
		}
		Map<String, Object> data = page.getData();
		data.put("company",company);
		data.put("productDefaultImg",productDefaultImg);
		return page;
	}

	@RequestMapping(value = "/searchPageData", method = {RequestMethod.GET,RequestMethod.POST})
	public @ResponseBody Page<Product> searchPageData(String keyword, BigDecimal startPrice, String specificationTag,
							 BigDecimal endPrice, OrderType orderType, String productCategoryId,
							 Pageable pageable, HttpServletRequest request,
							 Boolean isDiscount,Long minDiscount,Long maxDiscount,String supplierId, String attributeValue5, String attributeValue11,
							 String productCategoryName,String brandName,Integer type,Long companyId,HttpSession session) {
		if (StringUtils.isEmpty(keyword)) {
			return null;
		}
		Member member = memberService.getCurrent();
		
		keyword=StringEscapeUtils.unescapeHtml(keyword);
		if(productCategoryName!=null){
			productCategoryName=StringEscapeUtils.unescapeHtml(productCategoryName);
		}
		if(brandName!=null){
			brandName=StringEscapeUtils.unescapeHtml(brandName);
		}
		
		//---每日优鲜商品过滤城市
		String cityId=null;
		if(supplierId!=null&& !supplierId.contains(",") &&supplierService.exists(Filter.eq("id", Long.parseLong(supplierId)),Filter.eq("isOweOrder", 13))){
			cityId=areaService.getCurrentCityId(true);
		}

		if (supplierId!=null&& !supplierId.contains(",") && Long.parseLong(supplierId) == CommonAttributes.DANGAOSS2_SUPPLIERID) {
			cityId = areaService.getCurrentCityId(false);
		}

		Company company=null;
		if(member !=null){
			company=member.getCompanyId();
		}else{
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			}else{
				company = companyService.getDefultCompany();
			}
		}
		
		Page<Product> page =searchService.searchByOpenSearch(keyword, startPrice, endPrice, orderType, pageable, isDiscount, supplierId, productCategoryName,productCategoryId, brandName, minDiscount, maxDiscount,cityId,attributeValue11,company, specificationTag, attributeValue5,null);
		Setting setting = SettingUtils.get();
		String siteUrlImg = setting.getSiteUrlImg();
		String defaultThumbnailProductImage = setting.getDefaultThumbnailProductImage();
		String productDefaultImg = sysCodeService.getValue(Constants.COM_PERSON_CONF_GROUP_PREFIX + company.getId(), Constants.COM_PERSON_PRODUCT_DEFAULT_IMG);
		if (page!=null&&!page.getContent().isEmpty()){
			for (Product product:page.getContent()){
				AddPriceProductPageVo addPriceProductPageVo = productAddPriceService.getProductAddPrice(product, company);
				if(addPriceProductPageVo != null) {
					product.setPrice(addPriceProductPageVo.getPrice());
					product.setMarketPrice(addPriceProductPageVo.getMarketPrice());//设置市场价企业加价
				}
				String imgUrl="";
				if (product.getSupplierId().getId()==68 || product.getSupplierId().getId()==181){
					if(product.getAttributeValue8()!=null){
						imgUrl = product.getAttributeValue8();
					}else if(product.getImage()!=null){
						if (!product.getImage().contains("http")){
							imgUrl = siteUrlImg+product.getImage();
						}else {
							imgUrl = product.getImage();
						}
					}else {
						if(StringUtils.isNotBlank(productDefaultImg)){
							imgUrl = productDefaultImg;
						}else {
							imgUrl = siteUrlImg+defaultThumbnailProductImage;
						}
					}
				}else{
					if(product.getImage()!=null){
						if (!product.getImage().contains("http")){
							imgUrl = siteUrlImg+product.getImage();
						}else {
							imgUrl = product.getImage();
						}
					}else {
						if(StringUtils.isNotBlank(productDefaultImg)){
							imgUrl = productDefaultImg;
						}else {
							imgUrl =siteUrlImg+defaultThumbnailProductImage;;
						}
					}
				}
				product.setImage(imgUrl);
			}//商品for循环结束
		}
		Map<String, Object> data = page.getData();
		data.put("company",company);
		data.put("productDefaultImg",productDefaultImg);
		/** 蛋糕叔叔查询出对应品牌*/
		List<String> brandNames = (List<String>) page.getData().get("brandNames");
		if (!brandNames.isEmpty() && CommonAttributes.DANGAOSS2_SUPPLIERID.toString().equals(supplierId)){
			List<Filter> filters = new ArrayList<>();
			filters.add(Filter.eq("enabledFlag", true));
			filters.add(Filter.eq("supplierId.id", CommonAttributes.DANGAOSS2_SUPPLIERID));
			filters.add(Filter.in("name", brandNames));
			List<Order> orders = new ArrayList<>();
			orders.add(Order.asc("id"));
			List<Brand> brands = brandService.findList(null, filters, orders);
			
			page.getData().put("brands", brands);
		}
		return page;
	}
	/**
	 * 天猫商城
	 * @param model 视图
	 * @param session session
	 * @author: GanSiquan
	 * @date: 2025-07-23 17:59
	 */
	@RequestMapping("/tMallShop")
	public String tMallShop(ModelMap model,HttpSession session) {
		Member member = getCurrent();
		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}

		//天猫供应商
		Supplier supplier = supplierService.getSupplierByPlatform(CommonAttributes.TMALL_SUPPLIER_ISOWEORDER);

		model.addAttribute("company", company);
		model.addAttribute("supplier", supplier);
		if (company != null) {
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		
		// 1.查询供应商二级分类
		List<Filter> filters = new ArrayList<>();
		List<Order> orders = new ArrayList<>();
		filters.add(Filter.eq("supplierId", supplier.getId()));
		filters.add(Filter.eq("grade", 1));
		filters.add(Filter.eq("enabledFlag", true));
		orders.add(Order.asc("order"));
		List<ProductCategory> companyChildrenTwo = productCategoryService.findList(null, filters, orders);
		model.addAttribute("productCategories", companyChildrenTwo);

		Page<Product> page = productService.findPage(null, null, supplier, null, null, null, "3", null, null, true, true, null, false, null, new Pageable(1, 10), company, true, null, null, null, null, null);
		if (page != null && page.getContent() != null && !page.getContent().isEmpty()) {
			List<Product> products = page.getContent();

			// 企业加价
			for (Product product : products) {
				AddPriceProductPageVo addPriceProductPageVo = productAddPriceService.getProductAddPrice(product, company);
				if (addPriceProductPageVo != null) {
					product.setPrice(addPriceProductPageVo.getPrice());
					product.setMarketPrice(addPriceProductPageVo.getMarketPrice());//设置市场价企业加价
				}
			}
		}
		model.addAttribute("page", page);

		String actId = sysCodeService.getValue(SysCode.SYS_GROUP, "tmall_shop_act");
		Act act = actService.find(Long.valueOf(actId));
		model.addAttribute("act", act);

		return "/wechat/product/tmall_shop";
	}

	/**
	 * 查询指定分类下指定数量商品
	 * @param productCategoryId 分类id
	 * @param pageable 分页参数
	 * @author: GanSiquan
	 * @date: 2025-01-13 17:27
	 */
	@RequestMapping("/tmallProductList")
	public @ResponseBody Page<Product> productList(Pageable pageable, HttpServletRequest request){
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			HttpSession session = request.getSession();
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}

		// 供应商获取
		Supplier supplier = supplierService.getSupplierByPlatform(CommonAttributes.TMALL_SUPPLIER_ISOWEORDER);
		Page<Product> page = productService.findPage(null, null, supplier, null, null, null, "3", null, null, true, true, null, false, null, pageable, company, true, null, null, null, null, null);
		if (page!=null && page.getContent()!=null && !page.getContent().isEmpty()) {
			List<Product> products = page.getContent();

			// 企业加价
			for (Product product : products) {
				AddPriceProductPageVo addPriceProductPageVo = productAddPriceService.getProductAddPrice(product, company);
				if(addPriceProductPageVo != null) {
					product.setPrice(addPriceProductPageVo.getPrice());
					product.setMarketPrice(addPriceProductPageVo.getMarketPrice());//设置市场价企业加价
				}
			}
		}
		return page;
	}
}