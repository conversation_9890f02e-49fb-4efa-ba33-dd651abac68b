/**
 * 版权所有：版权所有(C) 2016，远行科技
 * 文件编号：M01_ProductController.java
 * 文件名称：ProductController.java
 * 系统编号：远行福利plus
 * 设计作者：彭龙
 * 完成日期：2016年7月20日
 * 设计文档：
 * 内容摘要：TODO
 * 系统用例：
 * 界面原型：
 */
package com.vispractice.vpshop.controller.shop;

import com.alibaba.fastjson.JSONObject;
import com.vispractice.vpshop.Message;
import com.vispractice.vpshop.Order;
import com.vispractice.vpshop.*;
import com.vispractice.vpshop.activemq.ProducerService;
import com.vispractice.vpshop.activemq.log.LogProductMsgBean;
import com.vispractice.vpshop.bnplus.service.BnplusService;
import com.vispractice.vpshop.constant.Constants;
import com.vispractice.vpshop.dao.ActProductDao;
import com.vispractice.vpshop.eascs.service.EascsSyncService;
import com.vispractice.vpshop.entity.*;
import com.vispractice.vpshop.entity.Product.OrderType;
import com.vispractice.vpshop.jd.model.request.StockVO;
import com.vispractice.vpshop.jd.service.JdService;
import com.vispractice.vpshop.jdHealth.JdHealthService;
import com.vispractice.vpshop.jstk.JSTKService;
import com.vispractice.vpshop.missfresh.service.MissfreshService;
import com.vispractice.vpshop.service.*;
import com.vispractice.vpshop.tmall.TMallService;
import com.vispractice.vpshop.util.*;
import com.vispractice.vpshop.vo.AddPriceProductPageVo;
import com.vispractice.vpshop.xy.service.XyService;
import com.vispractice.vpshop.yanxuan.service.YanxuanService;
import com.vispractice.vpshop.zip.ZipUtils;
import com.vispractice.vpshop.zzyc.service.ZhiZhenYunCaiService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.ServletContextAware;

import javax.annotation.Resource;
import javax.jms.Destination;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URL;
import java.util.*;

/**
 * 类 编 号：UI_PU010401_ProductController 类 名 称：ProductController.java
 * 内容摘要：Controller - 商品 完成日期：2016年7月20日 下午2:26:03 编码作者: 彭龙
 */
@Controller("shopProductController")
@RequestMapping("/product")
public class ProductController extends BaseController implements
ServletContextAware {
	private static Logger logger = LoggerFactory.getLogger(ProductController.class);
	@Resource(name = "reviewServiceImpl")
	private ReviewService reviewService;
	@Resource(name = "memberServiceImpl")
	private MemberService memberService;
	@Resource(name = "productServiceImpl")
	private ProductService productService;
	@Resource
	private ActProductService actProductService;
	@Resource
	private ActProductDao actProductDao;
	@Resource(name = "couponCodeServiceImpl")
	private CouponCodeService couponCodeService;

	@Resource(name = "productCategoryServiceImpl")
	private ProductCategoryService productCategoryService;
	@Resource(name = "brandServiceImpl")
	private BrandService brandService;
	@Resource(name = "promotionServiceImpl")
	private PromotionService promotionService;
	@Resource(name = "tagServiceImpl")
	private TagService tagService;
	@Resource(name = "searchServiceImpl")
	private SearchService searchService;
	@Resource(name = "receiverServiceImpl")
	private ReceiverService receiverService;
	@Resource(name = "staticServiceImpl")
	private StaticService staticService;
	@Resource(name = "jdServiceImpl")
	private JdService jdService;
	@Resource(name = "jdFeedbackServiceImpl")
	private JdFeedbackService jdFeedbackService;
	@Resource(name = "couponServiceImpl")
	private CouponService couponService;
	@Resource(name = "reportProductServiceImpl")
	private ReportProductService reportProductService;
	@Resource(name = "sysCodeServiceImpl")
	private SysCodeService sysCodeService;
	@Resource(name = "orderItemServiceImpl")
	private OrderItemService orderItemService;
	@Resource(name = "actServiceImpl")
	private ActService actService;
	@Resource(name = "groupProductServiceImpl")
	private GroupProductService groupProductService;

	@Resource(name = "areaServiceImpl")
	private AreaService areaService;
	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "searchHotServiceImpl")
	private SearchHotService searchHotService;

	@Resource(name = "memberFavoriteProductServiceImpl")
	private MemberFavoriteProductService memberFavoriteProductService;
	@Resource(name = "groupOrderServiceImpl")
	private GroupOrderService groupOrderService;

	@Resource(name = "companyServiceImpl")
	private CompanyService companyService;

	@Resource(name = "productHotServiceImpl")
	private ProductHotService productHotService;

	@Resource(name = "supplierServiceImpl")
	private SupplierService supplierService;

	@Autowired
	private ProducerService producerService;
	@Autowired
	@Qualifier("queueDestinationHitLog")
	private Destination destination;

	@Resource(name = "templateServiceImpl")
	private TemplateService templateService;

	@Resource
	private MissfreshService missfreshService;


	@Resource
	private ProductAddPriceService productAddPriceService;

	// 发送搜索日志队列
	@Autowired
	@Qualifier("queueDestinationSearchLog")
	private Destination destinationSearch;
	@Resource
	private YanxuanService yanxuanService;

	@Resource(name = "xyServiceImpl")
	private XyService xyService;

	@Resource
	private EascsSyncService eascsSyncService;

	@Resource
	private GroupService groupService;

	@Resource
	private BnplusService bnplusService;

	@Resource
	private ZhiZhenYunCaiService zhiZhenYunCaiService;

	@Resource
	private JdHealthService jdHealthService;
	@Resource(name ="purchaseListServiceImpl")
	private PurchaseListService purchaseListService;
	
	@Resource
	private JSTKService jstkService;
	
	@Resource
	private TMallService tMallService;
	/**
	 * servletContext
	 */
	private ServletContext servletContext;

	public void setServletContext(ServletContext servletContext) {
		this.servletContext = servletContext;
	}

	@RequestMapping("/auto")
	public void test11() {
		productHotService.saveProductHot();
	}

	/**
	 * 方法:京东商城首页 autor :penglong
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping("/index")
	public String index(ModelMap model, HttpServletRequest request) {
		logger.info("京东商城首页------------");
//		model.addAttribute("coupons", couponService.getCouponsOrderByAmount());

		// 增加判断是否移动端，移动端展示移动端页面
		try {
			if (CheckMobile.checkIsMobile(request)) {
				Setting setting = SettingUtils.get();
				return "redirect:" + setting.getSiteUrlwechat() + CheckMobile.getRequestURL(request);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		// end 移动端页面跳转

		// 获取热搜词
		SysCode sysCode = sysCodeService.findbyCode("hotSearch");
		String[] hotSearchs = null;
		if (sysCode != null) {
			hotSearchs = StringUtils.split(sysCode.getValue(), ",");
		}
		// 热搜词新增结束
		model.addAttribute("hotSearchs", hotSearchs);
		Long[] hotProductCategoryIds = productHotService
				.getHotProductCategoryOne(6, 2);
		List<ProductCategory> productCategorys = productCategoryService
				.findList(hotProductCategoryIds);
		model.addAttribute("productCategorys", productCategorys);
		List<ProductHot> hotProducts = new ArrayList<ProductHot>();
		if (productCategorys != null && productCategorys.size() > 0) {
			hotProducts = productHotService.getHotProduct(productCategorys.get(0)
					.getId(), 6, 2);
		}
		model.addAttribute("hotProducts", hotProducts);
		Member member = getCurrent();
		Company company = null;
		Long companyId = null;
		if (member != null) { //用户不为空时，显示用户默认地址；用户地址为空时，显示用户所在企业地址
			company = member.getCompanyId();
			companyId = company.getId();
			Receiver receiver = receiverService.findDefault(member);//获取用户默认地址
			if (receiver != null) {
				String cityName = receiver.getAreaName();
				String areaId = receiver.getArea().getId().toString();
				String treePath = receiver.getArea().getTreePath();
				if (treePath != null) {
					treePath = treePath.substring(1, treePath.length() - 1);
					String[] treePathArr = CommUtil.splitByChar(treePath,
							",");

					if (treePathArr.length >= 2) {
						String provinceId = treePathArr[0];
						String cityId = treePathArr[1];
						model.addAttribute("provinceId", provinceId);
						model.addAttribute("cityId", cityId);
					}
				}
				model.addAttribute("cityName", cityName);
				model.addAttribute("areaId", areaId);
			} else { //用户默认地址为空

			}
		} else {
			try {
				String companyIdStr = (String) request.getSession().getAttribute("companyId");
				if (companyIdStr != null && !companyIdStr.isEmpty()) {
					companyId = Long.valueOf(companyIdStr);
				}
			} catch (Exception e) {

			}
			if (companyId == null) {
				company = companyService.getDefultCompany();
				companyId = company.getId();
			}

		}

//		List<ProductCategory> companyCategory = new ArrayList<>();
//		List<ProductCategory> thirdProductCategories = new ArrayList<>();
		if (company != null) {
//			companyCategory = productCategoryService.findCompanyCategory(12, company, CommonAttributes.JD_SUPPLY_ID);
			//		model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}

			// 获取企业限制三级分类
//			if (!company.getCategoryAll()) {
//				// 京东首页默认展示12个一级分类
//				List<Long> companyCategoryId = productCategoryService.findCompanyCategory(company);
//				for (ProductCategory productCategory : companyCategory) {
//					productCategory.getChildren().clear();
//					productCategory.setChildren(new HashSet<ProductCategory>(productCategoryService.findCompanyCategoryChildren(productCategory, null, companyCategoryId)));
//
//					for (ProductCategory twoProductCategory : productCategory.getChildren()) {
//						List<ProductCategory> companyChildrenThird = productCategoryService.findCompanyCategoryChildren(twoProductCategory, null, companyCategoryId);
//						thirdProductCategories.addAll(companyChildrenThird);
//					}
//				}
//			}
		}

		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if (!StringUtil.isEmpty(excludeSuppliers)) {
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}

		List<Coupon> coupons = couponService.getCouponsOrderByAmount();

		if (member != null) {
			if (coupons != null && coupons.size() > 0) {
				Coupon coupon = null;
				for (int i = 0; i < coupons.size(); i++) {
					coupon = coupons.get(i);
					Long count = couponCodeService.getReceivedCoupon(coupon, member);
					if (count != null && count > 0) {// 已经领取过
						coupon.setIsRecived(true);
					}
				}
			}
		}
		model.addAttribute("coupons", coupons);
		model.addAttribute("companyId", companyId);
		model.addAttribute("company", company);
//		model.addAttribute("productCategories",companyCategory);
//		model.addAttribute("thirdProductCategories",thirdProductCategories);
		logger.info("京东商城首页结束------------");
		return "/shop/product/index";
	}

	/**
	 * 方法:折扣商城 autor :胡辉
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping("/discountShop")
	public String discountShop(ModelMap model, HttpServletRequest request, HttpSession session) {
		// 增加判断是否移动端，移动端展示移动端页面
		try {
			if (CheckMobile.checkIsMobile(request)) {
				Setting setting = SettingUtils.get();
				return "redirect:" + setting.getSiteUrlwechat() + CheckMobile.getRequestURL(request);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		// end 移动端页面跳转
		// 获取热搜词
		SysCode sysCode = sysCodeService.findbyCode("hotSearch");
		String[] hotSearchs = null;
		if (sysCode != null) {
			hotSearchs = StringUtils.split(sysCode.getValue(), ",");
		}
		// 热搜词新增结束
		model.addAttribute("hotSearchs", hotSearchs);
		// 获取折扣商城的热门推荐的6个一级分类以及商品
		Long[] hotProductCategoryIds = productHotService
				.getHotProductCategoryOne(6, 0);
		List<ProductCategory> productCategorys = productCategoryService
				.findList(hotProductCategoryIds);
		model.addAttribute("productCategorys", productCategorys);
		for (int i = 0; i < productCategorys.size(); i++) {
			List<ProductHot> hotProducts = new ArrayList<ProductHot>();
			hotProducts = productHotService.getHotProduct(productCategorys.get(i).getId(), 5, 0);
			model.addAttribute("hotProducts" + i, hotProducts);
		}

		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if (!StringUtil.isEmpty(excludeSuppliers)) {
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}

		Member member = getCurrent();
		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}
		model.addAttribute("company", company);
		if (company != null) {
			//		model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		return "/shop/product/discount";
	}

	/**
	 * @param model
	 * @param session
	 * @return
	 */
	@RequestMapping("/selfShop")
	public String selfShop(ModelMap model, HttpSession session) {
		// 获取热搜词
		SysCode sysCode = sysCodeService.findbyCode("hotSearch");
		String[] hotSearchs = null;
		if (sysCode != null) {
			hotSearchs = StringUtils.split(sysCode.getValue(), ",");
		}
		// 热搜词新增结束
		model.addAttribute("hotSearchs", hotSearchs);
		//获取自营的分类
		SysCode sc = sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_ROOT_PRODUCT_CATEGORY_IDS_KEY);
		if (sc != null && StringUtils.isNotBlank(sc.getValue())) {
			String[] array = sc.getValue().split(",");
			List<Long> productCategoryIds = new ArrayList<Long>();
			for (int i = 0; i < array.length; i++) {
				productCategoryIds.add(Long.parseLong(array[i]));
			}
			List<Filter> filters = new ArrayList<Filter>();
			List<Order> orders = new ArrayList<Order>();
			filters.add(Filter.in("id", productCategoryIds));
			orders.add(Order.desc("order"));
			model.addAttribute("rootProductCategories", productCategoryService.findList(12, filters, orders));

		}
		//获取自营供应商
		SysCode supplierSc = sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_SUPPLIER_IDS_KEY);
		if (supplierSc != null && StringUtils.isNotBlank(supplierSc.getValue())) {
			model.addAttribute("supplierIds", supplierSc.getValue());
		}
		Member member = getCurrent();
		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if (!StringUtil.isEmpty(excludeSuppliers)) {
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		model.addAttribute("company", company);
		if (company != null) {
			//		model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		return "/shop/product/self_shop";
	}

	/**
	 * @param model
	 * @param session
	 * @return
	 */
	@RequestMapping("/selfShopNew")
	public String selfShopNew(ModelMap model, HttpServletRequest request, HttpSession session) {
		// 增加判断是否移动端，移动端展示移动端页面
		try {
			if (CheckMobile.checkIsMobile(request)) {
				Setting setting = SettingUtils.get();
				return "redirect:" + setting.getSiteUrlwechat() + CheckMobile.getRequestURL(request);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		// end 移动端页面跳转
		// 获取热搜词
		SysCode sysCode = sysCodeService.findbyCode("hotSearch");
		String[] hotSearchs = null;
		if (sysCode != null) {
			hotSearchs = StringUtils.split(sysCode.getValue(), ",");
		}
		// 热搜词新增结束
		model.addAttribute("hotSearchs", hotSearchs);

		//获取自营的分类(怡亚通需要展示的一级分类id)
		SysCode eascsSysCode = sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_EASCS_ROOT_PRODUCT_CATEGORY_IDS_KEY);
		if (eascsSysCode != null && StringUtils.isNotBlank(eascsSysCode.getValue())) {
			String[] array = eascsSysCode.getValue().split(",");
			List<Long> productCategoryIds = new ArrayList<Long>();
			for (int i = 0; i < array.length; i++) {
				productCategoryIds.add(Long.parseLong(array[i]));
			}
			List<Filter> filters = new ArrayList<Filter>();
			List<Order> orders = new ArrayList<Order>();
			filters.add(Filter.in("id", productCategoryIds));
			orders.add(Order.desc("order"));
			model.addAttribute("eascsRootProductCategories", productCategoryService.findList(13, filters, orders));

		}

		//获取自营的分类(怡亚通需要展示的一级分类id)
		SysCode sc = sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_ROOT_PRODUCT_CATEGORY_IDS_KEY);
		if (sc != null && StringUtils.isNotBlank(sc.getValue())) {
			String[] array = sc.getValue().split(",");
			List<Long> productCategoryIds = new ArrayList<Long>();
			for (int i = 0; i < array.length; i++) {
				productCategoryIds.add(Long.parseLong(array[i]));
			}
			List<Filter> filters = new ArrayList<Filter>();
			List<Order> orders = new ArrayList<Order>();
			filters.add(Filter.in("id", productCategoryIds));
			orders.add(Order.desc("order"));
			model.addAttribute("rootProductCategories", productCategoryService.findList(12, filters, orders));

		}
		//获取自营供应商
		SysCode supplierSc = sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_SUPPLIER_IDS_KEY);
		if (supplierSc != null && StringUtils.isNotBlank(supplierSc.getValue())) {
			model.addAttribute("supplierIds", supplierSc.getValue());
		}
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if (!StringUtil.isEmpty(excludeSuppliers)) {
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		Member member = getCurrent();
		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}
		model.addAttribute("company", company);
		if (company != null) {
			//		model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		List<Coupon> coupons = couponService.getCouponsOrderByAmount();

		if (member != null) {
			if (coupons != null && coupons.size() > 0) {
				Coupon coupon = null;
				for (int i = 0; i < coupons.size(); i++) {
					coupon = coupons.get(i);
					Long count = couponCodeService.getReceivedCoupon(coupon, member);
					if (count != null && count > 0) {// 已经领取过
						coupon.setIsRecived(true);
					}
				}
			}
		}
		model.addAttribute("coupons", coupons);
//		model.addAttribute("coupons", couponService.getCouponsOrderByAmount());//优惠券信息
		return "/shop/product/self_shop_new";
	}


	@RequestMapping("/selfShopNew2")
	public String selfShopNew2(ModelMap model, HttpServletRequest request, HttpSession session) {
		// 增加判断是否移动端，移动端展示移动端页面
		try {
			if (CheckMobile.checkIsMobile(request)) {
				Setting setting = SettingUtils.get();
				return "redirect:" + setting.getSiteUrlwechat() + CheckMobile.getRequestURL(request);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		// end 移动端页面跳转
		// 获取热搜词
		String hotSearchStr = sysCodeService.getValue(SysCode.SYS_GROUP, "selfHotSearch");
		if (!StringUtil.isEmpty(hotSearchStr)) {
			String[] hotSearchs = StringUtils.split(hotSearchStr, ",");
			// 热搜词新增结束
			model.addAttribute("hotSearchs", hotSearchs);
		}

		//公告通知
		String noticeStr = sysCodeService.getValue(SysCode.SYS_GROUP, "selfShopNotice");
		if (!StringUtil.isEmpty(noticeStr)) {
			model.addAttribute("shopNotice", noticeStr);
		}

		//获取自营供应商
		SysCode supplierSc = sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_SUPPLIER_IDS_KEY);
		Long supplierId = null;
		if (supplierSc != null && StringUtils.isNotBlank(supplierSc.getValue())) {
			model.addAttribute("supplierIds", supplierSc.getValue());
			//取配置的首个供应商ID作为分类的供应商
			supplierId = Long.valueOf(supplierSc.getValue().split(",")[0]);
		}
		//供应商未配置，返回失败
		if (supplierId == null) {
			return ERROR_VIEW;
		}
//		//获取自营分类
//		List<Filter> filters=new ArrayList<Filter>();
//		List<Order> orders=new ArrayList<Order>();
//		filters.add(Filter.eq("supplierId", supplierId));
//		filters.add(Filter.eq("grade", 0));
//		filters.add(Filter.eq("enabledFlag", true));
//		orders.add(Order.asc("order"));
//		model.addAttribute("productCategories", productCategoryService.findList(null, filters, orders));

		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if (!StringUtil.isEmpty(excludeSuppliers)) {
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}

		Member member = getCurrent();
		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}
		model.addAttribute("company", company);

		//查询团购信息
		String code = null;
		if (company != null) {
			code = company.getGroupCode();
		}
		Group group = groupService.findValidGroup(code);
		if (group != null) {
			Long time = group.getEndDate().getTime() - new Date().getTime();
			model.addAttribute("time", time);
			List<GroupProduct> groupProducts = groupProductService.findListByGoods(8, group.getId());//根据商品规格合并返回
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			if (CollectionUtils.isNotEmpty(groupProducts)) {
				for (GroupProduct gp : groupProducts) {
					Map<String, Object> map = new HashMap<String, Object>();
					map.put("total", groupProductService.getGroupPurchaseCount(gp.getGroupId(), gp.getId()));
					map.put("groupProduct", gp);
					list.add(map);
				}
			}
			model.put("groupProducts", list);
			model.addAttribute("group", group);
		}
		//查询企业加价信息
		if (company != null) {
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}

		return "/shop/product/self_shop_new2";
	}


	@RequestMapping("/shop")
	public String shop(ModelMap model, HttpServletRequest request, HttpSession session) {
		// 增加判断是否移动端，移动端展示移动端页面
		try {
			if (CheckMobile.checkIsMobile(request)) {
				Setting setting = SettingUtils.get();
				return "redirect:" + setting.getSiteUrlwechat() + CheckMobile.getRequestURL(request);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		// end 移动端页面跳转
		// 获取热搜词
		String hotSearchStr = sysCodeService.getValue(SysCode.SYS_GROUP, "selfHotSearch");
		if (!StringUtil.isEmpty(hotSearchStr)) {
			String[] hotSearchs = StringUtils.split(hotSearchStr, ",");
			// 热搜词新增结束
			model.addAttribute("hotSearchs", hotSearchs);
		}

		//公告通知
		String noticeStr = sysCodeService.getValue(SysCode.SYS_GROUP, "selfShopNotice");
		if (!StringUtil.isEmpty(noticeStr)) {
			model.addAttribute("shopNotice", noticeStr);
		}

		//获取自营供应商
		SysCode supplierSc=sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_SUPPLIER_IDS_KEY);
		Long supplierId = null;
		if(supplierSc!=null&&StringUtils.isNotBlank(supplierSc.getValue())){
			model.addAttribute("supplierIds",supplierSc.getValue());
			//取配置的首个供应商ID作为分类的供应商
			supplierId = Long.valueOf(supplierSc.getValue().split(",")[0]);
		}
		//供应商未配置，返回失败
		if(supplierId==null){
			return ERROR_VIEW;
		}
//		//获取自营分类
//		List<Filter> filters=new ArrayList<Filter>();
//		List<Order> orders=new ArrayList<Order>();
//		filters.add(Filter.eq("supplierId", supplierId));
//		filters.add(Filter.eq("grade", 0));
//		filters.add(Filter.eq("enabledFlag", true));
//		orders.add(Order.asc("order"));
//		model.addAttribute("productCategories", productCategoryService.findList(null, filters, orders));

		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if (!StringUtil.isEmpty(excludeSuppliers)) {
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}

		Member member = getCurrent();
		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}
		model.addAttribute("company", company);

		//查询团购信息
		String code = null;
		if (company != null) {
			code = company.getGroupCode();
		}
		Group group = groupService.findValidGroup(code);
		if (group != null) {
			Long time = group.getEndDate().getTime() - new Date().getTime();
			model.addAttribute("time", time);
			List<GroupProduct> groupProducts = groupProductService.findListByGoods(8, group.getId());//根据商品规格合并返回
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			if (CollectionUtils.isNotEmpty(groupProducts)) {
				for (GroupProduct gp : groupProducts) {
					Map<String, Object> map = new HashMap<String, Object>();
					map.put("total", groupProductService.getGroupPurchaseCount(gp.getGroupId(), gp.getId()));
					map.put("groupProduct", gp);
					list.add(map);
				}
			}
			model.put("groupProducts", list);
			model.addAttribute("group", group);
		}
		//查询企业加价信息
		if (company != null) {
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}

		return "/shop/product/shop";
	}

	@RequestMapping("/freshShop")
	public String freshShop(ModelMap model, HttpServletRequest request, HttpSession session) {
		// 增加判断是否移动端，移动端展示移动端页面
		try {
			if (CheckMobile.checkIsMobile(request)) {
				Setting setting = SettingUtils.get();
				return "redirect:" + setting.getSiteUrlwechat() + CheckMobile.getRequestURL(request);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		// end 移动端页面跳转
		// 获取热搜词
		String hotSearchStr = sysCodeService.getValue(SysCode.SYS_GROUP, "selfHotSearch");
		if (!StringUtil.isEmpty(hotSearchStr)) {
			String[] hotSearchs = StringUtils.split(hotSearchStr, ",");
			// 热搜词新增结束
			model.addAttribute("hotSearchs", hotSearchs);
		}

		//查询标签ID集合
		SysCode sysCode = sysCodeService.findbyCode("freshType");
		String[] freshTypes = StringUtils.split(sysCode.getValue(), ",");
		Long[] tagIds = new Long[freshTypes.length];
		;
		for (int i = 0; i < freshTypes.length; i++) {
			tagIds[i] = Long.valueOf(freshTypes[i]);
		}
		List<Tag> tags = tagService.findList(tagIds);
		model.addAttribute("freshTypes", tags);
		model.addAttribute("supplierIds", "68"); //68:京东供应商

		//获取生鲜分类：11061
		List<Filter> filters = new ArrayList<Filter>();
		List<Order> orders = new ArrayList<Order>();
		filters.add(Filter.eq("parent", 11061));
		filters.add(Filter.eq("enabledFlag", true));
		orders.add(Order.asc("order"));
		List<ProductCategory> companyChildrenTwo = productCategoryService.findList(null, filters, orders);

		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if (!StringUtil.isEmpty(excludeSuppliers)) {
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}

		Member member = getCurrent();
		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}
		model.addAttribute("company", company);

		//查询企业加价信息
		if (company != null) {
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}

			// 获取企业限制二级分类
			if (!company.getCategoryAll()) {
				// 生鲜一级分类
				ProductCategory productCategory = productCategoryService.find(11061L);
				List<Long> companyCategoryId = productCategoryService.findCompanyCategory(company);
				companyChildrenTwo = productCategoryService.findCompanyCategoryChildren(productCategory, null, companyCategoryId);
			}
		}
		model.addAttribute("productCategories", companyChildrenTwo);

		return "/shop/product/fresh_shop";
	}

	// 获取推荐的一级分类
	@RequestMapping(value = "/get_hot_product_category_one", method = {
			RequestMethod.GET, RequestMethod.POST})
	public @ResponseBody
	List<ProductCategory> getHotProductCategoryOne(
			Integer productType) {
		Long[] hotProductCategoryIds = productHotService
				.getHotProductCategoryOne(6, productType);
		List<ProductCategory> productCategorys = productCategoryService
				.findList(hotProductCategoryIds);
		return productCategorys;
	}

	// 获取推荐的商品
	@RequestMapping(value = "/get_hot_products", method = {RequestMethod.GET,
			RequestMethod.POST})
	public @ResponseBody
	List<ProductHot> getHotProducts(
			Long productCategoryId, Integer productType) {
		List<ProductHot> hotProducts = new ArrayList<ProductHot>();
		hotProducts = productHotService.getHotProduct(productCategoryId, 6,
				productType);
		return hotProducts;
	}

	/**
	 * 商城楼层首页-自有商城 方法: 实现流程: 1. autor :sunjianwen
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping("/index_shop")
	public String indexShop(ModelMap model, HttpSession session) {
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}
		if (company != null) {
//			model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if (!StringUtil.isEmpty(excludeSuppliers)) {
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		model.addAttribute("company", company);
		return "/shop/product/index_shop";
	}


	/**
	 * 行云海外购首页 方法: 实现流程: 1. autor :sunjianwen
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping("/xingyun_index")
	public String xingyunIndex(ModelMap model) {
		Member member = getCurrent();
		List<Coupon> coupons = couponService.getCouponsOrderByAmount();
		if (member != null) {
			if (coupons != null && coupons.size() > 0) {
				Coupon coupon = null;
				for (int i = 0; i < coupons.size(); i++) {
					coupon = coupons.get(i);
					Long count = couponCodeService.getReceivedCoupon(coupon, member);
					if (count != null && count > 0) {// 已经领取过
						coupon.setIsRecived(true);
					}
				}
			}
		}
		model.addAttribute("coupons", coupons);
//		model.addAttribute("coupons", couponService.getCouponsOrderByAmount());
		return "/shop/xingyun/xingyun_index";
	}

	/**
	 * 方法：获取近期浏览商品信息 autor :penglong
	 *
	 * @param ids 商品ids
	 * @return
	 */
	@RequestMapping(value = "/history", method = RequestMethod.GET)
	public @ResponseBody
	List<Product> history(Long[] ids) {
		return productService.findList(ids);
	}

	/**
	 * 方法： 商品列表 实现流程： 1.根据商会员所在企业权限获取相应分类权限和供应商权限 2.根据平台促销标签等信息获取查询结果 autor
	 * :penglong
	 *
	 * @param productCategoryId 商品分类
	 * @param brandId           品牌id
	 * @param promotionId       促销id
	 * @param tagIds            标签id
	 * @param startPrice        最低价
	 * @param endPrice          最高价
	 * @param orderType         排序类型
	 * @param pageNumber        第几页
	 * @param pageSize          每页大小
	 * @param request
	 * @param model
	 * @param attributeValue5   5-精品商城商品
	 * @return
	 */
	@RequestMapping(value = "/list/{productCategoryId}", method = RequestMethod.GET)
	public String list(@PathVariable Long productCategoryId, Long brandId, String from,
					   Long promotionId, Long[] tagIds, BigDecimal startPrice, Long freshProductCategoryId,
					   BigDecimal endPrice, OrderType orderType, Integer pageNumber, Integer attributeValue5,
					   Integer pageSize, HttpServletRequest request, ModelMap model, HttpSession session) {
		// 增加判断是否移动端，移动端展示移动端页面
		try {
			if (CheckMobile.checkIsMobile(request)) {
				Setting setting = SettingUtils.get();
				return "redirect:" + setting.getSiteUrlwechat() + CheckMobile.getRequestURL(request);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		// end 移动端页面跳转

		ProductCategory productCategory = productCategoryService.find(productCategoryId);
		if (productCategory == null) {
			throw new ResourceNotFoundException();
		}

		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}
		List<Supplier> suppliers = new ArrayList<Supplier>();
		if (!company.getSupplierAll()) { // 如果非所有供应商开放获取相应有权限供应商 做in查询
			suppliers = supplierService.findCompanySupplier(company.getId());
			//suppliers = new ArrayList<Supplier>(company.getSuppliers());
		}
		if (!company.getCategoryAll()) { // 如果非所有商品分类开发查询相应有权限分类 做in查询
			// 动态获取公司下分类
			List<Long> companyCategory = productCategoryService.findCompanyCategory(company);

			productCategory.getChildren().clear();
			productCategory.setChildren(new HashSet<ProductCategory>(
					productCategoryService.findCompanyCategoryChildren(
							productCategory, null, companyCategory)));
		}
		Brand brand = brandService.find(brandId);
		Promotion promotion = promotionService.find(promotionId);
		List<Tag> tags = tagService.findList(tagIds);
//		Map<Attribute, String> attributeValue = new HashMap<Attribute, String>();
//		if (productCategory != null) {
//			Set<Attribute> attributes = productCategory.getAttributes();
//			for (Attribute attribute : attributes) {
//				String value = request.getParameter("attribute_"
//						+ attribute.getId());
//				if (StringUtils.isNotEmpty(value)
//						&& attribute.getOptions().contains(value)) {
//					attributeValue.put(attribute, value);
//				}
//			}
//		}
		Pageable pageable = new Pageable(pageNumber, 30);

//		if (StringUtils.isNotBlank(attributeValue5)) {
//			List<Filter> filters = pageable.getFilters();
//			filters.add(Filter.eq("attributeValue5", attributeValue5));
//		}
		model.addAttribute("from", from);
		model.addAttribute("freshProductCategoryId", freshProductCategoryId);
		model.addAttribute("orderTypes", OrderType.values());
		model.addAttribute("productCategory", productCategory);
		model.addAttribute("brand", brand);
		model.addAttribute("promotion", promotion);
		model.addAttribute("tags", tags);
		model.addAttribute("attributeValue5", attributeValue5);
		model.addAttribute("formURL", "/list/" + productCategoryId);
//		model.addAttribute("attributeValue", attributeValue);
		model.addAttribute("startPrice", startPrice);
		model.addAttribute("endPrice", endPrice);
		if (orderType == null) {
			//京东商品默认不排序
			if (productCategory.getSupplierId() == null ||
					!(productCategory.getSupplierId() == 68
							|| productCategory.getSupplierId() == 181)) {
				orderType = Product.OrderType.tagDesc;// 默认综合排序
			}
		}

		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if (!StringUtil.isEmpty(excludeSuppliers)) {
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		/*
		 * add by hzq
		 * 添加行云供应商
		 */
		model.addAttribute("supplierId", productCategory.getSupplierId());
		model.addAttribute("orderType", orderType);
		model.addAttribute("pageNumber", pageNumber);
		model.addAttribute("pageSize", pageSize);

		Supplier supplier = null;
		if (productCategory.getSupplierId() != null) { //供应商自有分类
			// 自营商城和怡亚通共用自营商城分类
			SysCode supplierSc = sysCodeService.findbyCode(SysCode.SYS_GROUP, CommonAttributes.SELF_SHOP_SUPPLIER_IDS_KEY);
			String[] supplierArr = supplierSc.getValue().split(",");
			Boolean isSelf = Arrays.asList(supplierArr).contains(String.valueOf(productCategory.getSupplierId()));
			if (isSelf && company.getCategoryAll()) {
				String supplierStr = supplierSc.getValue();
				Long[] supplierIds = new Long[supplierArr.length];
				for (int i = 0; i < supplierArr.length; i++) {
					supplierIds[i] = Long.parseLong(supplierArr[i]);
				}
				suppliers = supplierService.findList(supplierIds);
				model.addAttribute("supplierId", supplierStr);
			} else {
				supplier = supplierService.find(productCategory.getSupplierId());
			}
		}

		Page<Product> page = productService.findPage(productCategory,
				brand, supplier, suppliers, promotion, tags, "3",
				startPrice, endPrice, true, true, null, false, 
				orderType, pageable, company, true, null, null, null, attributeValue5!=null?String.valueOf(attributeValue5):null,null);
		List<Product> products = page.getContent();
		if(CollectionUtils.isNotEmpty(products)){
			String costPaymentFlag = (String)request.getSession().getAttribute("cost_payment_flag"); // 是否以成本价采购
			for(Product product:products){
				if(StringUtils.isNotEmpty(costPaymentFlag) && "1".equals(costPaymentFlag)){
					//查询是否有配置成本价加价比例
					SysCode sysCode = sysCodeService.findbyCode("company_" + company.getId(), CommonAttributes.ADD_COST_PRICE_RATE);
					if(sysCode!=null){
						product.setPrice((product.getCost().multiply(new BigDecimal(sysCode.getValue()))).setScale(2,BigDecimal.ROUND_HALF_UP));
					}else{
						product.setPrice(product.getCost());
					}
				}
			}
		}
		model.addAttribute("page", page);

		if (company != null) {
//			model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		model.addAttribute("company", company);

		//过滤分类
		if (attributeValue5!=null && attributeValue5==5) {
			productCategoryService.boutiqueCategorysFilter(productCategory.getChildren(), productCategory.getGrade() + 1, null);
		}

		//如果是第一层，则查询改分类下商品关联的所有brand
//		List<Long> brandIds = productService.findBrandIdsByCategory(productCategoryId, supplier);
//		List<Brand> brands = brandService.findList(brandIds.toArray(new Long[brandIds.size()]));
//		model.addAttribute("brandList",brands);

		return "/shop/product/list";
	}

	/**
	 * 方法： 商品列表 实现流程： 1.根据商会员所在企业权限获取相应分类权限和供应商权限 2.根据平台促销标签等信息获取查询结果 autor
	 * :penglong
	 *
	 * @param productCategoryId 商品分类
	 * @param brandId           品牌id
	 * @param promotionId       促销id
	 * @param tagIds            标签id
	 * @param orderType         排序类型
	 * @param pageNumber        第几页
	 * @param pageSize          每页大小
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/multiList", method = RequestMethod.GET)
	public String multiList(Long productCategoryId, Long brandId,
							Long promotionId, Long[] tagIds, Integer pageNumber,
							Integer pageSize, HttpServletRequest request, Long supplierId,
							OrderType orderType, String multiInfo, ModelMap model, HttpSession session) {
		String[] multiArr = multiInfo.split(";");
		List<Supplier> supplierList = new ArrayList<Supplier>();
		List<ProductCategory> categoryList = new ArrayList<ProductCategory>();
		Long sId = 0L;
		for (String multiStr : multiArr) {
			String[] infoArr = multiStr.split(",");
			Supplier itemSupplier = new Supplier();
			for (String item : infoArr) {
				String[] temp = item.split("=");
				if ("supplier".equals(temp[0])) {
					sId = Long.parseLong(temp[1].split("/")[0]);
					supplierList.add(supplierService.find(sId));
				} else if ("category".equals(temp[0])) {
					for (String catId : temp[1].split("/")) {
						categoryList.add(productCategoryService.find(Long.parseLong(catId)));
					}
				}

			}
		}

		Boolean firstFlag = false;

		List<Supplier> suppliers = new ArrayList<Supplier>();

		if (supplierId == null) {
			firstFlag = true;
			supplierId = supplierList.get(0).getId();
			suppliers.add(supplierList.get(0));
		}
		List<ProductCategory> oldCategoryList = new ArrayList<ProductCategory>();
		oldCategoryList.addAll(categoryList);
		if (productCategoryId == null) {
			firstFlag = true;
			for (ProductCategory temp : categoryList) {
				if (temp.getSupplierId() == supplierId) {
					productCategoryId = temp.getId();
					break;
				}
			}

		}


		ProductCategory productCategory = productCategoryService
				.find(productCategoryId);
		if (productCategory == null) {
			throw new ResourceNotFoundException();
		}
		if (!firstFlag) {
			suppliers.add(supplierService.find(supplierId));
			categoryList.removeAll(categoryList);
			categoryList.addAll(productCategory.getChildren());
		}

		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}

		model.addAttribute("company", company);
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if (!StringUtil.isEmpty(excludeSuppliers)) {
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}

		Brand brand = brandService.find(brandId);
		Promotion promotion = promotionService.find(promotionId);
		List<Tag> tags = tagService.findList(tagIds);
		Map<Attribute, String> attributeValue = new HashMap<Attribute, String>();

		Pageable pageable = new Pageable(pageNumber, 30);


		model.addAttribute("productCategory", productCategory);
		model.addAttribute("brand", brand);
		model.addAttribute("promotion", promotion);
		model.addAttribute("tags", tags);
		//		model.addAttribute("formURL", "/multiList/" + productCategoryId);
		//		model.addAttribute("attributeValue", attributeValue);
		//		model.addAttribute("startPrice", startPrice);
		//		model.addAttribute("endPrice", endPrice);
		if (orderType == null) {
			orderType = Product.OrderType.topDesc;// 默认综合排序
		}
		/*
		 * add by hzq
		 * 添加行云供应商
		 */
		model.addAttribute("supplierId", productCategory.getSupplierId());
		model.addAttribute("orderType", orderType);
		model.addAttribute("pageNumber", pageNumber);
		model.addAttribute("pageSize", pageSize);
		model.addAttribute("page", productService.findPage(productCategory,
				brand, null, suppliers, promotion, tags, "3",
				null, null, true, true, null, false,
				orderType, pageable, company, true, null, null));

		model.addAttribute("supplierList", supplierList);
		model.addAttribute("categoryList", categoryList);
		model.addAttribute("multiInfo", multiInfo);
		model.addAttribute("oldCategoryList", oldCategoryList);

		if (company != null) {
//			model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		return "/shop/product/multi_list";
	}

	/**
	 * 方法： 商品列表 autor :penglong
	 *
	 * @param brandId
	 * @param promotionId
	 * @param tagIds
	 * @param startPrice
	 * @param endPrice
	 * @param orderType
	 * @param pageNumber
	 * @param pageSize
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(Long brandId, Long promotionId, Long[] tagIds,
					   BigDecimal startPrice, BigDecimal endPrice, OrderType orderType,
					   Integer pageNumber, Integer pageSize, HttpServletRequest request,
					   ModelMap model) {
		Company company = memberService.getCurrent().getCompanyId();
		List<Supplier> suppliers = new ArrayList<Supplier>();
		if (!company.getSupplierAll()) {
			suppliers = supplierService.findCompanySupplier(company.getId());
			//suppliers = new ArrayList<Supplier>(company.getSuppliers());
		}
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if (!StringUtil.isEmpty(excludeSuppliers)) {
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		Brand brand = brandService.find(brandId);
		Promotion promotion = promotionService.find(promotionId);
		List<Tag> tags = tagService.findList(tagIds);
		Pageable pageable = new Pageable(pageNumber, pageSize);
		model.addAttribute("orderTypes", OrderType.values());
		model.addAttribute("brand", brand);
		model.addAttribute("promotion", promotion);
		model.addAttribute("tags", tags);
		model.addAttribute("startPrice", startPrice);
		model.addAttribute("endPrice", endPrice);
		model.addAttribute("orderType", orderType);
		model.addAttribute("pageNumber", pageNumber);
		model.addAttribute("pageSize", pageSize);
		model.addAttribute("formURL", "list");
		model.addAttribute("page", productService.findPage(null, brand, null,
				suppliers, promotion, tags, "3", startPrice, endPrice,
				true, true, null, false, orderType, pageable, company, true, null, null));
		return "/shop/product/list";
	}

	/**
	 * 方法：商品搜索 实现流程： 1.获取该会员公司商品分类权限、供应商权限 2.调用hibernate search 索引搜索方法获取搜索结果
	 * autor :penglong
	 *
	 * @param keyword
	 * @param startPrice
	 * @param endPrice
	 * @param orderType
	 * @param pageNumber
	 * @param pageSize
	 * @param model
	 * @return
	 */
	/*@RequestMapping(value = "/search", method = {RequestMethod.GET,RequestMethod.POST})
	public String search(String keyword, BigDecimal startPrice,
			BigDecimal endPrice, OrderType orderType, Long productCategoryId,
			Integer pageNumber, Integer pageSize, HttpServletRequest request,
			ModelMap model, Boolean isDiscount, Long minDiscount,
			Long maxDiscount,Long supplierId) {
		if (StringUtils.isEmpty(keyword)) {
			model.addAttribute("page", new Page());
			model.addAttribute("productCategories", null);
			model.addAttribute("filterProductCategory", null);
			model.addAttribute("supplierId", supplierId);
			return "shop/product/search";
		}
		List<Supplier> suppliers = new ArrayList<Supplier>();
		List<ProductCategory> categories = new ArrayList<ProductCategory>();
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			company = companyService.getDefultCompany();
		}

		if (!company.getSupplierAll()) {
			suppliers = new ArrayList<Supplier>(company.getSuppliers());
		}
		if (!company.getCategoryAll()) {
			categories = new ArrayList<ProductCategory>(
					company.getProductCategories());
		}

		if (orderType == null) {
			orderType = Product.OrderType.topDesc;// 默认综合排序
		}
		pageSize = 30;// 搜索一页展示30个商品
		Pageable pageable = new Pageable(pageNumber, pageSize);
		model.addAttribute("orderTypes", OrderType.values());
		model.addAttribute("productKeyword", keyword);
		model.addAttribute("startPrice", startPrice);
		model.addAttribute("endPrice", endPrice);
		model.addAttribute("orderType", orderType);
		model.addAttribute("formURL", "search");
		model.addAttribute("keyword", keyword);
		model.addAttribute("isDiscount", isDiscount);
		model.addAttribute("minDiscount", minDiscount);
		model.addAttribute("maxDiscount", maxDiscount);

		ProductCategory productCategory = productCategoryService
				.find(productCategoryId);
		Integer grade = null;
		if (productCategory != null) {
			grade = productCategory.getGrade();
			if( productCategory.getParent()!=null){
				model.addAttribute("productCategoryTwo", productCategory.getParent());
				model.addAttribute("productCategoryOne",productCategory.getParent().getParent());
			}

		}
		Page<Product> page = searchService.search(keyword, startPrice,
				endPrice, orderType, suppliers, categories, productCategoryId,
				grade, pageable, isDiscount, minDiscount, maxDiscount,supplierId);

		List<ProductCategory> list = new ArrayList<ProductCategory>();
		list = productCategoryService.findList(page.getFacets());

		// 新增搜索日志记录
		if (page.getPageNumber() == 1) {
			try {
				LogSearchMsgBean logSearchMsgBean = new LogSearchMsgBean();
				if (member != null) {
					logSearchMsgBean.setMemberId(member.getId());
				}
				logSearchMsgBean.setKeyword(keyword);
				logSearchMsgBean.setDeviceType(0);
				logSearchMsgBean.setTotal(page.getTotal());
				logSearchMsgBean.setIp(CommUtil.getIpAddr(request));
				logSearchMsgBean.setCreateDate(new Date());

				producerService
						.sendMessage(destinationSearch, logSearchMsgBean);// 发送消息
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		// 搜索日志记录完成

		model.addAttribute("page", page);
		model.addAttribute("productCategories", list);
		model.addAttribute("filterProductCategory", productCategory);
		model.addAttribute("supplierId", supplierId);
		return "shop/product/search";
	}*/

	/**
	 * 方法：商品搜索 实现流程： 1.获取该会员公司商品分类权限、供应商权限 2.调用hibernate search 索引搜索方法获取搜索结果
	 * autor :penglong
	 *
	 * @param keyword
	 * @param startPrice
	 * @param attributeValue5 5-精选商城商品
	 * @param endPrice
	 * @param orderType
	 * @param pageNumber
	 * @param pageSize
	 * @param model
	 * @param
	 * @return
	 */
	@RequestMapping(value = "/search", method = RequestMethod.GET)
	public String queryProducts(String keyword, BigDecimal startPrice, String attributeValue5,
								String from, BigDecimal endPrice, OrderType orderType, String productCategoryId,
								Integer pageNumber, Integer pageSize, ModelMap model, Boolean isDiscount, Long minDiscount,
								Long maxDiscount, String supplierId, String productCategoryName, String brandName, Integer type,
								HttpServletRequest request, HttpSession session) {

		// 增加判断是否移动端，移动端展示移动端页面
		try {
			if (CheckMobile.checkIsMobile(request)) {
				Setting setting = SettingUtils.get();
				return "redirect:" + setting.getSiteUrlwechat() + CheckMobile.getRequestURL(request);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		// end 移动端页面跳转
		model.addAttribute("from", from);
		model.addAttribute("formURL", "search");
		if (StringUtils.isEmpty(keyword)) {
			model.addAttribute("page", new Page());
			model.addAttribute("productCategories", null);
			model.addAttribute("filterProductCategory", null);
			model.addAttribute("supplierId", supplierId);
			return "shop/product/search";
		}
		//将搜索关键字限制长度
		if (keyword.length() > 50) {
			keyword = keyword.substring(0, 50) + "...";
		}
		if (productCategoryName != null) {
			productCategoryName = StringEscapeUtils.unescapeHtml(productCategoryName);
		}
		if (brandName != null) {
			brandName = StringEscapeUtils.unescapeHtml(brandName);
		}
		if (supplierId != null) {
			supplierId = StringEscapeUtils.unescapeHtml(supplierId);
		}
		if (productCategoryId != null) {
			productCategoryId = StringEscapeUtils.unescapeHtml(productCategoryId);
		}
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}
		pageSize = 30;// 搜索一页展示30个商品
		Pageable pageable = new Pageable(pageNumber, pageSize);
		model.addAttribute("orderTypes", OrderType.values());
		model.addAttribute("productKeyword", keyword);
		model.addAttribute("startPrice", startPrice);
		model.addAttribute("endPrice", endPrice);
		model.addAttribute("orderType", orderType);
		model.addAttribute("keyword", keyword);
		model.addAttribute("isDiscount", isDiscount);
		model.addAttribute("minDiscount", minDiscount);
		model.addAttribute("maxDiscount", maxDiscount);
		keyword = StringEscapeUtils.unescapeHtml(keyword);
		//---每日优鲜商品过滤城市
		String cityId = null;
		if (supplierId != null && supplierId.indexOf(",") == -1 && supplierService.exists(Filter.eq("id", Long.parseLong(supplierId)), Filter.eq("isOweOrder", 13))) {
			cityId = areaService.getCurrentCityId(true);
		}
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if (!StringUtil.isEmpty(excludeSuppliers)) {
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		Page<Product> page = searchService.searchByOpenSearch(keyword, startPrice, endPrice, orderType, pageable, isDiscount, supplierId, productCategoryName, productCategoryId, brandName, minDiscount, maxDiscount, cityId, null, company, null, attributeValue5,null);
		model.addAttribute("page", page);
		model.addAttribute("productCategoryId", StringEscapeUtils.escapeHtml(productCategoryId));
		model.addAttribute("supplierId", StringEscapeUtils.escapeHtml(supplierId));
		model.addAttribute("brandNames", page.getBrandNames());
		model.addAttribute("type", type);
		model.addAttribute("attributeValue5", attributeValue5);
		model.addAttribute("productCategoryName", productCategoryName);
		model.addAttribute("brandName", brandName);
		model.addAttribute("company", company);
		if (company != null) {
			//	model.addAttribute("supplierPriceMap",company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		return "shop/product/search";
	}

	/**
	 * 方法:异步获取商品点击量 autor :penglong
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/hits/{id}", method = RequestMethod.GET)
	public @ResponseBody
	Long hits(@PathVariable Long id,
			  HttpServletRequest request) {
		// 新增 20170317 记录登录日志 发送MQ异步进行保存操作
		Member member = memberService.getCurrent();
		try {
			LogProductMsgBean logProductMsgBean = new LogProductMsgBean();
			if (member != null) {
				logProductMsgBean.setCompanyId(member.getCompanyId().getId());
				logProductMsgBean.setMemberId(member.getId());
			}

			logProductMsgBean.setHitIp(CommUtil.getIpAddr(request));
			logProductMsgBean.setHitType(0);
			logProductMsgBean.setProductId(id);
			logProductMsgBean.setCreateDate(new Date());
			producerService.sendMessage(destination, logProductMsgBean);// 发送消息
		} catch (Exception e) {
			e.printStackTrace();
		}

		// 异步发送登录日志结束

		return 1L;
		// return productService.viewHits(id);
	}

	/**
	 * 方法:产品详情页 <br/>
	 * 实现流程  : 1.获取企业个性化域名标志<br/>
	 * 2.获取产品静态页面连接<br/>
	 * 3.如果产品静态页面不存在并且企业个性化域名标志为false则动态生成产品详情页，否则跳转到产品详情静态页面<br/>
	 *
	 * @param id      产品id
	 * @param sn
	 * @param request
	 * @param model
	 * @return autor : 吴锭超
	 */
	@RequestMapping(value = "/productContent/{sn}/{id}", method = RequestMethod.GET)
	public String productContent(@PathVariable Long id, @PathVariable Long sn, Long companyId,
								 String from, HttpServletRequest request, ModelMap model) {
		Member member = getCurrent();

		Company company = null;
		if (member != null) {
			//判断是否是集采账号
			String companyPurchase = (String)  request.getSession().getAttribute("com_person_moreAtts_company_purchase");
			if("open".equals(companyPurchase) && purchaseListService.getPurchaseFlagSession(request)){
				// 如果原先是集采配置，但是hr端关闭或删除了，需要去除集采配置
				if (purchaseListService.getPurchaseFlagSession(request) && !purchaseListService.isPurchaseAccount(member.getId())) {
					request.getSession().setAttribute(CommonAttributes.PURCHASE_MEMBER, false);
				}else {
					model.addAttribute("checkPurchaseMember", true);
					//是否有清单
					List<Filter> filters=new ArrayList<Filter>();
					filters.add(Filter.ne("status",-1));
					filters.add(Filter.eq("enabledFlag",true));
					filters.add(Filter.eq("createUser",member.getId()));
					List<PurchaseList> list = purchaseListService.findList(null, filters, null);
					if(list!=null && !list.isEmpty()) {
						model.addAttribute("purchaseLists", list);
					}
				}
			}else{
				model.addAttribute("checkPurchaseMember", false);
			}
			company = member.getCompanyId();
			model.addAttribute("companyId", company.getId());
		} else {
			String companyIdStr = (String) request.getSession().getAttribute("companyIdStr");
			if (StringUtils.isBlank(companyIdStr)) {
				companyIdStr = request.getParameter("companyIdStr");
			}
			logger.info("session中保存的companyIdStr=" + companyIdStr);
			if (StringUtils.isNotEmpty(companyIdStr)) {
				company = companyService.find(Long.valueOf(companyIdStr));
			} else {
				if (companyId != null) {//带了企业id
					company = companyService.find(companyId);
					if (company != null) {//获取到指定的企业
						companyIdStr = company.getId().toString();
					}
				} else {
					company = companyService.getDefultCompany();
					companyIdStr = company.getId().toString();
				}
			}
			model.addAttribute("companyId", companyIdStr);
		}
		model.addAttribute("from", from);
		model.addAttribute("company", company);
		Product product = productService.find(id);
		if (product == null) {
			throw new ResourceNotFoundException();
		}
		Long memberId = -1L;
		if (member != null) {
			memberId = member.getId();
		}

		//发送mq同步商品价格
		if (product.getSupplierId() != null && product.getSupplierId().getIsOweOrder() != null) { //京东商品
			if (product.getSupplierId().getIsOweOrder() == 1 || product.getSupplierId().getIsOweOrder() == 18 || product.getSupplierId().getIsOweOrder() == 32
					|| product.getSupplierId().getIsOweOrder() == 2) {
				try {
					jdFeedbackService.sendMq(product.getId(), memberId, 2);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}

		//企业个性化域名标志
		Boolean isComPersonConf = (Boolean) request.getSession().getAttribute(Constants.COM_PERSON_CONF);
		if (isComPersonConf == null) isComPersonConf = Boolean.FALSE;
		model.addAttribute("product", product);
		if (company.getSupplierPriceRate() != null) {  //供应商加价
			//	model.addAttribute("supplierPriceMap", company.getSupplierPriceRateMap());
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		groupService.putGroupInfo(model, product, company);
		model.addAttribute("defaultPromotion", promotionService.getPromotionByProduct(product.getId()));
		model.addAttribute("defaultGroup", groupService.getGroupByProduct(product.getId()));
		//是否是虚拟商品
		model.addAttribute("isVirtual", product.getSupplierId().getIsVirtualSupplier());

		return "/shop/product/content_main_content";

	}

	/**
	 * 方法: 商品详情页 实现流程： 1.获取该商品静态路径判断是否存在，存在直接返回静态页面 2.不存在则查询商品信息返回动态页面 autor<br/>
	 * 实现流程  : <br/>
	 *
	 * @param id        商品id
	 * @param actId     活动id
	 * @param sn
	 * @param companyId 企业id
	 * @param request
	 * @param model
	 * @return 编码作者 : 吴锭超
	 * 完成日期 : 2020-08-13 17:30:20
	 */
	@RequestMapping(value = "/content/{companyId}/{sn}/{id}", method = RequestMethod.GET)
	public String content4company(@PathVariable Long id, Long actId, @PathVariable Long sn, @PathVariable Long companyId,
								  HttpServletRequest request, ModelMap model) {

		StringBuffer redirectUri = new StringBuffer("/product/content/");
		redirectUri.append(sn).append("/").append(id).append(".jhtml?companyId=").append(companyId);

		try {
			if (actId != null) {
				redirectUri.append("&actId=").append(actId);
			}
			if (CheckMobile.checkIsMobile(request)) {// 增加判断是否移动端，移动端展示移动端页面
				Setting setting = SettingUtils.get();
				return "redirect:" + setting.getSiteUrlwechat() + redirectUri.toString();
			} else {
				return "redirect:" + redirectUri.toString();
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return ERROR_VIEW;

	}

	/**
	 * 方法:商品详情页 实现流程： 1.获取该商品静态路径判断是否存在，存在直接返回静态页面 2.不存在则查询商品信息返回动态页面 autor
	 * :penglong
	 *
	 * @param id
	 * @param sn
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/content/{sn}/{id}", method = RequestMethod.GET)
	public String content(@PathVariable Long id, Long actId, @PathVariable Long sn, Long companyId, String attributeValue5,
						  String from, HttpServletRequest request, ModelMap model) {

		// 增加判断是否移动端，移动端展示移动端页面
		try {
			if (CheckMobile.checkIsMobile(request)) {
				Setting setting = SettingUtils.get();
				return "redirect:" + setting.getSiteUrlwechat() + CheckMobile.getRequestURL(request);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		// end 移动端页面跳转

		model.addAttribute("productPath", sn + "/" + id);
		model.addAttribute("actId", actId);
		model.addAttribute("attributeValue5", attributeValue5);
		model.addAttribute("from", from);


		// staticService.build(product);
		Product product = productService.find(id);
		if (product == null) {
			throw new ResourceNotFoundException();
		}
		if (product.getSupplierId() != null
				&& product.getSupplierId().getId() == 68L) {// 京东商品
			model.addAttribute("flag", 1);
		} else if (product.getSupplierId() != null
				&& product.getSupplierId().getId() == 181L) {// 京东图书
			model.addAttribute("flag", 2);
		} else {
			model.addAttribute("flag", 0);
		}
		Boolean isVirtualSupplier = product.getSupplierId().getIsVirtualSupplier();//是否属于虚拟供应商商品
		model.addAttribute("isVirtualSupplier", isVirtualSupplier);
		model.addAttribute("product", product);

		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if (!StringUtil.isEmpty(excludeSuppliers)) {
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}

		Member member = memberService.getCurrent();

		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
			model.addAttribute("companyId", company.getId());

			//若未传活动ID则获取
			if (actId == null) {
				actId = actProductService.findConvertAct(product.getId(), company.getId());
				/*Set<ActProduct> actProducts = product.getActProducts();
				if (actProducts != null && actProducts.size() > 0) {
					for (ActProduct actProduct : actProducts) {
						if (actProduct.getAct().getType() == Act.ActType.convert) {
							if (actProduct.getActProductCompanyIds() != null && actProduct.getActProductCompanyIds().length() > 0) {
								String[] companyIds = actProduct.getActProductCompanyIds().split(",");
								if (Arrays.asList(companyIds).contains(company.getId().toString())) {
									//取满足条件的最大ID
									if (actId == null || (actId != null && actProduct.getAct().getId() > actId)) {
										actId = actProduct.getAct().getId();
									}
								}
							}
						}
					}
				}*/
				model.addAttribute("actId", actId);
			}

		} else {
			String companyIdStr = (String) request.getSession().getAttribute("companyIdStr");
			if (StringUtils.isBlank(companyIdStr)) {
				companyIdStr = request.getParameter("companyIdStr");
			}
			logger.info("session中保存的companyIdStr=" + companyIdStr);
			if (StringUtils.isNotEmpty(companyIdStr)) {
				company = companyService.find(Long.valueOf(companyIdStr));
			} else {
				if (companyId != null) {//带了企业id
					company = companyService.find(companyId);
					if (company != null) {//获取到指定的企业
						companyIdStr = company.getId().toString();
					}
				} else {
					company = companyService.getDefultCompany();
					companyIdStr = company.getId().toString();
				}
			}
			model.addAttribute("companyId", companyIdStr);
		}
		model.addAttribute("company", company);
		model.addAttribute("groupProduct", groupProductService.findByProductId(product.getId(), company));
		try {
			Long memberId = null;
			if (member != null) {
				memberId = member.getId();
			}
			if (product.getSupplierId() != null && product.getSupplierId().getIsOweOrder() != null) { //京东商品
				if (product.getSupplierId().getIsOweOrder() == 1 || product.getSupplierId().getIsOweOrder() == 18) {
					jdFeedbackService.sendMq(product.getId(), memberId, 4);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return "/shop/product/content";

	}

	/**
	 * 获取活动优惠券
	 *
	 * @param actId     活动ID
	 * @param productId 商品Id
	 * @return
	 */
	@RequestMapping(value = "/getActCoupons")
	public String getActCoupons(Long actId, Long productId, Integer count, ModelMap model) {
		try {
			Product product = productService.find(productId);
			model.put("convertCoupons", couponService.findConvertCoupon(actId, getCurrent(), product, count));
			if (getCurrent()!=null){
				model.put("companyId", getCurrent().getCompanyId().getId());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "/shop/product/act_coupon";
	}

	/**
	 * 方法：异步返回该会员是否收藏商品 autor :penglong
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody
	Message check(Long id) {
		Product product = productService.find(id);
		Member member = memberService.getCurrent();
		if (product == null || member == null) {
			return ERROR_MESSAGE;
		}
		if (!memberFavoriteProductService.exists(product, member)) {
			return Message.warn("");
		}

		return Message.success("");
	}

	/**
	 * 方法:异步获取商品评分信息 实现流程： 1.分别查询商品各分数评分数量 2.计算好评中评差评比例 autor :penglong
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/reviewCount", method = RequestMethod.POST)
	public @ResponseBody
	Map reviewCount(Long id) {
		Map<String, Float> map = new HashMap<String, Float>();
		Product product = productService.find(id);
		Long graph1 = reviewService.count(null, product, 1, true);
		Long graph2 = reviewService.count(null, product, 2, true);
		Long graph3 = reviewService.count(null, product, 3, true);
		Long graph4 = reviewService.count(null, product, 4, true);
		Long graph5 = reviewService.count(null, product, 5, true);
		Long total = reviewService.count(null, product, null, true);
		map.put("score2", (float) (Math.round(graph1.longValue() * 100)) / 100);
		map.put("score3", (float) (Math.round((graph2.longValue() + graph3
				.longValue()) * 100)) / 100);
		map.put("score5", (float) (Math.round((graph5.longValue() + graph4
				.longValue()) * 100)) / 100);
		;
		map.put("total", (float) (Math.round(total * 100)) / 100);
		float a = ((graph5.floatValue() + graph4.floatValue()) / total) * 100;
		map.put("scoregood", (float) (Math.round(a * 100)) / 100);

		return map;
	}

	/**
	 * 方法:异步刷新商品信息 实现流程： 1.根据用户切好城市id获取商品是否支持销售 autor :penglong
	 *
	 * @param id
	 * @param areaId
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/sale_area", method = RequestMethod.POST)
	public @ResponseBody
	Map<String, Object> saleArea(Long id, Long areaId,
								 HttpServletRequest request, HttpServletResponse response) {
		Map<String, Object> data = new HashMap<String, Object>();
		Product product = productService.find(id);
		String isShip = product.getIsShip() ? (1 + "") : (0 + "");// 商品是否免邮
		data.put("isShip", isShip);
		if (!product.getIsMarketable()) {
			data.put("message", Message.error("此商品已下架"));
			return data;
		}
		// 调用优鲜校验接口
		if (product.getSupplierId().getIsOweOrder() == 13) {
			if (!missfreshService.isSaleArea(product, areaId)) {
				data.put("message", Message.error("此地区缺货"));
				return data;
			}

		}
		//xl20171204网易严选校验库存
		if (product.getSupplierId().getIsOweOrder() == 8) {
			Long stock = yanxuanService.getYanxuanProductStock(product);
			if (stock == null || stock < 1) {
				data.put("message", Message.error("此地区缺货"));
				return data;
			}
		}
		//行云校验库存
		else if (product.getSupplierId().getIsOweOrder() == 2) {
			if (!productService.checkStock(product, 0)) {
				data.put("message", Message.error("此地区缺货"));
				return data;
			}
			if (!productService.isSaleArea(product, areaId)) {
				data.put("message", Message.error("暂不支持此地区销售"));
				return data;
			}
		}

		// 调用京东校验接口 modify by hzq  添加行云校验
		if (product.getSupplierId().getIsOweOrder() == 1) {
			if (jdService.checkProductsForList(product, areaId)) {
				data.put("message", Message.error("此地区缺货"));
				return data;
			}

			List<StockVO> stockVOs = new ArrayList<>();
			StockVO stockVO = new StockVO();
			stockVO.setSkuId(product.getJdSku());
			stockVO.setNum(1);
			stockVOs.add(stockVO);
			if (!jdService.checkAreaLimitForProductDetail(stockVOs, product, areaId)) { //校验区域购买限制
				data.put("message", Message.error("此地区缺货"));
				return data;
			}

		}
		// 调用fliPlus校验规则
		else if (product.getSupplierId().getIsOweOrder() == 0) {
			if (!productService.checkStock(product, 0)) {
				data.put("message", Message.error("缺货"));
				return data;
			}
			if (!productService.isSaleArea(product, areaId)) {
				data.put("message", Message.error("暂不支持此地区销售"));
				return data;
			}
		}
		// 京东健康调用库存查询接口
		else if(product.getSupplierId().getIsOweOrder()==61){
			Member member = memberService.getCurrent();
			Company company = null;
			if (member != null) {
				company = memberService.getCurrent().getCompanyId();
			} else {
				HttpSession session=request.getSession();
				String companyIdStr=(String) session.getAttribute("companyIdStr");
				if(companyIdStr!=null){
					Long companyId=Long.valueOf(companyIdStr);
					company=companyService.find(companyId);
					if (company != null) {
						logger.info("companyId="+company.getId());
					}
				}else{
					company = companyService.getDefultCompany();
				}
			}
			List<Product> list = new ArrayList<>();
			list.add(product);
			Map<Long, Boolean> map = jdHealthService.checkStock(company.getId(),list, areaId);
			if (map.get(product.getId())) {
				data.put("message", Message.error("此地区缺货"));
				return data;
			}
		}
		// 苏州通卡
		else if (product.getSupplierId().getIsOweOrder() == 65){
			if (!jstkService.checkStock(product,0)) {
				data.put("message", Message.error("缺货"));
				return data;
			}
		}
		// 天猫
		else if (product.getSupplierId().getIsOweOrder() == CommonAttributes.TMALL_SUPPLIER_ISOWEORDER) {
			List<Product> list = new ArrayList<>();
			list.add(product);
			Map<Long, Boolean> map = tMallService.checkStock(list, areaId);
			if (map.get(product.getId())) {
				data.put("message", Message.error("此地区缺货"));
				return data;
			}
		}
		data.put("message", SUCCESS_MESSAGE);

		return data;
	}

	/**
	 * 方法：异步刷新静态页面商品动态信息 实现流程： 1。刷新用户默认收货地址 2.刷新商品库存等信息 3.刷新企业简称信息 autor
	 * :penglong
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/getMemberCus", method = RequestMethod.POST)
	public @ResponseBody
	Map<String, Object> getDateTime(Long id, Long companyId,
									HttpServletRequest request) {
		Map<String, Object> data = new HashMap<String, Object>();
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			String companyIdStr = (String) request.getSession().getAttribute("companyIdStr");
			logger.info("session中保存的companyIdStr=" + companyIdStr);
			if (StringUtils.isNotEmpty(companyIdStr)) {
				company = companyService.find(Long.valueOf(companyIdStr));
			} else {
				if (companyId != null) {//带了企业id
					company = companyService.find(companyId);
					if (company == null) {//获取到指定的企业
						company = companyService.getDefultCompany();
					}
				} else {
					company = companyService.getDefultCompany();
				}
			}
		}

		Product product = productService.find(id);
		BigDecimal supplierPriceRate = new BigDecimal(1);//根据供应商id获取加价策略
		BigDecimal supplierMarketPriceRate = new BigDecimal(1);//根据供应商id获取市场价加价策略
		String supplierPriceRateJson = company.getSupplierPriceRuleBySupplierId(product.getSupplierId().getId());
		JSONObject jsonObject = JSONObject.parseObject(supplierPriceRateJson);
		if (jsonObject != null) {
			supplierPriceRate = jsonObject.getBigDecimal("price");
			supplierMarketPriceRate = jsonObject.getBigDecimal("market_price");
		}
		if (product != null) {
			
	        /*
			if(supplierPriceRate!=null){
				BigDecimal productPrice=product.getPrice().multiply(supplierPriceRate). setScale( 2, BigDecimal.ROUND_HALF_UP );
				BigDecimal marketPrice=product.getMarketPrice().multiply(supplierMarketPriceRate). setScale( 2, BigDecimal.ROUND_HALF_UP );
				data.put("productPrice", productPrice.toString());
				data.put("productSavePrice",
						marketPrice.subtract(productPrice).toString());
				data.put("marketPrice", marketPrice.toString());
			}else{
				data.put("productPrice", product.getPrice().toString());
				data.put("productSavePrice",
						product.getMarketPrice().subtract(product.getPrice())
						.toString());
				data.put("marketPrice", product.getMarketPrice().toString());
			}
			*/
			String costPaymentFlag = (String)request.getSession().getAttribute("cost_payment_flag"); // 是否以成本价采购
			AddPriceProductPageVo productAddPrice = productAddPriceService.getProductAddPrice(product, company);
			if (productAddPrice != null) {
				BigDecimal productPrice = productAddPrice.getPrice();
				BigDecimal marketPrice = productAddPrice.getMarketPrice();

				data.put("productPrice", productPrice.toString());
				if(StringUtils.isNotEmpty(costPaymentFlag) && "1".equals(costPaymentFlag)){
					//查询是否有配置成本价加价比例
					SysCode sysCode = sysCodeService.findbyCode("company_" + company.getId(), CommonAttributes.ADD_COST_PRICE_RATE);
					if(sysCode!=null){
						data.put("productPrice",(product.getCost().multiply(new BigDecimal(sysCode.getValue()))).setScale(2,BigDecimal.ROUND_HALF_UP));
					}else{
						data.put("productPrice",product.getCost().toString());
					}
				}
				data.put("productSavePrice", marketPrice.subtract(productPrice).toString());
				data.put("marketPrice", marketPrice.toString());
			} else {
				data.put("productPrice", product.getPrice().toString());
				data.put("productSavePrice", product.getMarketPrice().subtract(product.getPrice()).toString());
				data.put("marketPrice", product.getMarketPrice().toString());
			}
			data.put("hits", String.valueOf(product.getHits() + 20L));
			data.put("productTitle", product.getName());
			if (member != null) {
				data.put("companyId", member.getCompanyId().getId() + "");
			} else {
				data.put("companyId", null);
			}

			if(StringUtils.isNotEmpty(costPaymentFlag) && "1".equals(costPaymentFlag)){
				product.setPrice(product.getCost());
			}
			data.put("product", product);
		}

		// 获取cookie
		String provinceId = WebUtils.getCookie(request, "provinceIdCookie");
		String cityId = WebUtils.getCookie(request, "cityIdCookie");
		String areaId = WebUtils.getCookie(request, "areaIdCookie");
		String areaName = WebUtils.getCookie(request, "cityCookie");
		String companyAbbreviation = "福利PLUS专享";

		if (member != null && member.getCompanyId() != null) { //用户已登录

			if (StringUtil.isEmpty(areaId) || StringUtil.isEmpty(areaName)
					|| StringUtil.isEmpty(provinceId)
					|| StringUtil.isEmpty(cityId)) {
				Receiver receiver = receiverService.findDefault(member);
				if (receiver != null) {
					areaName = receiver.getAreaName();
					areaId = receiver.getArea().getId().toString();
					Area area = receiver.getArea();
					String treePath = area.getTreePath();
					if (treePath != null) {
						treePath = treePath.substring(1, treePath.length() - 1);
						String[] treePathArr = CommUtil.splitByChar(treePath,
								",");

						if (treePathArr.length >= 2) {
							provinceId = treePathArr[0];
							cityId = treePathArr[1];
						}
					}
				}
			}
			companyAbbreviation = member.getCompanyId()
					.getCompanyAbbreviation() + "专享";
		} else {
			//企业个性化域名标志
			Boolean isComPersonConf = (Boolean) request.getSession().getAttribute(Constants.COM_PERSON_CONF);
			if (isComPersonConf == null) isComPersonConf = Boolean.FALSE;
			if (isComPersonConf) {
				companyAbbreviation = (String) request.getSession().getAttribute(Constants.COM_PERSON_ABBREVIATION) + "专享";
			}
		}
		if (StringUtil.isEmpty(areaId) || StringUtil.isEmpty(areaName)) {
			if (member != null) {//用户不为空时，应取用户所在企业地址
				company = member.getCompanyId();
				Area area = company.getArea();
				String treePath = area.getTreePath();
				if (treePath != null) {
					treePath = treePath.substring(1, treePath.length() - 1);
					String[] treePathArr = CommUtil.splitByChar(treePath,
							",");

					if (treePathArr.length >= 2) {
						provinceId = treePathArr[0];
						cityId = treePathArr[1];
					}
				}
			} else {
				cityId = "1607";
				provinceId = "19";
				areaId = "3155";
				areaName = "广东深圳市南山区";
			}
		}

		data.put("companyAbbreviation", companyAbbreviation);
		data.put("cityId", cityId);
		data.put("provinceId", provinceId);
		data.put("areaName", areaName);
		data.put("areaId", areaId);
		String isShip = product.getIsShip() ? (1 + "") : (0 + "");// 商品是否免邮
		data.put("isShip", isShip);

		return data;
	}

	/**
	 * 方法：异步刷新静态页面商品动态信息-检查是否缺货 实现流程： 1。刷新用户默认收货地址 2.刷新商品库存等信息 3.刷新企业简称信息 autor
	 * :penglong
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/checkIsOutOfStock", method = RequestMethod.POST)
	public @ResponseBody
	Map<String, String> checkIsOutOfStock(Long id,
										  HttpServletRequest request) {
		Map<String, String> data = new HashMap<String, String>();
		Member member = memberService.getCurrent();
		Product product = productService.find(id);
		if (product != null && (product.getIsMarketable() == false || product.getEnabledFlag() == false)) {
			data.put("isOutOfStock", "此商品已下架");
			return data;
		}
		if (!productService.checkStock(product, 0)) {
			data.put("isOutOfStock", "缺货");
			return data;
		}

		String areaStr = WebUtils.getCookie(request, "areaIdCookie");
		logger.info("areaIdCookie={}", areaStr);
		Long areaId = null;
		if (areaStr == null) {
			Area area = null;
			if (member != null) {
				Receiver receiver = receiverService.findDefault(member);
				if (receiver != null) {
					area = receiver.getArea();
				}
			}
			if (area == null) {
				areaId = 3155L;
			} else {
				areaId = area.getId();
			}
		} else {
			areaId = Long.parseLong(areaStr);
		}

		// 调用京东校验接口
		if (product.getSupplierId().getIsOweOrder() == 1
				|| product.getSupplierId().getIsOweOrder() == 18) {
			if (!jdService.getBizProductSkuCheck(product)) { //校验是否可售
				data.put("isOutOfStock", "此商品已下架");
			}
			if (jdService.checkProductsForList(product, areaId)) {
				data.put("isOutOfStock", "此地区缺货");
			}
			List<StockVO> stockVOs = new ArrayList<>();
			StockVO stockVO = new StockVO();
			stockVO.setSkuId(product.getJdSku());
			stockVO.setNum(1);
			stockVOs.add(stockVO);
			if (!jdService.checkAreaLimitForProductDetail(stockVOs, product, Long.valueOf(areaId))) {//校验区域购买限制
				data.put("isOutOfStock", "此地区缺货");
			}
		}

		//xl20171204网易严选校验库存
		else if (product.getSupplierId().getIsOweOrder() == 8) {
			Long stock = yanxuanService.getYanxuanProductStock(product);
			if (stock == null || stock < 1) {
				data.put("isOutOfStock", "缺货");
				return data;
			}
		}

		//行云校验库存
		else if (product.getSupplierId().getIsOweOrder() == 2) {
			xyService.checkPrice(product, null);
			if (product.getIsMarketable() == false || (product.getStock() != null && product.getStock() == 0)) {
				data.put("isOutOfStock", "缺货");
				return data;
			}
		}

		//bnplus库存校验
		else if (product.getSupplierId().getIsOweOrder() == 38) {
			bnplusService.checkPriceStock(product, null);
			if (product.getIsMarketable() == false || (product.getStock() != null && product.getStock() == 0)) {
				data.put("isOutOfStock", "缺货");
				return data;
			}
		}
		//智臻云采
		else if (product.getSupplierId().getIsOweOrder() == 40) {

			if (!zhiZhenYunCaiService.checkStock(product, null, Long.valueOf(areaId))) {
				data.put("isOutOfStock", "缺货");
				return data;
			}
		}

		//怡亚通校验库存
		else if (product.getSupplierId().getIsOweOrder() == 32) {
			Long stock = eascsSyncService.getEascsProductStock(product);
			if (stock == -1) { // 1-商品已下架
				data.put("isOutOfStock", "此商品已下架");
				return data;
			} else if (stock == 0) { // 0-上架状态
				data.put("isOutOfStock", "缺货");
				return data;
			}
		}

		// 调用优鲜校验接口
		else if (product.getSupplierId().getIsOweOrder() == 13) {
			if (!missfreshService.isSaleArea(product, areaId)) {
				data.put("isOutOfStock", "此地区缺货");
			}

		}
		// 调用fliPlus校验规则
		else if (product.getSupplierId().getIsOweOrder() == 0) {
			if (!productService.isSaleArea(product, areaId)) {
				data.put("isOutOfStock", "暂不支持此地区销售");
			}
		}
		// 京东健康
		else if (product.getSupplierId().getIsOweOrder() == 61){
			Company company=null;
			if(member !=null){
				company=member.getCompanyId();
			}else{
				String companyIdStr = (String) request.getSession().getAttribute("companyIdStr");
				if (companyIdStr != null) {
					Long companyId = Long.valueOf(companyIdStr);
					company = companyService.find(companyId);
				}else{
					company = companyService.getDefultCompany();
				}
			}
			List<Product> list = new ArrayList<>();
			list.add(product);
			Map<Long, Boolean> map = jdHealthService.checkStock(company.getId(), list, areaId);
			if (map.get(product.getId())) {
				data.put("isOutOfStock", "此地区缺货");
			}
		}
		// 苏州通卡
		else if (product.getSupplierId().getIsOweOrder() == 65){
			if (!jstkService.checkStock(product,0)) {
				data.put("isOutOfStock", "缺货");
				return data;
			}
		}
		// 天猫
		else if (product.getSupplierId().getIsOweOrder() == CommonAttributes.TMALL_SUPPLIER_ISOWEORDER) {
			List<Product> list = new ArrayList<>();
			list.add(product);
			Map<Long, Boolean> map = tMallService.checkStock(list, areaId);
			if (map.get(product.getId())) {
				data.put("isOutOfStock", "缺货");
			}
		}
		String isShip = product.getIsShip() ? (1 + "") : (0 + "");// 商品是否免邮
		data.put("isShip", isShip);

		return data;
	}


	/**
	 * 价高反馈信息 方法: 实现流程: 1. autor :sunjianwen
	 *
	 * @param productId
	 * @return
	 */
	@RequestMapping(value = "/priceFeedback", method = RequestMethod.POST)
	@ResponseBody
	public Boolean priceFeedback(Long productId) {
		Member member = memberService.getCurrent();
		Long memberId = -1L;
		if (member != null) {
			memberId = member.getId();
		}
		jdFeedbackService.sendMq(productId, memberId, 2);
		return true;
	}

	/**
	 * 团购商品已购买数量 方法: 实现流程: 1. autor :刘于燕
	 *
	 * @param productId
	 * @return
	 */
	@RequestMapping(value = "/getQuantityGroupPurchase", method = RequestMethod.POST)
	@ResponseBody
	public Map<String, Object> getQtyGroupPurchase(Long productId,
												   HttpServletRequest request) {
		Map<String, Object> data = new HashMap<String, Object>();
		Product product = productService.find(productId);
		List<Group> groups = new ArrayList<Group>(product.getStatusGroups());
		if (groups != null && groups.size() > 0) {
			Date date = new Date();
			Group group = groups.get(0);
			List<Group> groupsValid = new ArrayList<Group>(
					product.getValidGroups());
			if (groupsValid.size() > 0) {
				group = groupsValid.get(0);
				data.put("group", "1"); // 促销正在进行
			}
			if (group.hasEnded()) { // 促销结束
				data.put("group", "4");
			} else if (!group.hasBegun()
					&& CommUtil.isSameDate(group.getBeginDate(), date)) {
				data.put("group", "2"); // 开始倒计时
			} else if (!group.hasBegun()) {
				data.put("group", "3"); // 促销提醒
			}
			data.put("beginDate", group.getBeginDate());
			data.put("endDate", group.getEndDate());
			data.put("groupId", group.getId());
			GroupProduct groupProduct = groupProductService
					.findByGroupIdAndProductId(group.getId(), productId);
			data.put("productPrice", groupProduct.getGroupPrice());
			data.put(
					"productSavePrice",
					product.getMarketPrice()
							.subtract(groupProduct.getGroupPrice()).toString());
			Integer successCount = groupOrderService
					.getSuccessCount(groupProduct.getId());// 已购买数量
			Integer diff = groupProduct.getGroupCount() - successCount > 0 ? groupProduct
					.getGroupCount() - successCount
					: 0;
			// Integer
			// diff=groupProduct.getGroupCount()-groupProduct.getSuccessCount()>0?groupProduct.getGroupCount()-groupProduct.getSuccessCount():0;
			data.put("qtyMsg", "已购" + successCount + "份,还差" + diff + "份成团");

		}

		return data;
	}

	/**
	 * 每日促销商品信息 方法: 实现流程: 1. autor :刘于燕
	 *
	 * @param productId
	 * @return
	 */
	@RequestMapping(value = "/getPromotion", method = RequestMethod.POST)
	@ResponseBody
	public Map<String, Object> getPromotion(Long productId,
											HttpServletRequest request) {
		Map<String, Object> data = new HashMap<String, Object>();
		Product product = productService.find(productId);
		List<Promotion> promotions = new ArrayList<Promotion>(
				product.getStatusPromotions());
		if (promotions.size() > 0) {
			Date date = new Date();
			Promotion promotion = promotions.get(0);
			List<Promotion> promotionsValid = new ArrayList<Promotion>(
					product.getValidPromotions());
			if (promotionsValid.size() > 0) {
				promotion = promotionsValid.get(0);
				data.put("promotion", "1"); // 促销正在进行
			}
			if (promotion.hasEnded()) { // 促销结束
				data.put("promotion", "4");
			} else if (!promotion.hasBegun()
					&& CommUtil.isSameDate(promotion.getBeginDate(), date)) {
				data.put("promotion", "2"); // 开始倒计时
			} else if (!promotion.hasBegun()) {
				data.put("promotion", "3"); // 促销提醒
			}
			data.put("beginDate", promotion.getBeginDate());
			data.put("endDate", promotion.getEndDate());
		}
		return data;

	}

	/**
	 * 每日促销 团购 满减或折扣 方法: 实现流程: 1. autor :刘于燕
	 *
	 * @param productId
	 * @return type 0:促销 1：团购 2：折扣
	 */
	@RequestMapping(value = "/getPriority", method = RequestMethod.POST)
	@ResponseBody
	public int getPriority(Long productId, Long actId) {
		Product product = productService.find(productId);
		Set<Promotion> statusPromotions = product.getStatusPromotions();
		if (statusPromotions != null && statusPromotions.size() > 0) {// 每日促销
			return 0;
		}
		Set<Group> statusGroups = product.getStatusGroups();
		if (statusGroups != null && statusGroups.size() > 0) {// 团购
			return 1;
		}

		return 2;
	}

	/**
	 * 每日促销 团购 满减或折扣 方法: 实现流程: 1. autor :刘于燕
	 *
	 * @param productId
	 * @return
	 */
	@RequestMapping(value = "/getStatusActs", method = {RequestMethod.POST})
	@ResponseBody
	public Set<Act> getStatusActs(Long productId) {
		Product product = productService.find(productId);
		Set<Act> set = product.getValidActs();
		return set;
	}

	/**
	 * 自动模糊查询搜索词 autor :刘于燕
	 *
	 * @param response
	 * @throws IOException
	 */
	@RequestMapping(value = "/autoCom", method = {RequestMethod.POST,
			RequestMethod.GET})
	public @ResponseBody
	Map<String, Object> AutoCom(
			HttpServletResponse response, String query) throws IOException {
		//		List<SearchHot> searchHots = searchHotService.findSearehHots(query);
		List<Map<String, String>> list = searchService.suggest(query);
		List<SearchHotVo> autoVos = new ArrayList<SearchHotVo>();
		if (list != null && list.size() > 0) {
			for (Map<String, String> map : list) {
				SearchHotVo searchHotVo = new SearchHotVo();
				searchHotVo.setValue(map.get("code"));
				searchHotVo.setData(map.get("code"));
				autoVos.add(searchHotVo);
			}
		}

		/*for (SearchHot searchHot : searchHots) {
			SearchHotVo searchHotVo = new SearchHotVo();
			searchHotVo.setValue(searchHot.getSearchName());
			searchHotVo.setData(searchHot.getSearchName());
			autoVos.add(searchHotVo);
		}*/

		Map<String, Object> outMsg = new HashMap<String, Object>();
		outMsg.put("query", query);

		//关键词放入集合第一位
		SearchHotVo searchHotVo = new SearchHotVo();
		searchHotVo.setValue(query);
		searchHotVo.setData(query);
		autoVos.add(0, searchHotVo);

		outMsg.put("suggestions", autoVos);
		return outMsg;

	}

	/**
	 * 方法:列表判断商品是否缺货 true 缺货 实现流程: 1.如果地址为空取深圳南山区地址 autor :彭龙
	 *
	 * @return
	 */
	@RequestMapping(value = "/testListStock", method = {RequestMethod.POST, RequestMethod.GET})
	public String testListStock() {
		return "/shop/product/index_test";

	}


	/**
	 * 判断有货无货 autor :刘于燕
	 *
	 * @param response
	 * @throws IOException
	 */
	@RequestMapping(value = "/listStock", method = RequestMethod.POST)
	public @ResponseBody
	Map<Long, Boolean> listStock(HttpServletRequest request,
			HttpServletResponse response, Long[] ids, Long areaId) {
		if (areaId == null) {
			areaId = 3155L;
		}
		Map<Long, Boolean> outMsg = new HashMap<Long, Boolean>();
		List<Product> products = productService.findList(ids);
		List<Product> productjd = new ArrayList<Product>();
		List<Product> jdHealthProducts = new ArrayList<>();
		List<Product> tmallProducts = new ArrayList<>();
//		List<Product> productMissfresh = new ArrayList<Product>();
		//用来存储行云的skus和商品Id
//		Map<String, Long> xyMap = new HashMap<>();
		for (Product product : products) {
			if (product != null && !product.getIsMarketable()) {
				outMsg.put(product.getId(), true);
			} else {
				// 调用京东校验接口  modify by hzq 添加行云校验
				if (product.getSupplierId().getIsOweOrder() == 1
						|| product.getSupplierId().getIsOweOrder() == 18) {
					productjd.add(product);
				} 
				/*else if (product.getSupplierId().getIsOweOrder() == 13) {
					productMissfresh.add(product);
				}*/
				// 调用fliPlus校验规则
				else if (product.getSupplierId().getIsOweOrder() == 0) {
					// 库存是否不足
					if (!productService.checkStock(product, 0)) {
						outMsg.put(product.getId(), true);
						continue;
					}
					// 该区域是否销售
					if (!productService.isSaleArea(product, areaId)) {
						outMsg.put(product.getId(), true);
						continue;
					}
					// 两者都满足(有货)
					outMsg.put(product.getId(), false);
				}
				// 苏州通卡
				else if (product.getSupplierId().getIsOweOrder() == 65){
					if (!jstkService.checkStock(product,0)) {
						outMsg.put(product.getId(), true);
						continue;
					}
				}
				
			/*	else if (product.getSupplierId().getIsOweOrder() == 2) {
					//将海外购商品更改为实时查询接口
					xyMap.put(product.getJdSku(), product.getId());
				}*/
				else if (product.getSupplierId().getIsOweOrder() == 61){// 京东健康
					jdHealthProducts.add(product);
				}
				
				// 天猫
				else if (product.getSupplierId().getIsOweOrder() == CommonAttributes.TMALL_SUPPLIER_ISOWEORDER){
					tmallProducts.add(product);
				}
			}
		}
		//批量查询行云库存
		/*		Map<Long, Boolean> xyList = xyService.getProductStockList(xyMap);
		if (xyList != null) {
			outMsg.putAll(xyList);
		}
		Map<Long, Boolean> missfreshList = missfreshService.checkProductsForList(productMissfresh, areaId);
		if (missfreshList != null) {
			outMsg.putAll(missfreshList);
		}
 		*/
		Map<Long, Boolean> jdList = jdService.checkProductsForList(productjd, areaId);
		if (jdList != null) {
			outMsg.putAll(jdList);
		}
		if(jdHealthProducts!=null && !jdHealthProducts.isEmpty()) {
			Member member = memberService.getCurrent();
			Company company=null;
			if(member !=null){
				company=member.getCompanyId();
			}else{
				String companyIdStr = (String) request.getSession().getAttribute("companyIdStr");
				if (companyIdStr != null) {
					Long companyId = Long.valueOf(companyIdStr);
					company = companyService.find(companyId);
				}else{
					company = companyService.getDefultCompany();
				}
			}
			Map<Long, Boolean> jdHealthMap = jdHealthService.checkStock(company.getId(),jdHealthProducts, areaId);
			if(jdHealthMap!=null){
				outMsg.putAll(jdHealthMap);
			}
		}

		// 天猫
		else if (!tmallProducts.isEmpty()) {
			Map<Long, Boolean> tnallMap = tMallService.checkStock(tmallProducts, areaId);
			if (tnallMap != null) {
				outMsg.putAll(tnallMap);
			}
		}
		return outMsg;

	}


	/**
	 * 方法： 根据折扣率获取商品列表 autor :huhui
	 *
	 * @param discountRate      折扣率
	 * @param productCategoryId 商品分类
	 * @param brandId           品牌id
	 * @param promotionId       促销id
	 * @param tagIds            标签id
	 * @param startPrice        最低价
	 * @param endPrice          最高价
	 * @param orderType         排序类型
	 * @param pageNumber        第几页
	 * @param pageSize          每页大小
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/discountList", method = RequestMethod.GET)
	public String getListByDiscountRate(Long discountRate,
										Boolean isProductCategory, Long productCategoryId, Long brandId,
										Long promotionId, Long[] tagIds, BigDecimal startPrice,
										BigDecimal endPrice, OrderType orderType, Integer pageNumber,
										Integer pageSize, HttpServletRequest request, ModelMap model, HttpSession session) {
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}
		Supplier supplier = supplierService.find(68L);
		//List<Supplier> suppliers = new ArrayList<Supplier>();
		//suppliers.add(supplier);
		ProductCategory productCategory = null;
		if (productCategoryId == null) {
			Long[] CategoryIds = productService
					.findProductCategoryByDiscountRate(discountRate);
			List<ProductCategory> list = productCategoryService
					.findList(CategoryIds);
			model.addAttribute("initProductCategory", list);
		} else {
			productCategory = productCategoryService.find(productCategoryId);
		}
		Brand brand = brandService.find(brandId);
		Promotion promotion = promotionService.find(promotionId);
		List<Tag> tags = tagService.findList(tagIds);
		Pageable pageable = new Pageable(pageNumber, 30);
		if (isProductCategory != null) {
			discountRate = null;
			pageable.getFilters().add(Filter.le("attributeValue14", 80));
		} else {
			if (discountRate != null) {
				if (discountRate == 3) {
					pageable.getFilters()
							.add(Filter.le("attributeValue14", 30));
				} else if (discountRate == 5) {
					pageable.getFilters()
							.add(Filter.ge("attributeValue14", 30));
					pageable.getFilters()
							.add(Filter.le("attributeValue14", 50));
				} else if (discountRate == 8) {
					pageable.getFilters()
							.add(Filter.ge("attributeValue14", 50));
					pageable.getFilters()
							.add(Filter.le("attributeValue14", 80));
				}
			}
		}
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if (!StringUtil.isEmpty(excludeSuppliers)) {
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		model.addAttribute("isProductCategory", isProductCategory);
		model.addAttribute("orderTypes", OrderType.values());
		model.addAttribute("productCategory", productCategory);
		model.addAttribute("brand", brand);
		model.addAttribute("promotion", promotion);
		model.addAttribute("tags", tags);
		model.addAttribute("startPrice", startPrice);
		model.addAttribute("endPrice", endPrice);
		model.addAttribute("orderType", orderType);
		model.addAttribute("pageNumber", pageNumber);
		model.addAttribute("pageSize", pageSize);
		model.addAttribute("discountRate", discountRate);
		model.addAttribute("formURL", "discountList");
		model.addAttribute("page", productService.findListByDiscountRate(
				discountRate, productCategory, brand, supplier, null,
				promotion, tags, "3", startPrice, endPrice, true, true,
				null, false, orderType, pageable,company));
		model.addAttribute("company", company);
		if (company != null) {
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}
		return "shop/discount/list";
	}


	/**
	 * 价高反馈信息 方法: 实现流程: 1. autor :sunjianwen
	 *
	 * @param productId
	 * @return
	 */
	@RequestMapping(value = "/getProductById", method = {RequestMethod.POST, RequestMethod.GET})
	@ResponseBody
	public Product getProductById(Long productId, Long companyId, HttpServletRequest request) {
		Member member = getCurrent();
		Product product = null;
		if (productId != null) {
			product = productService.find(productId);
		}
		Company company = null;

		if (member != null) {
			company = member.getCompanyId();
		} else {
			String companyIdStr = (String) request.getSession().getAttribute("companyIdStr");
			logger.info("session中保存的companyIdStr=" + companyIdStr);
			if (StringUtils.isNotEmpty(companyIdStr)) {
				company = companyService.find(Long.valueOf(companyIdStr));
			} else {
				if (companyId != null) {//带了企业id
					company = companyService.find(companyId);
					if (company == null) {//获取到指定的企业
						companyIdStr = company.getId().toString();
						company = companyService.getDefultCompany();
					}
				} else {
					company = companyService.getDefultCompany();
				}
			}
		}

		/**

		 Map supplierPriceMap = null;
		 String supplierPriceRate = company.getSupplierPriceRate();
		 if (StringUtils.isNotEmpty(supplierPriceRate)) {
		 supplierPriceMap = JSONObject.parseObject(supplierPriceRate);
		 if (supplierPriceMap != null) {
		 if (supplierPriceMap.get(product.getSupplierId().getId().toString()) != null) {
		 String priceRate = JSON.parseObject(supplierPriceMap.get(product.getSupplierId().getId().toString()).toString()).getString("price");
		 String marketPriceRate = JSON.parseObject(supplierPriceMap.get(product.getSupplierId().getId().toString()).toString()).getString("market_price");
		 if (StringUtils.isNotEmpty(priceRate)) {
		 product.setPrice(new BigDecimal(priceRate).multiply(product.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
		 }
		 if (StringUtils.isNotEmpty(marketPriceRate)) {
		 product.setMarketPrice(new BigDecimal(marketPriceRate).multiply(product.getMarketPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
		 }
		 }
		 }
		 }
		 *
		 */
		AddPriceProductPageVo productAddPrice = productAddPriceService.getProductAddPrice(product, company);
		if (productAddPrice != null) {
			product.setPrice(productAddPrice.getPrice());
			product.setMarketPrice(productAddPrice.getMarketPrice());
		}
		String costPaymentFlag = (String)request.getSession().getAttribute("cost_payment_flag");
        if(StringUtils.isNotEmpty(costPaymentFlag) && "1".equals(costPaymentFlag)){ //成本价采购
			//查询是否有配置成本价加价比例
			SysCode sysCode = sysCodeService.findbyCode("company_" + company.getId(), CommonAttributes.ADD_COST_PRICE_RATE);
			if(sysCode!=null){
				product.setPrice((product.getCost().multiply(new BigDecimal(sysCode.getValue()))).setScale(2,BigDecimal.ROUND_HALF_UP));
			}else{
				product.setPrice(product.getCost());
			}
		}
		return product;
	}


	/**
	 * 方法:京东商城首页是否弹出添加商品，价高反馈的提示
	 * autor :l刘于燕
	 *
	 * @param type=1 :京东商城   type=2:折扣商城
	 * @return
	 */
	@RequestMapping("/productNotice")
	public @ResponseBody
	Boolean productNotice(Integer type) {
		Member member = getCurrent();
		if (member != null) {
			if (type != null) {
				if (type == 1) {//京东商城
					if (member.getAttributeValue0() != null) {
						if ("0".equals(member.getAttributeValue0()) || "2".equals(member.getAttributeValue0())) {//初始状态或用户取消了不再提示
							return true;
						}
					} else {
						return true;
					}
				} else {//折扣商城
					if (member.getAttributeValue1() != null) {
						if ("0".equals(member.getAttributeValue1()) || "2".equals(member.getAttributeValue1())) {//初始状态或用户取消了不再提示
							return true;
						}
					} else {
						return true;
					}
				}
			}

		}
		return false;
	}

	/**
	 * 方法:京东商城首页是否弹出添加商品，价高反馈的提示
	 * autor :l刘于燕
	 *
	 * @return
	 */
	@RequestMapping("/productNoticeWindow")
	public String productNoticeWindow(Integer type, ModelMap model) {
		model.addAttribute("type", type);
		return "shop/product/product_notice";
	}

	/**
	 * 方法:京东商城首页是否弹出添加商品，价高反馈的提示
	 * autor :l刘于燕
	 *
	 * @return
	 */
	@RequestMapping("/updateProductNotice")
	public @ResponseBody
	Boolean updateProductNotice(String status, Integer type) {
		Member member = memberService.getCurrent();
		if (member != null) {
			if (type != null) {
				if (type == 1) {
					member.setAttributeValue0(status);
				} else {
					member.setAttributeValue1(status);
				}
			}
			memberService.update(member);
		}
		return true;
	}


	/**
	 * 方法：商品详情页获取推荐商品
	 * :刘于燕
	 *
	 * @param productId
	 * @return
	 */
	@RequestMapping(value = "/getRecommendProducts", method = {RequestMethod.POST, RequestMethod.GET})
	public @ResponseBody
	List<Product> getRecommendProducts(Long productId) {
		return productService.getRecommendProducts(productId);
	}

	/**
	 * @param hotProductPageSize
	 * @param commendProductsPageSize
	 * @param model
	 * @return java.lang.String
	 * <AUTHOR>
	 * @Description 精品商城首页
	 * @Date 2022/4/12 11:31
	 **/
	@RequestMapping("/boutiqueShop")
	public String toBoutiqueShop(@RequestParam(defaultValue = "6") Integer hotProductPageSize,
								 @RequestParam(defaultValue = "10") Integer commendProductsPageSize,
								 ModelMap model) {
		Member member = this.getCurrent();
		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			company = companyService.getDefultCompany();
		}

		//1、热销商品tagId
		Long boutiqueShopHotTagId = Long.parseLong(sysCodeService.getValue(SysCode.SYS_GROUP, Constants.BOUTIQUE_HOT_PRODUCT_TAG_ID));
		model.addAttribute("boutiqueShopHotTagId", boutiqueShopHotTagId);
		//2、推荐商品tagId
		Long boutiqueShopRecommendTagId = Long.parseLong(sysCodeService.getValue(SysCode.SYS_GROUP, Constants.BOUTIQUE_RECOMMEND_TAG_ID));
		model.addAttribute("boutiqueShopRecommendTagId", boutiqueShopRecommendTagId);

		Pageable pageable = new Pageable();
		pageable.setPageNumber(1);
		//3、热销商品
		pageable.setPageSize(hotProductPageSize);
		model.addAttribute("hotProducts", productService.findPageByTagId(pageable, boutiqueShopHotTagId, null));

		//4、推荐商品
		pageable.setPageSize(commendProductsPageSize);
		model.addAttribute("commendProducts", productService.findPageByTagId(pageable, boutiqueShopRecommendTagId, null));

		//5、获取一级分类
		List<ProductCategory> productCategories = productCategoryService.findBoutiqueCategorys(company);//一级分类

		//6、过滤二、三级分类：只显示关联了精品商城商品的分类
		productCategoryService.boutiqueCategorysFilter(productCategories, 0, company);

		model.addAttribute("productCategories", productCategories);

		//7、查询活动
		//查询活动
		List<Act> actList = actService.findBoutiqueShopList();
		model.addAttribute("actList", actList);
		model.addAttribute("company", company);
		return "shop/product/boutique_shop";
	}

	/**
	 * @param tagId           标签，用于区分热销商品、精品推荐的商品
	 * @param orderType
	 * @param attributeValue5
	 * @param pageable
	 * @param model
	 * @return java.lang.String
	 * <AUTHOR>
	 * @Description 精品商城-热销商品、精品推荐 的分页数据
	 * @Date 2022/4/14 14:17
	 **/
	@RequestMapping(value = "/boutiqueShopList", method = {RequestMethod.GET, RequestMethod.POST})
	public String toBoutiqueShopMorePage(Long tagId, OrderType orderType, String attributeValue5,
										 String from, Pageable pageable, ModelMap model) {
		model.addAttribute("tagId", tagId);
		model.addAttribute("orderType", orderType);
		model.addAttribute("attributeValue5", attributeValue5);
		model.addAttribute("from", from);
		model.addAttribute("page", productService.findPageByTagId(pageable, tagId, orderType));

		return "shop/product/boutique_shop_list";
	}
	/**
	 *方法:工业品商城
	 *作者:sangyj
	 * @param model
	 * @param session
	 * @return
	 */
	@RequestMapping("/industrialShop")
	public String industrialShop(ModelMap model,HttpSession session) {
        Member member=getCurrent();
        Long cateId = CommonAttributes.INDUSTRIAL_CATEGORY_ID;
        Company company=null;
        if(member!=null){
            company=member.getCompanyId();
        }else{
            String companyIdStr = (String) session.getAttribute("companyIdStr");
            if (companyIdStr != null) {
                Long companyId = Long.valueOf(companyIdStr);
                company = companyService.find(companyId);
            }
        }
        model.addAttribute("company",company);
        //获取所有一级分类
        List<ProductCategory> productCategories = productCategoryService.findIndustrialCate(company);
        for(ProductCategory productCategory : productCategories) {
        	//二级分类
            List<Filter> filters=new ArrayList<Filter>();
    		filters.add(Filter.eq("supplierId", CommonAttributes.JD_SUPPLY_ID));
    		filters.add(Filter.eq("parent.id", cateId));
    		filters.add(Filter.eq("enabledFlag", true));
    		filters.add(Filter.eq("seoKeywords", productCategory.getId()));
    		List<Order> orders=new ArrayList<Order>();
    		orders.add(Order.asc("order"));
    		List<ProductCategory> productCategories1 = productCategoryService.findList(null, filters, orders);
    		productCategory.setChildren(new HashSet<ProductCategory>(productCategories1));
        }
        model.addAttribute("productCategories", productCategories);
        //68：京东供应商
		model.addAttribute("supplierIds",CommonAttributes.JD_SUPPLY_ID);
		//查询排除的供应商ID
		String excludeSuppliers = sysCodeService.getValue(SysCode.SYS_GROUP, Constants.ADD_PRICE_EXCLUDE_SUPPLIER_KEY);
		if(!StringUtil.isEmpty(excludeSuppliers)){
			model.addAttribute("excludeSuppliers", excludeSuppliers.split(","));
		}
		//查询品牌
		ProductCategory productCategory = productCategoryService.find(cateId);
		Set<Brand> brandSet = productCategory.getBrands();
		List<Brand> brandList = new ArrayList<Brand>(brandSet);
		Collections.sort(brandList, new Comparator<Brand>() {
			@Override
			public int compare(Brand o1, Brand o2) {
				return o1.getOrder()-o2.getOrder();
			}
		});
		model.addAttribute("brands",brandList);
		
		//推荐分类
		List<ProductCategory> recommendCategories = productCategoryService.findIdstRecommendCate(company);
		model.addAttribute("recommendCategories", recommendCategories);
		//热推商品
		Page<Product> hostProducts = productService.getPageBytagId(new Pageable(1,10), 8L, null, null, CommonAttributes.JD_SUPPLY_ID, cateId, null);
		model.put("hostProducts", hostProducts.getContent());
		//分类推荐商品
		Map<Long,List<Product>> recommendProducts = new HashMap<Long, List<Product>>();
		for(ProductCategory cate : recommendCategories) {
			Page<Product> recProducts = productService.getPageBytagId(new Pageable(1,8), 10L, null, null, CommonAttributes.JD_SUPPLY_ID, null, cate.getId());
			recommendProducts.put(cate.getId(), recProducts.getContent());
		}
		model.put("recommendProducts", recommendProducts);
		model.put("productCategoryId", cateId);
		return "/shop/product/industrial_shop";
	}

	/**
	 * method: 京东商城首页异步获取一级分类下二三级子分类
	 * author: xiaoyue
	 *
	 * @param productCategoryId
	 * @param session
	 * @return
	 * @createData 2022-07-01
	 */
	@RequestMapping(value = "/getCategoryChild", method = {RequestMethod.GET, RequestMethod.POST})
	public @ResponseBody
	Map<String, Object> getProducChildrenCategory(Long productCategoryId, HttpSession session) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		ProductCategory productCategory = productCategoryService.find(productCategoryId);
		if (productCategory == null) {
			throw new ResourceNotFoundException();
		}

		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}

		List<ProductCategory> thirdProductCategoryList = new ArrayList<>();
		if (!company.getCategoryAll()) { // 如果非所有商品分类开发查询相应有权限分类 做in查询
			// 动态获取公司下分类
			List<Long> companyCategory = productCategoryService.findCompanyCategory(company);

			// 二级分类
			productCategory.getChildren().clear();
			productCategory.setChildren(new HashSet<ProductCategory>(
					productCategoryService.findCompanyCategoryChildren(
							productCategory, null, companyCategory)));

			// 三级分类企业个性化
			for (ProductCategory twoProductCategory : productCategory.getChildren()) {
				List<ProductCategory> companyChildrenThird = productCategoryService.findCompanyCategoryChildren(twoProductCategory, null, companyCategory);
				thirdProductCategoryList.addAll(companyChildrenThird);
			}
		}
		resultMap.put("productCategoryTwo", productCategory.getChildren());
		resultMap.put("productCategoryThird", thirdProductCategoryList);

		return resultMap;
	}


	/**
	 * @param tagId
	 * @param orderType
	 * @param attributeValue5
	 * @param pageable
	 * @param model
	 * @return java.lang.String
	 * <AUTHOR>
	 * @Description 监狱商品列表
	 * @Date 2022/8/4 14:17
	 **/
	@RequestMapping(value = "/prisonProduct", method = {RequestMethod.GET, RequestMethod.POST})
	public String prisonProduct(Long actId, String from, Pageable pageable, ModelMap model) {


		Member member = getCurrent();
		Company companyId = member.getCompanyId();
		List<Product> products = new ArrayList();
		if (actId != null) {
			List<Filter> filters = new ArrayList();
			filters.add(Filter.eq("act", actService.find(actId)));
			filters.add(Filter.eq("actProductCompanyIds", companyId));
			List<ActProduct> actProducts = actProductService.findList(null, filters, null);
			for (ActProduct actProduct : actProducts) {
				products.add(actProduct.getProducts());
			}
		}
		model.addAttribute("actId", actId);
		model.addAttribute("products", products);
		return "shop/prison_procurement/product";
	}

	/**
	 * 方法: 监狱商品子列表 <br/>
	 * 实现流程  : <br/>
	 *
	 * @param model 数据
	 * @Return java.lang.String
	 * <AUTHOR>
	 * @CreateData 2022-08-4 08:47
	 */
	@RequestMapping(value = "/prisonProduct/child", method = {RequestMethod.GET, RequestMethod.POST})
	public String orderListChild(Long id,Long actId, String productName, BigDecimal startPrice, BigDecimal endPrice, Pageable pageable, ModelMap model,String operation) {
		if (actId != null) {
			Page<Product> page = productService.findProductsByAct(id, actId, productName, startPrice, endPrice, pageable);
			model.addAttribute("page", page);
		}
		return "/shop/prison_procurement/include/product_child";
	}

	/**
	 * * method:监狱商品列表导出
	 * * author: chengang
	 * * @createData 2022-08-04
	 */
	@RequestMapping(value = "/productInfoExport", method = {RequestMethod.POST, RequestMethod.GET})
	public void memberInfoExport(Long actId, HttpServletResponse response, Pageable pageable, ModelMap model) {
		Member member = getCurrent();
		Company companyId = member.getCompanyId();
		List<Product> products = new ArrayList();
		if (actId != null) {
			List<Filter> filters = new ArrayList();
			filters.add(Filter.eq("act", actService.find(actId)));
//			filters.add(Filter.eq("actProductCompanyIds", companyId));
			List<ActProduct> actProducts = actProductService.findList(null, filters, null);
			for (ActProduct actProduct : actProducts) {
				products.add(actProduct.getProducts());
			}
		}
		productService.downProductInfo(products, companyId, response);
	}

	/**
	 * * method:监狱商品图片导出
	 * * author: chengang
	 * * @createData 2022-08-04
	 */
	@RequestMapping(value = "/downZipImages", method = {RequestMethod.POST, RequestMethod.GET})
	public void downZipImages(Long actId, HttpServletRequest request, HttpServletResponse response, Pageable pageable, ModelMap model) {
		Member member = getCurrent();
//		Company companyId = member.getCompanyId();
		Map<String, URL> urls = new HashMap();
		Setting setting = SettingUtils.get();
		String siteUrlImg = setting.getSiteUrlImg();
		String ImagePath = siteUrlImg;

		if (actId != null) {
			try {
				List<Filter> filters = new ArrayList();
				filters.add(Filter.eq("act", actService.find(actId)));
//				filters.add(Filter.eq("actProductCompanyIds", companyId));
				List<ActProduct> actProducts = actProductService.findList(null, filters, null);
				for (ActProduct actProduct : actProducts) {
					Product product = actProduct.getProducts();
							//供应商为京东
							String urlString ="";
							if(product.getSupplierId()!=null && product.getSupplierId().getIsOweOrder()==1){
								if (product.getAttributeValue8()!=null) {
									String[] urlStrings = product.getAttributeValue8().split("/");
									urlStrings[3] = "n1";
//									urlString = String.join("/", urlStrings);
//									urlString = product.getAttributeValue8();
								}
							}else{
								urlString = ImagePath + product.getImage();
//								urlString = "https://img13.360buyimg.com/n6/jfs/t3/204796/30/5579/271137/6152ac82Ef17edb94/aa8a3863b5834393.jpg";
							}
							urls.put(product.getId().toString(), new URL(urlString));
					}
					ZipUtils.toZip(urls, response);
				} catch(Exception e){
					e.printStackTrace();
				}
			}
		}
	
		/**
		 *方法:检查商品分类限制
		 *作者:sangyj
		 * @param type
		 * @param jdSku
		 * @return
		 */
		@RequestMapping(value = "/checkCateLimit", method = RequestMethod.POST)
		public @ResponseBody Message checkCateLimit(Long productId,String jdSku) {
			Product product = null;
			if(productId!=null) {
				product = productService.find(productId);
			}else if(!StringUtil.isEmpty(jdSku)) {
				product = productService.findByJdSku(jdSku);
			}
			Member member = memberService.getCurrent();
			if (product == null || member == null) {
				return ERROR_MESSAGE;
			}
			//企业分类限制
			if(!member.getCompanyId().getCategoryAll()){ // 企业包含部分分类
				if(!productCategoryService.checkCompanyCategory(member.getCompanyId().getId(),product.getProductCategory().getId())){
					logger.info("检查商品分类限制失败{}--{}",member.getCompanyId().getId(),product.getProductCategory().getId());
					return Message.success("0");
				}
			}
			return Message.success("1");
		}

	/**
	 * 天猫商城
	 * @param model 视图
	 * @param session session
	 * @author: GanSiquan
	 * @date: 2025-07-23 17:59
	 */
	@RequestMapping("/tMallShop")
	public String tMallShop(ModelMap model, HttpSession session) {
		Member member = getCurrent();
		Company company = null;
		if (member != null) {
			company = member.getCompanyId();
		} else {
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}

		//天猫供应商
		Supplier supplier = supplierService.getSupplierByPlatform(CommonAttributes.TMALL_SUPPLIER_ISOWEORDER);

		model.addAttribute("company", company);
		model.addAttribute("supplier", supplier);
		if (company != null) {
			Map supplierPriceMap = null;
			List<Map> supplierPriceRateList = company.getSupplierPriceRateList(); // 供应商加价规则
			if (CollectionUtils.isNotEmpty(supplierPriceRateList)) {
				supplierPriceMap = supplierPriceRateList.get(0);  // 非虚拟供应商
				if (supplierPriceMap.size() > 0) {
					model.addAttribute("supplierPriceMap", supplierPriceMap);
				}
			}
		}

		// 1.查询供应商二级分类
		List<Filter> filters = new ArrayList<>();
		List<Order> orders = new ArrayList<>();
		filters.add(Filter.eq("supplierId", supplier.getId()));
		filters.add(Filter.eq("grade", 1));
		filters.add(Filter.eq("enabledFlag", true));
		orders.add(Order.asc("order"));
		List<ProductCategory> companyChildrenTwo = productCategoryService.findList(null, filters, orders);
		model.addAttribute("productCategories", companyChildrenTwo);

		if (companyChildrenTwo!=null) {
			ProductCategory productCategoryFirst = companyChildrenTwo.get(0);
			Page<Product> page = productService.findPage(productCategoryFirst, null, supplier, null, null, null, "3", null, null, true, true, null, false, null, new Pageable(1, 20), company, true, null, null, null, null, null);
			if (page != null && page.getContent() != null && !page.getContent().isEmpty()) {
				List<Product> products = page.getContent();

				// 企业加价
				for (Product product : products) {
					AddPriceProductPageVo addPriceProductPageVo = productAddPriceService.getProductAddPrice(product, company);
					if (addPriceProductPageVo != null) {
						product.setPrice(addPriceProductPageVo.getPrice());
						product.setMarketPrice(addPriceProductPageVo.getMarketPrice());//设置市场价企业加价
					}
				}
			}
			model.addAttribute("page", page);
		}

		// 2.分组二级分类集合
		List<List<ProductCategory>> groupProductCategories = new ArrayList<>();
		Map<Integer, List<ProductCategory>> typeToCategoriesMap = new HashMap<>();
		for (ProductCategory productCategory : companyChildrenTwo) {
			Integer type = productCategory.getProductCategoryType();

			if (!typeToCategoriesMap.containsKey(type)) {
				typeToCategoriesMap.put(type, new ArrayList<ProductCategory>());
			}

			typeToCategoriesMap.get(type).add(productCategory);
		}
		groupProductCategories.addAll(typeToCategoriesMap.values());
		model.addAttribute("groupProductCategories", groupProductCategories);
		
		// 3.关联的活动数据
		String actId = sysCodeService.getValue(SysCode.SYS_GROUP, "tmall_shop_act");
		Act act = actService.find(Long.valueOf(actId));
		model.addAttribute("act", act);

		return "/shop/product/tmall_shop";
	}

	/**
	 * 查询指定分类下指定数量商品
	 * @param productCategoryId 分类id
	 * @param pageable 分页参数
	 * @author: GanSiquan
	 * @date: 2025-01-13 17:27
	 */
	@RequestMapping("/tmallProductList")
	public @ResponseBody Page<Product> productList(Long productCategoryId,Pageable pageable, HttpServletRequest request) {
		Member member = memberService.getCurrent();
		Company company = null;
		if (member != null) {
			company = memberService.getCurrent().getCompanyId();
		} else {
			HttpSession session = request.getSession();
			String companyIdStr = (String) session.getAttribute("companyIdStr");
			if (companyIdStr != null) {
				Long companyId = Long.valueOf(companyIdStr);
				company = companyService.find(companyId);
			} else {
				company = companyService.getDefultCompany();
			}
		}
		ProductCategory productCategory = productCategoryService.find(productCategoryId);
		// 供应商获取
		Supplier supplier = supplierService.getSupplierByPlatform(CommonAttributes.TMALL_SUPPLIER_ISOWEORDER);
		Page<Product> page = productService.findPage(productCategory, null, supplier, null, null, null, "3", null, null, true, true, null, false, null, new Pageable(1, 20), company, true, null, null, null, null, null);
		if (page != null && page.getContent() != null && !page.getContent().isEmpty()) {
			List<Product> products = page.getContent();

			// 企业加价
			for (Product product : products) {
				AddPriceProductPageVo addPriceProductPageVo = productAddPriceService.getProductAddPrice(product, company);
				if (addPriceProductPageVo != null) {
					product.setPrice(addPriceProductPageVo.getPrice());
					product.setMarketPrice(addPriceProductPageVo.getMarketPrice());//设置市场价企业加价
				}
			}
		}
		return page;
	}
}
