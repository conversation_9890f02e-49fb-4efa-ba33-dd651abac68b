/*@charset "utf-8";*/

[v-cloak] {
  display: none;
}
.taobao-page{

}
.taobao-page .hotWordSearchBox{
  width: 639px;
  height: 44px;
}
.fli_shop_search_section{
  margin-top: 34px;
}
.hotWordSearchBox .announcementsearchBox{
  height: 44px;
  line-height: 44px;
  width: 639px;
}
.hotWordSearchBox .announcementsearchBox #announcement3 li,
.hotWordSearchBox .announcementsearchBox #announcement4 li{
  height: 44px;
  line-height: 44px;
}
.main-content-header{
  background-image: linear-gradient(0deg,
  #f3feef 0%,
  #ffffff 100%),
  linear-gradient(
          #ffffff,
          #ffffff);
  background-blend-mode: normal, normal;
  height: 150px;
  display: flex
;
  align-items: center;
}
.search_section{
  position: relative;
  height: 46px;
  border: 1px solid #2abd00;
  background: #fff;
  border-radius: 8px 9px 9px 8px;
  padding: 0 0 0 8px;
  box-sizing: border-box;
}
.search_section input[type=text]{
  width: 639px;
  height: 44px;
  line-height: 44px;
  border: none;
  padding: 0 6px;
  color: #999;
}
.search_section .icon_search{
  display: block;
  position: absolute;
  right: 0;
  top: 0;
  width: 103px;
  height: 45px;
  background-color: #2abd00;
  border-radius: 0 8px 8px 0;
  font-family: AdobeHeitiStd-Regular;
  font-size: 20px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0;
  color: #ffffff;
  line-height: 44px;
  text-align: center;
}
.fli_shop_keywords{
  height: 14px;
  margin-top: 18px;
}
.fli_shop_keywords a{
  display: block;
  float: left;
  font-family: SourceHanSansSC-Normal;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #333333;
}
.fli_shop_keywords a:nth-child(-n+3){
  color: #f4210b;
}
.fli_shop_keywords a:not(:first-of-type){
  margin-left: 20px;
}
.text-ellipsis {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
.main-content-content{
  background: #ffffff;
  margin-top: 20px;
  border-radius: 16px;
 position: relative;
  padding: 14px 16px;
}
.ad{
  position: fixed;
  top: 290px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 260px;
}
.content-left{
  left: calc(50% - 1200px / 2);
  transform: translateX(-66%);
}
.content-right{
  right: calc(50% - 1200px / 2);
  transform: translateX(66%);
}
.fli_shop_header .nav_section {
  width: 236px;
  height: 320px;
  background-color: #f6f8fb;
  border-radius: 8px;
  float: left;
}
.fli_shop_header .nav_section .side-nav ul a:hover {
  color: #f4210b;
}
.fli_shop_header .nav_section > ul {
  /*padding-top: 10px;*/
}
.fli_shop_header .nav_section > ul > li > a {
  display: block;
  font-family: SourceHanSansSC-Medium;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 28px;
  letter-spacing: 0;
  color: #333333;
  width: calc(100% - 34px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.fli_shop_header .nav_section > ul > li.active ,
.fli_shop_header .nav_section > ul > li:hover  {
  background: #d5d5d5;

}
.fli_shop_header .nav_section > ul > li.active > ul,
.fli_shop_header .nav_section > ul > li:hover > ul {
  display: block;
}
.fli_shop_header .nav_section > ul > li > ul {
  display: none;
  position: absolute;
  background: #fff;
  width: 952px;
  padding: 24px 106px 24px 0;
  min-height: 390px;
  left:255px;
  top: 0;
  z-index: 999;
  box-shadow: 0 0 3px #31bf07;
}
.fli_shop_header .nav_section>ul>li>ul>li div a.right-title{
  width: 110px;
  font-size: 14px;
  color: #444;
  float: left;
  text-align: right;
  line-height: 38px;
  font-weight: 600;
}
.fli_shop_header .nav_section>ul>li>ul>li div ul{
  margin-left: 136px;
  padding: 8px 0;
  overflow: hidden;
  min-height: 38px;
}
.fli_shop_header .nav_section  li.right-item{
  float: left;
}
.fli_shop_header .nav_section a.right-content {
  font-size: 12px;
  color: #000;
  line-height: 22px;
  padding: 0 11px;
}
.fli_shop_header{
  height: 320px;
}
.swiper-container-content{
  float: left;
  width: calc(100% - 236px  - 242px - 32px);
  height: 100%;
  box-shadow: 0px 3px 7px 0px
  rgba(192, 192, 192, 0.35);
  border-radius: 8px;
  margin: 0 16px;
}
.swiper-container-content .swiper-container{
  height: 100%;
}
.swiper-container-content .swiper-container a{
  display: block;
  width: 100%;
  height: 100%;
}
.swiper-container-content .swiper-container a img{
  width: 100%;
  height: 100%;
  border-radius: 8px;
}
.swiper-button-prev{
  background-image: url(../img/boutique/swiper-left.png);
  left: 0;
  margin-top: -26px;
  height: 49px;
}
.swiper-button-next{
  background-image: url(../img/boutique/swiper-right.png);
  right: 0;
  margin-top: -26px;
  height: 49px;
}
.img-item{
  display: flex;
  width: 248px;
  height: 152px;
}
.img-content{
  height: 100%;
  float: left;
  width: 246px;
}
.data-list{
  height: 100%;
  float: left;
  flex-wrap: wrap;
  width: 242px;
}
.data-list .item:nth-of-type(2n){
  margin-left: 12px;
}
.data-list .item{
  width: 113px;
  height: 153px;
  cursor: pointer;
  /*background-image: linear-gradient(0deg,*/
  /*#ffffff 0%,*/
  /*#edfcff 100%),*/
  /*linear-gradient(*/
          /*#fff3f2,*/
          /*#fff3f2);*/
  /*background-blend-mode: normal,*/
  /*normal;*/
  border-radius: 8px;
  border: solid 1px #f6f8fb;
  padding: 4px 0;
}
/*.data-list .item:nth-of-type(2){*/
  /*background-image: linear-gradient(0deg,*/
  /*#ffffff 0%,*/
  /*#fff9ed 100%),*/
  /*linear-gradient(*/
          /*#fff3f2,*/
          /*#fff3f2););*/
/*}*/
/*.data-list .item:nth-of-type(3){*/
  /*background-image: linear-gradient(0deg,*/
  /*#ffffff 0%,*/
  /*#ffefed 100%),*/
  /*linear-gradient(*/
          /*#fff3f2,*/
          /*#fff3f2);*/
/*}*/
/*.data-list .item:nth-of-type(4){*/
  /*background-image: linear-gradient(0deg,*/
  /*#ffffff 0%,*/
  /*#eeffed 100%),*/
  /*linear-gradient(*/
          /*#fff3f2,*/
          /*#fff3f2);*/
/*}*/
.data-list .item p:first-of-type{
  font-family: SourceHanSansSC-Bold;
  font-size: 18px;
  font-weight: bold;
  font-stretch: normal;
  line-height: 30px;
  letter-spacing: 0px;
  color: #333333;
}
.data-list .item img{
  width: 63px;
  height: 54px;
}
.data-list .item p:last-of-type{
  font-family: SourceHanSansSC-Heavy;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  line-height: 30px;
  letter-spacing: 0px;
  color: #f4210b;
}
.data-list .item p:last-of-type span{
  font-size: 18px;
  /*font-weight: bold;*/
  /*letter-spacing: 0px;*/
  color: #f4210b;
}
.nav_section .allClass{
  font-family: SourceHanSansSC-Bold;
  font-size: 18px;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
  padding: 12px 17px 5px;
}
.swiper-container-horizontal>.swiper-pagination{
  text-align: left;
  padding: 0 30px;
}
.swiper-container-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background-color: #ffffff!important;
  border: solid 1px #c6c6c6;
  opacity: 1;
}
.swiper-container-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active{
  width: 18px;
  height: 10px;
  background-color: #959fa4!important;
  border-radius: 6px;
  border: solid 1px #ffffff;
}
.com-header-top{
margin: 35px 0 34px;
}
.com-header-top.product-recommendation .com-header-top-center{
  height: 62px;
  line-height: 62px;
}
.com-header-top.product-recommendation .com-header-top-center span{
  position: relative;
  color: #ffffff;
}
.com-header-top.product-recommendation .com-header-top-center:before,
.com-header-top.product-recommendation .com-header-top-center:after{
  width: 374px;
  height: 3px;
  background-color: #fdd2ce;
  border-radius: 1px;
  background-image:initial;
  top: 30px;
}
.com-header-top .com-header-top-center{
  width: 370px;
  height: 28px;
  position: relative;
  text-align: center;
  line-height: 28px;
  font-family: SourceHanSansSC-Bold;
  font-size: 22px;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #333333;
  margin: 0 auto;
}
.com-header-top .com-header-top-center:before{
  content: "";
  width: 385px;
  height: 23px;
  background-image: linear-gradient(90deg,
  #ffffff 0%,
  #ffefed 100%),
  linear-gradient(
          #fdd2ce,
          #fdd2ce);
  background-blend-mode: normal,
  normal;
  border-radius: 8px 8px 8px 0px;
  display: block;
  position: absolute;
  right: 379px;
  top: 4px;
}
.com-header-top .com-header-top-center:after{
  content: "";
  width: 385px;
  height: 23px;
  background-image: linear-gradient(90deg,
  #ffefed 0%,
  #ffffff 100%),
  linear-gradient(
          #fdd2ce,
          #fdd2ce);
  background-blend-mode: normal,
  normal;
  border-radius: 8px 8px 8px 0px;
  display: block;
  position: absolute;
  left: 379px;
  top: 4px;
}
.com-header-top .com-header-top-center img{
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
.store-introduction .store-introduction-item{
  width: calc(50% - 8px);
  height: 344px;
  background-image: linear-gradient(0deg,
  #ffefed 0%,
  #ffffff 100%),
  linear-gradient(
          #ffffff,
          #ffffff);
  background-blend-mode: normal,
  normal;
  border-radius: 12px;
  border: solid 1px #f6f8fb;
}
.store-introduction .store-introduction-item .header{
  height: 58px;
  font-family: SourceHanSansSC-Bold;
  font-size: 18px;
  font-weight: bold;
  font-stretch: normal;
  line-height: 30px;
  letter-spacing: 0px;
  color: #333333;
  padding: 0 30px;
}
.store-introduction .store-introduction-item .header>p{
  height: 100%;
}
.store-introduction .store-introduction-item .header>p img{
  margin-right: 20px;
}
.store-introduction .store-introduction-item .header span{
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 30px;
  letter-spacing: 0px;
  color: #f4210b;
}
.store-introduction .store-introduction-item .header span:after{
  content: " ";
  position: absolute;
  display: inline-block;
  height: 7px;
  width: 7px;
  border-width: 1px 1px 0 0;
  border-color: #f4210b;
  border-style: solid;
  -webkit-transform: matrix(-.71, -.71, .71, -.71, 0, 0) rotate(180deg);
  transform: matrix(-.71, -.71, .71, -.71, 0, 0) rotate(180deg);
  position: relative;
  top: -1px;
}
.brands .brand-item{
  width: calc(25% - 15px);
  float: left;
  height: 61px;
  background-color: #ffffff;
  background-color: #ffffff;
  border: solid 1px #e4e9f0;
  border-radius: 6px;
}
.brands .brand-item:not(:last-of-type){
  margin-right: 20px;
}
.brands .brand-item img{
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
}
.brands .brand-item.active{
  box-shadow: 0px 4px 7px 0px rgba(244, 33, 11, 0.38);
  border: solid 1px #f4210b;
  position: relative;
}
 .brand-content{
  background-color: #ffffff;
  box-shadow: 0px 4px 7px 0px rgba(244, 33, 11, 0.38);
  border: solid 1px #f4210b;
  padding: 20px;
  border-radius: 6px;
   margin: 17px 16px 0;
  font-family: SourceHanSansSC-Medium;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 0px;
  color: #333333;
   position: relative;
}
 .brand-content .triangle {
   position: absolute;
   top: -10px; /* 调整三角形的位置 */
   left: 50%;
   transform: translateX(-50%);
   width: 0;
   height: 0;
   border-left: 9px solid transparent;
   border-right: 9px solid transparent;
   border-bottom: 10px solid #f4210b;
 }

.brand-content .triangle::after {
  content: '';
  position: absolute;
  top: 2px; /* 调整白色三角形的位置 */
  left: -8px; /* 调整白色三角形的位置 */
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
}
.brands{
  padding: 0 29px;
}
.store-introduction.product-recommendation-box .store-introduction-item{
  height: 330px;
  background-image: linear-gradient(0deg,
  #ffefed 0%,
  #ffffff 100%),
  linear-gradient(
          #ffffff,
          #ffffff);
  background-blend-mode: normal,
  normal;
  border-radius: 12px;
  border: solid 4px #f4210b;
}
.store-introduction.product-recommendation-box .store-introduction-item .header{
  height: 48px;
  background-color: #f4210b;
  border-radius: 0px 0px 22px 22px;
  color: #fffefe;
  width: calc(100% - 32px);
  margin: 0 16px 0;
}
.store-introduction.product-recommendation-box  .store-introduction-item .header span{
  color: #fffefe;
}
.store-introduction.product-recommendation-box  .store-introduction-item .header span:after{
  border-color: #fffefe;
}
.product-list{
  padding: 15px 18px;
}
.product-list .product-list-item{
  width: calc(33.33% - 11.33px);
}
.product-list .product-list-item .img-box{
  background-color: #fffefe;
  border-radius: 1px;
}
.product-list .product-list-item .img-box img{
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
}
.product-list .product-list-item .product-name{
  font-family: SourceHanSansSC-Medium;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 20px;
  letter-spacing: 1px;
  color: #333333;
  margin: 12px 0 8px;
  text-align: justify;
  text-align-last: left;
  padding: 0 4px;
}
.product-list .product-list-item .price-box{
  font-family: SourceHanSansSC-Medium;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #999999;
  align-items: flex-end;
}
.product-list .product-list-item .price-box .price-content{
  font-family: SourceHanSansSC-Bold;
  font-size: 14px;
  font-weight: normal;
  letter-spacing: 0px;
  color: #f4210b;
}
.product-list .product-list-item .price-box .price-content span{
 font-size: 24px;
}
.nav{
  margin: 29px auto 24px;
    width: 100%;
  /*width: calc(100% - 82px - 82px);*/
}
.nav .item{
  width: 108px;
  height: 32px;
  background-color: #f6f8fb;
  border-radius: 4px;
  border: solid 1px #e4e9f0;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
}
.nav .item span{
  white-space: nowrap;
  display: block;
  max-width: 78px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.nav .item img{
  width: 26px;
  max-height: 100%;
  margin-right: 4px;
}
.nav  .active  .item{
  background-color: #2bbd00;
  color: #fffefe;
}
.product-list-content .advertisement-content{
  width: calc(20% - 8px);
  height: 336px;
  float: left;
}
.product-list-content .advertisement-content img{
  width: 100%;
  height: 100%;
}
.product-list-content .item{
  width: calc(20% - 8px);
  height: 336px;
  float: left;
  background-color: #ffffff;
  border-radius: 8px;
  border: solid 1px #e4e9f0;
  margin-bottom:10px ;
  cursor: pointer;
}
.product-list-content .item:nth-of-type(2n){
    margin-right: 10px;
    margin-left: 10px;
}
.product-list-content .item:nth-child(5n+1){
  margin-left:0;
}
.product-list-content .item:nth-child(10n+10){
    margin-right:0;
}
.product-list-content .item .img-box{
  height: 234px;
  border-radius: 8px 8px 0px 0px;
  position: relative;
}
.product-list-content .item .img-box .no_stock{
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  line-height: 26px;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  text-align: center;
}
.product-list-content .item .img-box img{
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  width: 100%;
  height: 100%;
}
.product-list-content .item .title{
    height: 48px;
  font-family: SourceHanSansSC-Medium;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #333333;
  margin: 12px 0 8px;
  text-align: justify;
  text-align-last: left;
  padding: 0 20px;
  font-family: SourceHanSansSC-Medium;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #333333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product-list-content .item .price-box{
  padding: 0 20px;
  align-items: flex-end;
}
.product-list-content .item .price-box .price-content{
  font-weight: normal;
  font-family: SourceHanSansSC-Bold;
  font-size: 14px;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #f4210b;
}
.product-list-content .item .price-box .price-content span{
  font-size: 24px;
  color: #f4210b;
}
.product-list-content .item .price-box span{
  font-family: SourceHanSansSC-Medium;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #999999;
}
.data-list .item .image-box img{
  width: 100%;
  height: auto;
  max-height: 100%;
}
.data-list .item  .image-box{
  width: calc(100% - 10px);
  height: 110px;
}
.no-more{
  text-align: center;
}
.loadingbox img{
  width: 22px;
  margin-right: 4px;
}
.taobao-page .product-list-content-box {
    height: 1383px;
}
.product-list-content.reserve-seat .item .img-box{
    background: #F2F2F2;
}
.product-list-content.reserve-seat .item .title{
    background: #F2F2F2;
    width: 66%;
}
.product-list-content.reserve-seat .item .price-box{
    background: #F2F2F2;
    width: 30%;
    height: 24px;
}