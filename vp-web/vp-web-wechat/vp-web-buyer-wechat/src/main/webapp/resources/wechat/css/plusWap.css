@charset "utf-8";
.cakeCartPage .classContentBox{
  padding-left: 0.4533rem;
  padding-right: 0.8267rem;
}
.cakeCartPage .popbg{
  /*display: block;*/
}
.cakeCartPage .popbg .selectArgsBox .selectArgs > header{
  margin-top: 0.56rem;
  margin-left: 0.32rem;
  height: 2.24rem;
}
.order_items_section ul li .item .title .margin_btw12{
  margin-bottom: 0.12rem;
}
.order_items_section ul li .item .title .enterprise_payment{
  padding: 0.08rem 0.18rem;
  background: #d7e5fd;
  color: #6189fc;
  font-size: 0.32rem;
  border-radius: 0.16rem;
}
.cakeCartPage .popbg .selectArgsBox .selectArgs > header .img {
  top: 0;
  width: 2.24rem;
  height: 2.24rem;
}
.cakeCartPage .popbg .selectArgsBox .selectArgs > header .img img{
  width: 100%;
  height: 100%;
}
.cakeCartPage .popbg .selectArgsBox .selectArgs > header .intro{
  left: 2.5rem;
}
.cakeCartPage .selectArgsBox  .icon_close{
  background: rgb(241,241,241);
  width: 0.533rem;
  height: 0.533rem;
  border-radius: 50%;
  text-align: center;
  position: absolute;
  right: 0.667rem;
  top: 0.8267rem;
}
.cakeCartPage .selectArgsBox  .icon_close img{
  width: 0.267rem;
}
.cakeCartPage .selectArgsBox{
  border-radius: 0.32rem 0.32rem 0 0;
}
.cakeCartPage .classContent h3{
  color: rgb(51,51,51);
  font-size: 0.3733rem;
  height: 0.88rem;
  line-height: 0.88rem;
}
.cakeCartPage .classContent ul li{
  color: rgb(51,51,51);
  font-size: 0.32rem;
  display: inline-block;
  background: rgb(241,241,241);
  height: 0.5867rem;
  line-height: 0.5867rem;
  padding: 0 0.48rem;
  border-radius: 0.29335rem;
  margin-right: 0.3467rem;
  margin-bottom: 0.3467rem;
}
.cakeCartPage .classContent ul li.active{
  color: rgb(55,154,255);
  background: rgb(204,229,255);
}
.cakeCartPage .productName{
  background: #ffffff;
  height: 1.0133rem;
  color: rgb(51,51,51);
  font-size: 0.3467rem;

}
.cakeCartPage.CartPage .item .col3{
  height: 100%;
}
.cakeCartPage.CartPage .item .col3 .colsleftContent{
  width: calc(100% - 2.8rem);
  float: left;
}
.cakeCartPage.CartPage .item .col3 .colsrightContent{
  width: 2.8rem;
  float: left;
  text-align: right;
}
.cakeCartPage.CartPage .item .col3 .colsrightContent .originPrice{
  color: rgb(153,153,153);
  font-size: 0.37rem;
  text-decoration: line-through;
}
.cakeCartPage.CartPage  .item .col3 .num_count_box{
  background: none;
  bottom: 0;
}
.cakeCartPage.CartPage .num_count_box input{
  background: rgb(241,241,241);
}
.cakeCartPage.CartPage .num_count_box .decrease,.cakeCartPage.CartPage .num_count_box .increase{
  width: 0.7rem;
}
.cakeCartPage.CartPage .num_count_box .decrease span{
  background: url("./../img/cake/other/jian.png")  no-repeat center center;
  background-size: contain;
  width: 0.32rem;
  margin-left: 0.19rem;
  float: none;
}
.cakeCartPage.CartPage .num_count_box .increase span{
  background: url("./../img/cake/other/add.png")  no-repeat center center;
  background-size: contain;
  width: 0.32rem;
  margin-left: 0.19rem;
  float: none;
}
.cakeCartPage .productName{
margin-top: 0.2667rem;
}
.cakeCartPage div.productName:nth-of-type(1){
margin-top: 0;
}
.cakeCartPage .productName .fli_checkbox_blue{
margin: 0 0.25rem;
}
.cakeCartPage.CartPage .item .col3 .attr{
  max-width: 100%;
  position: relative;
  padding-right: 0.4rem;
  display: inline-block;
}
.cakeCartPage.CartPage .item .col3 .attr:after{
  /*content: '';*/
  /*position: absolute;*/
  /*margin-top: 0rem;*/
  /*right: 0rem;*/
  /*height: 0.18rem;*/
  /*width: 0.2rem;*/
  /*border-top: 0.05rem solid rgb(178, 178, 178);*/
  /*border-right: 0.05rem solid rgb(178, 178, 178);*/
  /*transform: rotate(135deg);*/
  /*top: 0.08rem;*/
}
.cakeDetailPage .popbg .selectArgsBox .content_section .select_item header{
  color: rgb(51,51,51);
  font-size: 0.3733rem;
  height: 0.88rem;
  line-height: 0.88rem;
  font-weight: bold;
}
.cakeDetailPage .popbg .selectArgsBox .content_section .select_item .color li{
  border: none;
  color: rgb(51,51,51);
  font-size: 0.32rem;
  display: inline-block;
  background: rgb(241,241,241);
  height: 0.5867rem;
  line-height: 0.5867rem;
  padding: 0 0.48rem;
  border-radius: 0.29335rem;
  margin-right: 0.3467rem;
  margin-bottom: 0.3467rem;
}
.cakeDetailPage .popbg .selectArgsBox .content_section .select_item .color li.active{
  color: rgb(55,154,255);
  background: rgb(204,229,255);
}
.cakeListPage .btn_selectable{
  width: auto;
  padding: 0 0.48rem;
  border-radius: 0.4rem;
  max-width: 4rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.cakeListPage .btn_selectable.active{
  background: rgb(204,229,255);
  color: rgb(55,154,255);
  border: none!important;
  padding: 0 0.48rem;

}
.cakeListPage .cakeListSection .productUl{
  padding: 0.08rem 0.32rem;
}

.cakeListPage  .goods_list_section ul li:nth-child(2n-1){
  margin-right: 0.16rem;
}
.cakeListPage  .goods_list_section ul li:nth-child(2n){
  margin-left: 0.16rem;
}
.cakeListPage  .goods_list_section ul.productUl{
  margin-top: 0.16rem;
}
.cakeListPage .goods_list_section ul li a .img img{
  width: 3.3066667rem;
}

.cakeListPage  .goods_list_section ul li a{
  position: relative;
}
.cakeListPage .cakeListSection .productUl li .brandName{
  position: absolute;
  top: 0;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.2933rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0;
  color: #ffffff;
  background-color: #fb2818;
  border-radius: 0.2133rem 0;
  left: 0;
  padding: 0.06rem 0.32rem;
  max-width: 98%;
  width: 2.2rem;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
}
.cakeListPage .cakeListSection .productUl li .tabList{
  padding: 0 0.32rem;
  margin-bottom: 0.12rem;
}
.cakeListPage .cakeListSection .productUl li .tabList .tab{
  height: 0.4267rem;
  border-radius: 0.0133rem;
  border: solid 1px #fb2818;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: SourceHanSansSC-Regular;
  font-size: 0.2667rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.0133rem;
  color: #ff5726;
  padding: 0 0.24rem;
  float: left;
  border-radius: 0.06rem;
}
.cakeListPage .cakeListSection .productUl li .tabList .tab:not(:last-of-type){
margin-right: 0.2rem;
}
.cakeListPage .cakeListSection .productUl li .price .shop,.cakeListPage .cakeListSection .productUl li .price .market{
  line-height: normal;
}
.cakeListPage .cakeListSection .productUl li .price .couponModeShop{
  line-height: normal;
  font-size: 0.32rem;
  color: #fd1b3b;
  margin-left: 0.32rem;
}
.cakeListPage .cakeListSection .productUl li .price .couponModeShop .num{
  font-size: 0.4rem;
}
.hideOverflow{
  touch-action: none;
  overflow: hidden;
}
.searchPage .goods_list_section.hidden {
  position: relative;
  touch-action: none;
}
.banner_section img {
  display: block;
  width: 100%;
  height: auto;
}
.banner_section {
  position: relative;
  overflow: hidden;
}
.banner_section .banner_count_down {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 9;
  font-size: 0.266666rem;
  color: #333;
  width: 4.306666rem;
  height: 0.8rem;
  padding-top: 0.15rem;
  padding-left: 0.533333rem;
  background: url(../img/count_bg.png) center center / contain no-repeat;
}
.banner_section .banner_count_down .icon_clock_v2 {
  display: inline-block;
  vertical-align: middle;
  width: 0.346666rem;
  height: 0.373333rem;
  margin-right: 0.08rem;
  margin-top: -0.04rem;
  background: url(../img/icon_clock_v2.png) center center / contain no-repeat;
}
.banner_section .banner_count_down .count_time {
  display: inline-block;
  vertical-align: middle;
  margin-left: 0.2rem;
}
.banner_section .banner_count_down .count_time span {
  display: inline-block;
  width: 0.4rem;
  height: 0.533333rem;
  line-height: 0.533333rem;
  text-align: center;
  background: #111a23;
  color: #ffffff;
  margin: 0 0.066666rem;
}
.banner_section .swiper-container {
  height: 5.17333333rem;
  width: 100%;
}
.banner_section .swiper-container a.swiper-slide {
  display: block;
  width: 100%;
  height: 5.17333333rem;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  overflow: hidden;
}
.banner_section .poster-main {
  width: 100%;
}
.banner_section .poster-main .poster-btn {
  height: 2rem;
}
.middle_section {
  width: 100%;
  background-color: #fff;
}
.middle_section ul {
  overflow: hidden;
  display: flex;
  justify-content: flex-start;
  flex-wrap: nowrap;
}
.middle_section ul li {
  position: relative;
  display: block;
  width: 20%;
  padding-top: 0.25333333rem;
  padding-bottom: 0.25333333rem;
}
.middle_section ul li a {
  display: block;
}
.middle_section ul li a .img {
  width: 1.3733rem;
  height: 1.3733rem;
  margin: 0 auto;
  border-radius: 100%;
  overflow: hidden;
}
.middle_section ul li a .img img {
  width: 1rem;
  height: auto;
  margin: 0.18rem auto;
}
.middle_section ul li a .text {
  text-align: center;
  font-size: 0.32rem;
  color: #444444;
  margin-top: 0.13333333rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.index_discount_module {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0.25333333rem 0.1rem;
  flex-wrap: nowrap;
  background-color: #fff;
  border-top: 0.01rem solid #f5f5f5;
}
.index_discount_module .img {
  width: 32%;
  height: auto;
  overflow: hidden;
}
.discount_good_lists {
  margin-top: 0.32rem;
}
.discount_good_lists header {
  background: url(../img/discount_flag.png) left center no-repeat;
  background-size: auto 100%;
  height: 0.933333rem;
  line-height: 0.9rem;
  border-bottom: 1px solid #ebeef0;
  background-color: #fff;
  overflow: hidden;
  position: relative;
}
.discount_good_lists header .v2_header .label {
  font-size: 0.3rem;
  color: #f96472;
  margin-left: 0.2rem;
  font-weight: 600;
}
.discount_good_lists header .v2_header .hours,
.discount_good_lists header .v2_header .minutes,
.discount_good_lists header .v2_header .seconds {
  color: #fff;
  display: inline-block;
  vertical-align: middle;
  border-radius: 0.1rem;
  padding: 0.01rem 0.08rem;
  background-color: #f96472;
  line-height: 0.45rem;
  margin: 0 0.06rem;
  font-weight: lighter;
  font-size: 0.28rem;
  margin-top: -0.0533rem;
}
.discount_good_lists header .enter {
  position: absolute;
  top: 0;
  width: 1.8rem;
  color: #f95842;
  font-size: 0.32rem;
  background: url(../img/jd/icon_floor_link.png) right center no-repeat;
  background-size: 0.44rem 0.44rem;
  right: 0.3rem;
}
.discount_good_lists .content {
  overflow: hidden;
  background: #fff;
  padding: 0.32rem 0;
}
.discount_good_lists .content .item {
  display: block;
  width: 25%;
  padding: 0 0.2rem;
  float: left;
}
.discount_good_lists .content .item .img {
  width: 2rem;
  height: 2rem;
  margin: 0 auto;
  overflow: hidden;
}
.discount_good_lists .content .item .img img {
  display: block;
  width: 2rem;
  height: 2rem;
  object-fit: contain;
}
.discount_good_lists .content .item .price {
  margin-top: 0.16rem;
  text-align: center;
}
.discount_good_lists .content .item .price span {
  display: block;
}
.discount_good_lists .content .item .price .shop {
  font-size: 0.34rem;
  color: #ff1e00;
}
.discount_good_lists .content .item .price .market {
  font-size: 0.26rem;
  color: #999999;
  text-decoration: line-through;
}
.you_favorite {
  margin-top: 0.2rem;
}
.you_favorite header {
  background: url(../img/you_favorite.png) center no-repeat;
  background-size: auto 75%;
  height: 0.933333rem;
  line-height: 0.9rem;
  border-bottom: 1px solid #ebeef0;
  background-color: #fff;
}
.nav_section {
  width: 100%;
  background: #ffffff;
}
.nav_section.block_50 {
  background: none;
}
.nav_section.block_50 ul {
  padding: 0.133333rem;
}
.nav_section.block_50 ul li {
  width: 50%;
  padding: 0.133333rem;
}
.nav_section.block_50 ul li a {
  padding: 1.2rem 0;
  background: #fff;
  border-radius: 0.133333rem;
}
.nav_section.block_50 ul li a .img {
  position: relative;
  left: 50%;
  margin-left: -1.2rem;
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  width: 2.4rem;
  height: 2.4rem;
}
.nav_section.block_50 ul li a .img img {
  display: inline-block;
  width: 50%;
}
.nav_section.block_50 ul li a .text {
  font-size: 0.4rem;
  margin-top: 0.4rem;
}
.nav_section.block_50 ul li + li {
  border-left: none;
}
.nav_section ul {
  overflow: hidden;
}
.nav_section ul li {
  position: relative;
  display: block;
  width: 25%;
  float: left;
  padding-top: 0.25333333rem;
  padding-bottom: 0.25333333rem;
}
.nav_section ul li + li {
  border-left: 1px solid #ebebeb;
}
[data-dpr="1"] .nav_section ul li + li {
  border-left: none;
}
[data-dpr="1"] .nav_section ul li + li:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 1px;
  border-left: 1px solid #ebebeb;
  color: #ebebeb;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
}
[data-dpr="1"] .nav_section.block_50 ul li + li:before {
  border-left: none;
}
.nav_section ul li a {
  display: block;
}
.nav_section ul li a .img {
  width: 1.57333333rem;
  height: 1.57333333rem;
  margin: 0 auto;
  border-radius: 100%;
  overflow: hidden;
}
.nav_section ul li a .img img {
  width: 0.906666rem;
  height: auto;
  margin: 0.34rem auto;
}
.nav_section ul li a .text {
  text-align: center;
  font-size: 0.32rem;
  color: #444444;
  margin-top: 0.13333333rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.weekly_welfare_section {
  margin-top: 0.24rem;
}
.weekly_welfare_section .header {
  position: relative;
  height: 0.88rem;
  line-height: 0.85333333rem;
  text-align: center;
  color: #4993fa;
  background: #fff;
}
.weekly_welfare_section .header:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.weekly_welfare_section .header:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.weekly_main_section {
  margin-bottom: 0.26666667rem;
}
.weekly_main_section .swiper-container.weekly_scroll {
  background: #fff;
  margin-bottom: 0.26666667rem;
}
.weekly_main_section .swiper-container.weekly_scroll .swiper-slide + .swiper-slide {
  position: relative;
}
.weekly_main_section .swiper-container.weekly_scroll .swiper-slide + .swiper-slide:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 1px;
  border-left: 1px solid #ebebeb;
  color: #ebebeb;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
}
.weekly_main_section .swiper-container.weekly_scroll .time {
  width: 1.6rem;
  padding-top: 0.26666667rem;
  padding-bottom: 0.26666667rem;
  margin: 0 auto;
}
.weekly_main_section .swiper-container.weekly_scroll .time p {
  text-align: center;
  color: #999999;
  font-size: 0.32rem;
  font-size: 0.38rem;
}
.weekly_main_section .swiper-container.weekly_scroll .time p:first-child {
  font-size: 0.34rem;
}
.weekly_main_section .swiper-container.weekly_scroll .time.active {
  border-bottom-width: 0.05333333rem;
  border-bottom-style: solid;
  border-bottom-color: #4993fa;
}
.weekly_main_section .swiper-container.weekly_scroll .time.active p {
  color: #4993fa;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .swiper-slide {
  padding-left: 0.4rem;
  padding-right: 0.4rem;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .pic_section {
  border-top-left-radius: 0.13333333rem;
  border-top-right-radius: 0.13333333rem;
  overflow: hidden;
  height: 4.50666667rem;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .pic_section img {
  display: block;
  width: 100%;
  height: 4.50666667rem;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .goods_description {
  background: #fff;
  padding-left: 0.4rem;
  padding-right: 0.4rem;
  padding-bottom: 0.4rem;
  border-bottom-left-radius: 0.13333333rem;
  border-bottom-right-radius: 0.13333333rem;
  overflow: hidden;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .goods_description .title {
  position: relative;
  font-size: 0.46rem;
  color: #444;
  line-height: 1.06666667rem;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .goods_description .title:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #d6d6d6;
  color: #d6d6d6;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.weekly_main_section .swiper-container.weekly_scroll_detail .goods_description .fultitle {
  font-size: 0.34rem;
  color: #999;
  line-height: 0.4rem;
  padding-top: 0.13333333rem;
  padding-bottom: 0.13333333rem;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .count_time {
  text-align: center;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .count_time img {
  display: inline-block;
  vertical-align: middle;
  width: 0.64rem;
  height: 0.74666667rem;
  margin-top: -0.05333333rem;
  margin-right: -0.08rem;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .count_time p {
  display: inline-block;
  vertical-align: middle;
  font-size: 0.26rem;
  color: #999;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .count_time p.gray {
  display: block;
  text-align: center;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .count_time .btn_submit_long {
  margin-top: 0.24rem;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .count_time .clock {
  display: inline-block;
  vertical-align: middle;
  font-size: 0.26rem;
  color: #fe6029;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .count_time .clock + .btn_index_long {
  margin-top: 0.21333333rem;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .count_time .input_btn_group {
  display: block;
  overflow: hidden;
  width: 6.66666667rem;
  margin-top: 0.13333333rem;
  margin-bottom: 0.13333333rem;
  margin-left: auto;
  margin-right: auto;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .count_time .input_btn_group .input_info {
  display: block;
  width: 4.66666667rem;
  height: 0.8rem;
  border-width: 0.03rem;
  border-style: solid;
  border-color: #fe6029;
  font-size: 0.32rem;
  line-height: 0.74666667rem;
  background: #fff;
  float: left;
  color: #444;
  padding: 0rem 0.26666667rem;
}
.weekly_main_section .swiper-container.weekly_scroll_detail .count_time .input_btn_group .btn_tip {
  display: block;
  height: 0.8rem;
  line-height: 0.8rem;
  background: #fe6029;
  border: none;
  font-size: 0.4rem;
  float: left;
  color: #fff;
  width: 2rem;
}
.tab_switch_section .tab_theme_section {
  background: #f0f0f0;
  margin: 0;
}
.tab_switch_section article {
  padding-top: 0.26666667rem;
  padding-bottom: 0.26666667rem;
}
.tab_switch_section article ul {
  display: none;
  text-align: center;
  overflow: hidden;
  padding-left: 0.37333333rem;
  padding-right: 0.37333333rem;
}
.tab_switch_section article ul:first-child {
  display: block;
}
.tab_switch_section article ul li {
  position: relative;
  display: block;
  width: 32%;
  height: 3.6rem;
  padding-top: 0.53333333rem;
  padding-bottom: 0.50666667rem;
  background: #fff;
  margin-top: 0.10666667rem;
  margin-bottom: 0.10666667rem;
  border-radius: 0.2rem;
  float: left;
}
.tab_switch_section article ul li:hover {
  box-shadow: 0 0 0.2rem rgba(52, 109, 252, 0.1);
}
.tab_switch_section article ul li + li:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 1px;
  border-left: 1px solid #ebebeb;
  color: #ebebeb;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
}
.tab_switch_section article ul li:nth-child(3n-1) {
  margin-left: 2%;
  margin-right: 2%;
}
.tab_switch_section article ul li a {
  display: block;
  text-align: center;
}
.tab_switch_section article ul li a .img {
  display: inline-block;
  width: 1.81333333rem;
  height: 1.81333333rem;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  border-radius: 100%;
}
.tab_switch_section article ul li a .img img {
  width: 0.906666rem;
  height: auto;
  margin: 0.453333rem auto;
}
.tab_switch_section article ul li a .text {
  text-align: center;
  font-size: 0.4rem;
  color: #444444;
  margin-top: 0.2rem;
}
.sortSection {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  /* Safari */
  display: box;
  display: flex;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  -webkit-align-items: flex-end;
  align-items: flex-end;
  margin-top: 0.03rem;
  background: #fff;
  box-shadow: 0 0 0.1333rem rgba(0, 0, 0, 0.2);
}
.sortSection.fixed {
  position: fixed;
  left: 0;
  top: 1.2rem;
  right: 0;
  z-index: 888;
}
.sortSection.fixed:before {
  content: " ";
  position: absolute;
  left: 0;
  right: 0;
  top: -0.2rem;
  display: block;
  width: 100%;
  height: 0.2667rem;
  background: #fff;
  z-index: 666;
}
.sortSection li {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  flex-grow: 1;
  text-align: center;
}
.sortSection li:last-child a i {
  width: 0.33333333rem;
  height: 0.33333333rem;
  margin-top: -0.06666667rem;
  background: url(../img/icon_filter.png) center center / contain no-repeat;
}
.sortSection li a {
  display: block;
  width: 1.86666667rem;
  line-height: 1.04rem;
  color: #999;
  text-align: center;
  border-bottom-width: 0.09333333rem;
  border-bottom-style: solid;
  border-bottom-color: transparent;
  margin: 0 auto;
}
.sortSection li a.defaultScreen{
  position: relative;
}
.sortSection li a.defaultScreen #TotalGoods{
  position: absolute;
  font-size: 0.32rem;
  bottom: -0.04rem;
  transform: scale(0.9);
  left: 1.38rem;
  color: #adadad;
  white-space: nowrap;
}
.sortSection li a i {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.08rem;
  width: 0.13333333rem;
  height: 0.26666667rem;
  margin-left: 0.08rem;
  background: url(../img/icon_sort.png) center center / contain no-repeat;
}
.sortSection li.active a {
  border-bottom-color: #4993fa;
}
.sortSection li.active.top a i {
  background: url(../img/icon_sort_top.png) center center / contain no-repeat;
}
.sortSection li.active.down a i {
  background: url(../img/icon_sort_down.png) center center / contain no-repeat;
}
.goods_list_section {
  padding-bottom: 0.26666667rem;
}
.tab_theme_section_content.goods_list_section>ul{
  min-height: 1.6rem;
}
.goods_list_section ul {
  overflow: hidden;
  padding: 0.08rem 0.24rem;
}
.product_classification_list ul{
  padding: 0rem 0rem;
}
.cakeListPage .goods_list_section ul.productUl .price-padingleft{
  padding-left: 0.32rem;
}
.goods_list_section ul li {
  position: relative;
  display: block;
  width: 49%;
  height: 6.90666667rem;
  margin-top: 0.08rem;
  margin-bottom: 0.08rem;
  float: left;
  background-color: #ffffff;
  padding: 0.13333333rem;
  border-radius: 0.24rem;
}
.goods_list_section ul li .tag_sale {
  position: absolute;
  left: 0.26666667rem;
  top: 0rem;
  width: 0.85333333rem;
  height: 0.72rem;
  line-height: 0.58666667rem;
  font-size: 0.32rem;
  text-align: center;
  color: #fff;
  background: url(../img/tag_sale.png) center center / contain no-repeat;
  z-index: 99;
}
.goods_list_section ul li:nth-child(2n-1) {
  margin-right: 1%;
}
.goods_list_section ul li:nth-child(2n) {
  margin-left: 1%;
}
.selfShopNew2Page .goods_list_section ul li:nth-child(2n-1),
.freshShopPage .goods_list_section ul li:nth-child(2n-1){
  margin-right: 0.16rem;
}
.selfShopNew2Page .goods_list_section ul li:nth-child(2n),
.freshShopPage .goods_list_section ul li:nth-child(2n){
  margin-left: 0.16rem;
}
.selfShopNew2Page .goods_list_section ul li,
.freshShopPage .goods_list_section ul li{
  width: calc(50% - 0.16rem);
  margin-top: 0;
  margin-bottom: 0.32rem;
}
.selfShopNew2Page .product_classification_list .nav{
  margin-bottom: 0.32rem;
}
.goods_list_section ul li:hover {
  box-shadow: 0 0 0.2rem rgba(52, 109, 252, 0.1);
}
.goods_list_section ul li a {
  display: block;
}
.goods_list_section ul li a .img {
  position: relative;
  width: 100%;
  height: 4.34666667rem;
  margin-bottom: 0.13333333rem;
  overflow: hidden;
  text-align: center;
}
.goods_list_section ul li a .img img {
  display: inline-block;
  width: 4.34666667rem;
  height: 4.34666667rem;
  border: none;
  /*object-fit: contain;*/
}
.goods_list_section ul li a .img span.t_circle {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -1.06666667rem;
  margin-top: -1.06666667rem;
  display: block;
  width: 2.13333333rem;
  height: 2.13333333rem;
  line-height: 2.13333333rem;
  font-size: 0.34rem;
  color: #ffffff;
  border-radius: 100%;
  text-align: center;
}
.goods_list_section ul li a .img span.t_circle.end {
  background: rgba(35, 35, 35, 0.69);
}
.goods_list_section ul li a .img span.t_circle.coming_soon {
  background: rgba(47, 199, 151, 0.69);
}
.goods_list_section ul li a p.title {
  color: #444;
  height: 1.2rem;
  line-height: 0.4rem;
  font-size: 0.32rem;
  overflow: hidden;
  text-align: left;
}
.goods_list_section ul li p.price {
  font-size: 0.3rem;
  line-height: 0.54rem;
  margin-top: 0.3rem;
  overflow: hidden;
}
.goods_list_section ul li p.price > span {
  display: block;
}
.goods_list_section ul li p.price > span.shop,
.goods_list_section ul li p.price > span.huaqiaocheng {
  float: left;
  color: #fd1b3b;
  font-size: 0.4rem;
  line-height: 0.54rem;
}
.goods_list_section ul li p.price > span.shop b,
.goods_list_section ul li p.price > span.huaqiaocheng b {
  font-size: 0.32rem;
  font-weight: normal;
}
.goods_list_section ul li p.price > span:only-child {
  float: none;
  text-align: center;
}
.goods_list_section ul li p.price span.huaqiaocheng:after {
  content: "积分";
  margin-left: 0.1rem;
  font-size: 0.26rem;
  line-height: 0.54rem;
  vertical-align: middle;
}
.carIndexListPage .goods_list_section ul li p.price span.market{
  width: auto;
  margin-left: 0;
}
.goods_list_section ul li p.price span.market {
  width: 40%;
  float: left;
  color: #999;
  text-decoration: line-through;
  margin-left: 0.2rem;
  text-align: center;
}
.goods_list_section ul li p.price .icon_cart {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 1rem;
  height: 1rem;
  background: url(../img/jd/icon_top_cart_d.png) center center no-repeat;
  background-size: 50%;
}
.icon_arrow {
  display: inline-block;
  width: 0.42666667rem;
  height: 0.24rem;
  background: url(../img/icon_arrow.png) center center no-repeat;
  background-size: contain;
  transform-origin: center center;
  -ms-transform-origin: center center;
  -webkit-transform-origin: center center;
}
.icon_arrow.icon_arrow_right {
  transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  /* IE 9 */
  -webkit-transform: rotate(-90deg);
  /* Safari and Chrome */
}
.CartPage {
  padding-bottom: 1.33333333rem;
}
.CartPage .tab_theme_section {
  margin-top: 0.133333rem;
  margin-bottom: 0;
}
.CartPage .item .col3 a.icon_fav_cart {
  display: block;
  position: absolute;
  right: 0.4rem;
  bottom: 0;
  width: 0.533333rem;
  height: 0.533333rem;
  background: url(../img/icon_cart_bg_red.png) center center / contain no-repeat;
}
.CartPage .item .col3 a.icon_fav_cart:before {
  content: "";
  position: absolute;
  top: -0.26666667rem;
  left: -0.26666667rem;
  right: -0.26666667rem;
  bottom: -0.26666667rem;
}
.CartPage .tag_sale {
  display: inline-block;
  width: 0.73333333rem;
  height: 0.4rem;
  line-height: 0.4rem;
  text-align: center;
  color: #fff;
  background: #ff0030;
}
.CartPage .sale_header {
  position: relative;
  font-size: 0.32rem;
  margin-top: 0.10666667rem;
  padding: 0.32rem 0.1333rem;
  background: #fff;
  color: #444;
  line-height: 0.4rem;
  border-bottom: 1px solid #e5e5e5;
}
.CartPage .sale_header .minus {
  color: red;
}
.CartPage .sale_header a {
  float: right;
  color: #4993fa;
}
.CartPage #line_split + .item {
  margin-top: 0.10666667rem;
}
.CartPage #line_split + .item:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.CartPage .public_top_header .text_right {
  right: 1.2rem;
}
[data-dpr="1"] .CartPage .sale_header {
  border-bottom: none;
}
[data-dpr="1"] .CartPage .sale_header:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.CartPage .item {
  position: relative;
  background: #fff;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  border-bottom: 1px solid #e5e5e5;
}
.CartPage .item .flex_line {
  position: relative;
  left: 0;
  display: -webkit-box;
  display: -webkit-flex;
  /* Safari */
  display: box;
  display: flex;
  -webkit-align-items: center;
  align-items: center;
  width: 100%;
  height: 2.568rem;
  padding-top: 0.21333333rem;
  padding-bottom: 0.21333333rem;
  transition: left 0.5s;
  -webkit-transition: left 0.5s;
}
.CartPage .item .sDelBtn {
  position: absolute;
  left: 100%;
  top: 0;
  height: 2.568rem;
  line-height: 2.568rem;
  width: 2.666666rem;
  color: #fff;
  background: #fb273b;
  text-align: center;
  font-size: 0.4rem;
  margin-left: 0;
  transition: margin-left 0.5s;
  -webkit-transition: margin-left 0.5s;
}
.CartPage .item .sale_info {
  position: relative;
  display: block;
  margin-left: 1rem;
  margin-right: 0.26rem;
  margin-bottom: 0.16rem;
  font-size: 0.32rem;
  padding: 0.2rem;
  background: #fff9f9;
  color: #666;
  line-height: 0.32rem;
}
.CartPage .item .sale_info a {
  display: block;
  float: right;
  line-height: 0.4rem;
}
.CartPage .item .sale_info:after {
  content: " ";
  display: block;
  font-size: 0;
  height: 0;
  clear: both;
  visibility: hidden;
}
.CartPage .item .sale_info .free_fee {
  padding-top: 0.16rem;
}
.CartPage .item .sale_info .free_fee .tag_sale {
  position: relative;
  margin-right: 0.16rem;
  background: transparent;
  border: 1px solid #ff0030;
  color: #ff0030;
  border-radius: 0.053333rem;
}
.CartPage .item .sale_info .free_fee:only-child {
  padding-top: 0;
}
[data-dpr="1"] .CartPage .item {
  border-bottom: none;
}
[data-dpr="1"] .CartPage .item:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
[data-dpr="1"] .CartPage .item .sale_info .free_fee .tag_sale {
  border: none;
}
[data-dpr="1"] .CartPage .item .sale_info .free_fee .tag_sale:after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid #ff0030;
  -webkit-transform: scale(0.5);
  transform: scale(0.5);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: 10px;
  z-index: 0;
  pointer-events: auto;
}
[data-dpr="1"] .CartPage .item .sale_info .free_fee .tag_sale:after {
  border-radius: 0.053333rem;
}
.CartPage .item .col1 {
  width: 1rem;
  flex-shrink: 0;
  -webkit-flex-shrink: 0;
}
.CartPage .item .col1 .fli_checkbox_blue {
  margin: 0 auto;
}
.CartPage .item .col2 {
  width: 2.4rem;
  flex-shrink: 0;
  -webkit-flex-shrink: 0;
}
.CartPage .item .col2 .img {
  position: relative;
  display: block;
  width: 2.13333333rem;
  height: 2.13333333rem;
}
.CartPage .item .col2 .img img {
  height: 2.13333333rem;
}
.CartPage .item .col3 {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  flex-grow: 1;
  line-height: 0.58rem;
  padding-right: 0.26rem;
  width: 4rem;
}
.CartPage .item .col3 a {
  display: block;
  width: 100%;
}
.CartPage .item .col3 .title {
  font-size: 0.37333333rem;
  line-height: 0.4266666rem;
  height: 0.85333334rem;
  color: #444444;
  overflow: hidden;
}
.CartPage .item .col3 .shop {
  display: block;
  height: 0.50666667rem;
  line-height: 0.50666667rem;
  color: #fd1b3b;
  font-size: 0.37333333rem;
  margin-left: -0.05333333rem;
}
.CartPage .item .col3 .attr {
  height: 0.45333333rem;
  line-height: 0.45333333rem;
  max-width: 50%;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #999999;
  font-size: 0.32rem;
}
.CartPage .item .col3 .attr span + span {
  margin-left: 0.24rem;
}
.CartPage .item .col3 .num_count_box {
  position: absolute;
  right: 0.26rem;
  bottom: -0.24rem;
}
.CartPage .item .col3 .del {
  display: block;
  float: right;
  width: 1.78rem;
  height: 0.7rem;
  border: 1px solid #999;
  color: #999;
  font-size: 0.34rem;
  border-radius: 0.36rem;
  background: none;
}
.CartPage .item .col3 .limit {
  display: block;
  color: #fd1b3b;
  font-size: 0.26666667rem;
  line-height: 0.37333333rem;
  text-align: right;
  margin-right: 2.66666667rem;
}
.FreightMask {
  background: rgba(0, 0, 0, 0.3);
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: none;
}
.FreightMask .content {
  width: 100%;
  height: auto;
  position: absolute;
  bottom: 1.341rem;
  background: #fff;
  padding: 0.2rem 0.3rem 0.5rem;
}
.FreightMask .content .top {
  color: #333;
  overflow: hidden;
  line-height: 0.66rem;
}
.FreightMask .content .top .txtleft {
  float: left;
  font-size: 0.37rem;
}
.FreightMask .content .top .txtright {
  float: right;
  font-size: 0.32rem;
}
.FreightMask .content .top .txtright .number {
  color: red;
}
.FreightMask .content .lists {
  width: 100%;
  height: auto;
  margin-top: 0.15rem;
}
.FreightMask .content .lists .item {
  line-height: 0.75rem;
  font-size: 0.34rem;
  border-bottom: none;
}
.FreightMask .content .lists .item .txtleft {
  float: left;
}
.FreightMask .content .lists .item .txtleft .name {
  color: #999;
}
.FreightMask .content .lists .item .txtleft .num {
  color: red;
}
.FreightMask .content .lists .item .txtright {
  float: right;
  color: #4993fa;
}
.FreightMask .content .lists .item:after {
  border: none;
}
.CartPage .cartFooter {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1.33333333rem;
  background: #fff;
  padding-top: 0.24rem;
  padding-left: 0.26666667rem;
  box-shadow: 0 0 0.2rem rgba(0, 142, 255, 0.2);
  z-index: 1000;
}
.CartPage .cartFooter .fli_checkbox_blue {
  display: inline-block;
  vertical-align: middle;
  margin-right: 0.13333333rem;
}
.CartPage .cartFooter .text {
  display: inline-block;
  color: #a2a7bd;
  font-size: 0.37333333rem;
}
.CartPage .cartFooter .FreightBox span {
  font-size: 0.32rem;
  color: #1875f9;
}
.CartPage .cartFooter .FreightBox .img {
  display: inline-block;
  width: 0.32rem;
  height: 0.1867rem;
  margin-left: 0.2rem;
  vertical-align: middle;
  background: url(../img/angle-down-blue.png) center center / contain no-repeat;
}
@-webkit-keyframes change {
  0% {
    transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
  }
  50% {
    transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
    -o-transform: rotate(90deg);
  }
  100% {
    -ms-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -o-transform: rotate(180deg);
  }
}
@-webkit-keyframes changeback {
  0% {
    transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -o-transform: rotate(180deg);
  }
  50% {
    transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
    -o-transform: rotate(90deg);
  }
  100% {
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
  }
}
.CartPage .cartFooter .FreightBox .img.up {
  animation: change 0.3s linear;
  animation-fill-mode: forwards;
}
.CartPage .cartFooter .FreightBox .img.down {
  animation: changeback 0.3s linear;
  animation-fill-mode: forwards;
}
.CartPage .cartFooter .right {
  float: right;
}
.CartPage .cartFooter .right p {
  text-align: right;
  color: #a2a7bd;
  font-size: 0.32rem;
  padding-right: 3.14666667rem;
}
.CartPage .cartFooter .right p.total {
  line-height: 0.66666667rem;
  margin-top: -0.13333333rem;
}
.CartPage .cartFooter .right p.total span {
  color: #ff022f;
  font-size: 0.42666667rem;
}
.CartPage .cartFooter .right p.total .label {
  color: #333;
}
.CartPage .cartFooter .right .btn_submit {
  position: absolute;
  top: 0;
  right: 0;
  display: block;
  width: 2.88rem;
  height: 1.33333333rem;
  line-height: 1.33333333rem;
  background: #1875f9;
  /*background: -webkit-linear-gradient(left, #416efb, #519cff);*/
  /*!* Safari 5.1 - 6.0 *!*/
  /*background: -o-linear-gradient(left, #416efb, #519cff);*/
  /*!* Opera 11.1 - 12.0 *!*/
  /*background: -moz-linear-gradient(left, #416efb, #519cff);*/
  /*!* Firefox 3.6 - 15 *!*/
  /*background: linear-gradient(left, #416efb, #519cff);*/
  /* 标准的语法（必须放在最后） */
  color: #fff;
  font-size: 0.42666667rem;
  text-align: center;
}
.CartPage .cartFooter .right .btn_submit span {
  font-size: 0.32rem;
}
.CartPage .cartFooter .right .btn_del {
  display: inline-block;
  width: 2.13333333rem;
  height: 0.8rem;
  line-height: 0.8rem;
  border: none;
  background: #ff022f;
  color: #fff;
  font-size: 0.37333333rem;
  text-align: center;
  margin-right: 0.26666667rem;
  border-radius: 0.06666667rem;
}
.CartPage .cartFooter .right .btn_del.btn_theme {
  background: #416efb;
}
.layui-m-layerchild.cart_sale_pop {
  min-height: 50%;
  border: none;
  background: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  border-radius: 0;
}
.layui-m-layerchild.cart_sale_pop h3 {
  text-indent: 0.26666667rem;
  height: 1.06666667rem;
  line-height: 1.06666667rem;
  font-size: 0.37333333rem;
  color: #333;
  text-align: left;
  background: #f4f7fe;
  margin-top: 0;
  border-bottom: 1px solid #c9ccdd;
}
.layui-m-layerchild.cart_sale_pop .content {
  padding: 0.4rem;
  text-align: left;
}
.layui-m-layerchild.cart_sale_pop .line {
  line-height: 1rem;
  font-size: 0.37333333rem;
  color: #333;
}
.layui-m-layerchild.cart_sale_pop .line .fli_checkbox_blue {
  display: inline-block;
  vertical-align: middle;
  margin-right: 0.13333333rem;
}
.layui-m-layerchild.cart_sale_pop .line .fli_checkbox_blue * {
  box-sizing: border-box;
}
.myOrderPage .amount_section .FreightTxt {
  color: #4993fa;
}
.myOrderPage .amount_section .FreightTxt .img {
  display: inline-block;
  width: 0.32rem;
  height: 0.1867rem;
  margin-left: 0.01rem;
  vertical-align: middle;
  margin-top: -0.06rem;
  background: url(../img/angle-down-old.png) center center / contain no-repeat;
}
@-webkit-keyframes change {
  0% {
    transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
  }
  50% {
    transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
    -o-transform: rotate(90deg);
  }
  100% {
    -ms-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -o-transform: rotate(180deg);
  }
}
@-webkit-keyframes changeback {
  0% {
    transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -o-transform: rotate(180deg);
  }
  50% {
    transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
    -o-transform: rotate(90deg);
  }
  100% {
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
  }
}
.myOrderPage .amount_section .FreightTxt .img.up {
  animation: change 0.3s linear;
  animation-fill-mode: forwards;
}
.myOrderPage .amount_section .FreightTxt .img.down {
  animation: changeback 0.3s linear;
  animation-fill-mode: forwards;
}
.myOrderPage .chailvCB {
  background: #fff;
  margin: 0.16rem 0;
}
.myOrderPage .chailvCB header {
  line-height: 1.2rem;
  padding: 0 0.2667rem;
  font-size: 0.4rem;
  border-bottom: 1px solid #ddd;
  color: #333;
}
.myOrderPage .chailvCB .content {
  font-size: 0.32rem;
}
.myOrderPage .chailvCB .content .item {
  padding: 0.2667rem;
  border-bottom: 1px solid #ddd;
}
.myOrderPage .chailvCB .content .item label {
  color: #999;
  width: 1.8rem;
  float: left;
}
.myOrderPage .chailvCB .content .item .value {
  color: #333;
  margin-left: 1.6rem;
}
.myOrderPage .freightMask {
  display: none;
  overflow: hidden;
  background: #f5f5f5;
}
.myOrderPage .freightMask .Content {
  width: 100%;
  height: auto;
  padding: 0.2rem;
}
.myOrderPage .freightMask .Content .top {
  color: #333;
  overflow: hidden;
  line-height: 0.66rem;
}
.myOrderPage .freightMask .Content .top .txtleft {
  float: left;
  font-size: 0.32rem;
}
.myOrderPage .freightMask .Content .top .txtright {
  float: right;
  font-size: 0.32rem;
}
.myOrderPage .freightMask .Content .top .txtright .number {
  color: red;
}
.myOrderPage .freightMask .Content .lists {
  width: 100%;
  height: auto;
  margin-top: 0.15rem;
  overflow: hidden;
}
.myOrderPage .freightMask .Content .lists .item {
  line-height: 0.75rem;
  font-size: 0.32rem;
  border-bottom: none;
}
.myOrderPage .freightMask .Content .lists .item .txtleft {
  float: left;
}
.myOrderPage .freightMask .Content .lists .item .txtleft .name {
  color: #999;
}
.myOrderPage .freightMask .Content .lists .item .txtleft .num {
  color: red;
}
.myOrderPage .freightMask .Content .lists .item .txtright {
  float: right;
  color: #4993fa;
}
.myOrderPage .freightMask .Content .lists .item:after {
  border: none;
}
.myOrderPage {
  padding-bottom: 0.4rem;
}
.myOrderPage.simple ul.order_list_section {
  margin-top: -0.44rem;
}
.myOrderPage.simple ul.order_list_section:only-child {
  margin-top: 0;
}
.myOrderPage.simple ul.order_list_section li .img {
  width: 2rem;
  height: 2rem;
  border: 1px solid #e5e5e5;
}
.myOrderPage.simple ul.order_list_section li .img img {
  height: 2rem;
}
.myOrderPage.simple ul.order_list_section li .info {
  margin-left: 2.2rem;
}
.myOrderPage .form_input_item {
  padding: 0.133333rem 0.32rem;
}
.myOrderPage .form_input_item label {
  display: block;
  width: 2.533333rem;
  float: left;
  color: #333;
  line-height: 1.2rem;
}
.myOrderPage .form_input_item .inputs {
  position: relative;
  margin-left: 2.533333rem;
}
#myOrderPage.pay-info-page{
  padding-bottom: 0.4rem;
}
#myOrderPage.pay-info-page .exchange-notice{
  background: #fff;
  padding: 0.4rem;
  font-size: 0.34rem;
  line-height: 0.52rem;
  margin-top: -0.28rem;
}
.myOrderPage .form_input_item .inputs .input_text {
  display: block;
  width: 100%;
  height: 1.2rem;
  line-height: 0.6rem;
  padding: 0.3rem 0;
  font-size: 0.373333rem;
  color: #333;
  background: #fff;
}
.myOrderPage .form_input_item .inputs .input_text.modify-placeholder::placeholder{
  font-size: 0.32rem;
}
.myOrderPage .form_input_item .inputs .input_text[disabled="disabled"] {
  background: #fff;
}
.myOrderPage .link_btn_section {
  padding: 0.2rem 0.4rem;
  text-align: right;
  margin-top: 0.4rem;
}
.myOrderPage .link_btn_section a {
  display: inline-block;
  vertical-align: middle;
  padding: 0.133333rem 0.4rem;
  background: #4993fa;
  border: none;
  border-radius: 0.066666rem;
  color: #fff;
  font-size: 0.32rem;
  text-align: center;
}
.myOrderPage > section {
  position: relative;
  background: #fff;
  margin-top: 0.12rem;
  color: #444;
}
.myOrderPage > section:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.myOrderPage > section.ticket_exchage_section {
  padding: 0.1333rem 0 0.2667rem;
  margin-top: 0.3733rem;
}
.myOrderPage > section.ticket_exchage_section .header {
  border-top: 1px solid #e5e5e5;
  text-align: center;
  margin: 0.5333rem 0.7467rem -0.32rem;
}
.myOrderPage > section.ticket_exchage_section .header span {
  display: inline-block;
  vertical-align: middle;
  padding: 0.2667rem;
  background: #fff;
  margin-top: -0.6rem;
  font-size: 0.3467rem;
  color: #999;
}
.myOrderPage > section.card_info header .tips {
  color: #999;
  font-size: 0.32rem;
  margin-left: 0.1333rem;
}
.myOrderPage > section.card_info .list {
  padding: 0  0.38rem 0.24rem;
}
.myOrderPage > section.card_info .list .item {
  border: 1px solid #ddd;
}
.myOrderPage > section.card_info .list .item + .item {
  margin-top: 0.16rem;
}
.myOrderPage > section.card_info .list .item .title {
  height: 1.2rem;
  line-height: 1.2rem;
  font-size: 0.4rem;
  font-weight: 800;
  padding: 0 0.32rem;
  /*border-bottom: 1px solid #ddd;*/
  color: #369ff2;
}
.myOrderPage > section.card_info .list .item .title .arrow {
  position: absolute;
  margin-top: 0.36rem;
  margin-right: 0.2rem;
  float: right;
  z-index: 99;
  display: inline-block;
  height: 0.32rem;
  width: 0.32rem;
  border-width: 0.04rem 0.04rem 0 0;
  border-color: #369ff2;
  border-style: solid;
  -webkit-transform: matrix(-0.71, 0.71, -0.71, -0.71, 0, 0);
  transform: matrix(-0.71, 0.71, -0.71, -0.71, 0, 0);
  position: relative;
  top: -0.04rem;
}
.myOrderPage > section.card_info .list .item .title.active .arrow {
  margin-top: 0.5rem;
  -webkit-transform: matrix(0.71, -0.71, 0.71, 0.71, 0, 0);
  transform: matrix(0.71, -0.71, 0.71, 0.71, 0, 0);
}
.myOrderPage > section.card_info .list .item .table_section {
  padding:0 0.2667rem 0.2667rem;
}
.myOrderPage > section.card_info .list .item .table_section table {
  width: 100%;
  border: 1px solid #ddd;
  border-collapse: collapse;
}
.myOrderPage > section.card_info .list .item .table_section table tr td {
  border: 1px solid #ddd;
  font-size: 0.32rem;
  padding: 0.1333rem;
}
.myOrderPage > section.card_info .list .item .table_section table tr td:first-child {
  width: 30%;
  background: #f9f9f9;
}
.myOrderPage > section.card_info .list .item .table_section table tr td span {
  display: inline-block;
  margin-right: 0.32rem;
}
.myOrderPage > section.card_info .list .item .table_section table tr td .text {
  color: #369ff2;
}
.myOrderPage > section.card_info .list .item .table_section table tr td .text.disabled {
  color: #999;
}
.myOrderPage  .exchange-certificate-box {
  padding: 0.12rem 0.38rem;
  line-height: 0.64rem;
  font-size: 0.34rem;
}
.myOrderPage .exchange-certificate-box .label{

  color: #444;
}
.myOrderPage .exchange-certificate-box .couponNum{
  color: #ff3900;
}
.myOrderPage > section > header {
  background: #f9f9f9;
  padding: 0.18rem 0.38rem;
}
.myOrderPage > section > header span:first-child {
  border-left-width: 0.1rem;
  border-left-style: solid;
  border-left-color: #4993fa;
  padding-left: 0.35rem;
  height: 0.64rem;
  line-height: 0.64rem;
  font-size: 0.42rem;
}
.myOrderPage .orderCreate {
  margin-top: 0;
  background: #fff;
}
.myOrderPage .orderCreate p,
.myOrderPage .orderCreate a {
  position: relative;
  height: 1.18rem;
  line-height: 1.15rem;
  font-size: 0.42rem;
  border-bottom: 1px solid #e5e5e5;
  padding: 0 0.4rem;
}
[data-dpr="1"] .myOrderPage .orderCreate p,
[data-dpr="1"] .myOrderPage .orderCreate a {
  border-bottom: none;
}
[data-dpr="1"] .myOrderPage .orderCreate p:before,
[data-dpr="1"] .myOrderPage .orderCreate a:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.myOrderPage .orderCreate .no_order span {
  color: #fd1b3b;
  float: right;
}
.myOrderPage .orderCreate .no_trans {
  display: block;
  color: #333;
  font-size: 0.38rem;
  background: url(../img/icon_trans.png) left 48% no-repeat;
  background-size: 0.5rem 0.36rem;
  background-origin: content-box;
}
.myOrderPage .orderCreate .no_trans span {
  margin-left: 0.66rem;
}
.myOrderPage .orderCreate_theme {
  margin-top: 0;
  background: #fff;
}
.myOrderPage .orderCreate_theme p,
.myOrderPage .orderCreate_theme a {
  position: relative;
  height: 1.18rem;
  line-height: 1.15rem;
  font-size: 0.42rem;
  border-bottom: 1px solid #e5e5e5;
  padding: 0 0.4rem;
}
[data-dpr="1"] .orderCreate_theme .orderCreate p,
[data-dpr="1"] .orderCreate_theme .orderCreate a {
  border-bottom: none;
}
[data-dpr="1"] .orderCreate_theme .orderCreate p:before,
[data-dpr="1"] .orderCreate_theme .orderCreate a:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.myOrderPage .orderCreate_theme .no_order span {
  color: #fd1b3b;
  float: right;
}
.myOrderPage .orderCreate_theme .no_trans {
  display: block;
  color: #333;
  font-size: 0.38rem;
  background: url(../img/icon_trans.png) left 48% no-repeat;
  background-size: 0.5rem 0.36rem;
  background-origin: content-box;
}
.myOrderPage .orderCreate_theme .no_trans span {
  margin-left: 0.66rem;
}
.cakeMyOrderPage .myOrderPage .base_info a{
  padding-bottom: 0.2267rem;
}
.cakeMyOrderPage.modify .myOrderPage .base_info a{
  padding-bottom:0.35rem;
  padding-left: 0;
  padding-right: 0;
}
.myOrderPage .base_info a {
  padding: 0.6rem 0.35rem ;
  font-size: 0.42rem;
  color: #444;
  display: block;

}
.mianMyOrderPage .myOrderPage .base_info a{
  padding: 0.6rem 0.35rem;
}
.myOrderPage .base_info a.fli_link_line:after {
  top: 45%;
  right: 0;
}
.modifyMyOrderPage.myOrderPage .base_info a.fli_link_line:after{
  right: 0.4rem;
}
.modifyMyOrderPage.myOrderPage .coupon{
  display: inline-block;
  border: 1px solid #fdeded;
  background: #fdeded;
  height: 0.48rem;
  line-height: 0.48rem;
  padding: 0 0.16rem;
  color: #da7c8c!important;
  font-size: 0.2667rem!important;
  margin-left: 0.12rem;
  border-radius: 0.12rem;
  font-weight: normal;
}
.mianMyOrderPage .myOrderPage .base_info a.fli_link_line:after{
  right: 0.4rem;
}
.myOrderPage .base_info a.location {
  background: url(../img/icon_location.png) left top no-repeat;
  background-origin: content-box;
  background-size: 0.32rem 0.43rem;
}
.myOrderPage .base_info a.location p {
  margin-left: 0.66rem;
}
.myOrderPage .base_info a p span {
  margin-left: 0.37rem;
}
.myOrderPage .base_info a p.addr {
  font-size: 0.32rem;
  color: rgb(102,102,102);
  margin-top: 0.42rem;
  padding-right: 0.34rem;

}
.mianMyOrderPage .myOrderPage .base_info a p.addr{
  margin-top: 0.14rem;
}
.myOrderPage .base_info a p.addr img{
  width: 0.26667rem;
}
.cakeMyOrderPage .myOrderPage .message_section{
  display: none;
}
.cakeMyOrderPage .myOrderPage > section > header span:first-child{
  border: none;
  color: rgb(51,51,51);
  font-weight: bold;
  font-size: 0.37333rem;
}
.cakeMyOrderPage .contentBox .order_list_section{
  padding: 0 0.38rem;
}
.cakeMyOrderPage .contentBox .order_list_section header{
  color: rgb(51,51,51);
  font-size: 0.3733rem;
  height: 0.98rem;
  line-height: 0.98rem;
  border-bottom: 0.026rem solid rgb(245,245,245);
  padding: 0;
}
.cakeMyOrderPage .contentBox .order_list_section .distribution li{
  padding: 0;
  /*padding-bottom: 0.426667rem;*/
  font-size: 0.3733rem;
  min-height: 1rem;
  height: auto;
}
.cakeMyOrderPage .contentBox .order_list_section .distribution li.topMiddle{
  padding-top: 0.32rem;

}
.cakeMyOrderPage .contentBox .order_list_section .distribution li.topMiddle span{
  word-break:break-all
}
.cakeMyOrderPage .contentBox .order_list_section .distribution li span{
  color: rgb(51,51,51);
  font-size: 0.32rem;
  text-align: right;
}
.cakeMyOrderPage .contentBox .order_list_section .distribution li.topMiddle{
  position: relative;
}
.cakeMyOrderPage .contentBox .order_list_section .distribution li span.numBox{
  position: absolute;
  right: 0;
  bottom: 0;
  font-size: 0.2667rem;
  padding-bottom: 0.09rem;
}
.cakeMyOrderPage  ul.order_list_section li .img{
  width: 2.24rem;
  height: 2.24rem;
}
.cakeMyOrderPage  ul.order_list_section li .img img{
  width: 100%;
  height: 100%;
}
.cakeMyOrderPage ul.order_list_section li .info{
  margin-left: 0;
}
.cakeMyOrderPage ul.order_list_section li .productInfo{
  width: 100%;
  position: relative;
}
.cakeMyOrderPage.modify ul.order_list_section li .productInfo{
  /*height: 2.533rem;*/
}
.cakeMyOrderPage.modify ul.order_list_section li .productInfo .info{
  float: left;
  width: calc(100% - 2.24rem - 0.2667rem - 0.2667rem - 1.24rem);

}
.cakeMyOrderPage.modify ul.order_list_section li .productInfo .infoRight{
  width: 2rem;
  float: left;
  text-align: right;
}
.cakeMyOrderPage .infoRight{
  text-align: right;
}
.cakeMyOrderPage .infoRight .salePrice{
  color: rgb(255,87,38);
  font-size: 0.4267rem;
  margin-bottom: 0.1rem;
  text-align: right;
}
.cakeMyOrderPage .infoRight .originalPrice{
  color: rgb(153,153,153);
  font-size: 0.3733rem;
  text-decoration:line-through;
}
.cakeMyOrderPage .infoRight .quantity{
  color: rgb(153,153,153);
  font-size: 0.3733rem;
  position: absolute;
  bottom: 0.21333rem;
  right: 0;
}
.cakeMyOrderPage ul.order_list_section li .info{
  margin: 0 0.2667rem;
}
.cakeMyOrderPage ul.order_list_section li .info p.title{
  color: rgb(51,51,51);
  font-weight: bold;
  font-size: 0.3733rem;
  text-overflow: ellipsis;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  max-height: 1.09333333rem;
  height: auto;
  word-break: break-all;
}
.cakeMyOrderPage ul.order_list_section li .distribution{
  font-size: 0.293rem;
  color: rgb(51,51,51);
  margin-top: 0.50667rem;
}
.cakeMyOrderPage ul.order_list_section li .distribution li.noshow{
  display: none;
}
.cakeMyOrderPage ul.order_list_section li .settlementInfo{
  width: 100%;
  text-align: right;
  color: rgb(51,51,51);
  font-size: 0.3733rem;
  font-weight: bold;
}
.cakeMyOrderPage ul.order_list_section li .settlementInfo span{
  color: rgb(255,83,31);
}
.cakeMyOrderPage ul.order_list_section .distribution li label{
  font-weight: bold;
  min-width: 1.56rem;
}

.cakeMyOrderPage ul.order_list_section li{
  padding-top: 0.1067rem;
  padding-left: 0;
  padding-right: 0;
}
.cakeMyOrderPage ul.order_list_section li .productInfo{
  padding: 0.21333rem 0;
}
.cakeSubInfoPage ul.order_list_section  .distribution li label{
  min-width: 1.56rem;

}
.cakeSubInfoPage .layer_content_box{
  max-height: 4rem;
  overflow: auto;
}

.cakeSubInfoPage .layer_content_box. li.fli_checkbox_blue{
  text-align: left;
  padding-left: 0.9rem;
  width: calc(100% - 0.9rem);
}
.cakeSubInfoPage .layer_content_box.pickUpStore li.fli_checkbox_blue{
  height: auto;
  text-align: left;
  padding-left: 0.9rem;
  width: calc(100% - 0.9rem);
}
.cakeSubInfoPage .layer_content_box .fli_checkbox_blue label{
  top: 0.1rem;
}
.cakeSubInfoPage .layer_content_box.pickUpStore li.fli_checkbox_blue p{
  font-size: 0.2677rem;
  color: #999;
}
.cakeSubInfoPage{

  padding-bottom: 1.36rem;
}
.cakeSubInfoPage .bottom{
  box-shadow: 0 0 0 0.12rem #f2f2f2;
  height: 1.3333rem;
  background: #fff;
  position: fixed;
  bottom: 0;
  width: 100%;
  left:0;

}
.cakeSubInfoPage .bottom .leftContent{
  color: rgb(51,51,51);
  font-size: 0.32rem;
  margin-left: 1.146667rem;
}
.cakeSubInfoPage .bottom .leftContent span{
  color: rgb(255,87,38);
}
.cakeSubInfoPage .btn_submit_long{
  width: 2.88rem;
  height: 0.88rem;
  line-height: 0.88rem;
  border-radius: 0.16rem;
  margin: 0;
  color: rgb(255,255,255);
  font-size: 0.3733rem;
  margin-right: 0.32rem;
}
.cakeSubInfoPage ul.order_list_section li span{

  color: rgb(153,153,153);

  font-size: 0.32rem;
}
.cakeSubInfoPage .contentBox ul.order_list_section   li .yunfei{
  color: rgb(255,87,38);

  font-size: 0.2667rem;
}
.cakeSubInfoPage .contentBox ul.order_list_section .distribution li .yunfei span{
  font-size:0.42667rem;

  color: rgb(255,87,38);
}
.cakeMyOrderPage ul.order_list_section li .save{
  display: none;
}
.myOrderPage .base_info .identify {
  position: relative;
  padding: 0.18rem 0.22rem;
  border-top: 1px solid #e5e5e5;
}
.myOrderPage .base_info .identify .input {
  position: relative;
  display: block;
  width: 78%;
  height: 0.8rem;
  line-height: 0.8rem;
  font-size: 0.32rem;
  background: #f9f9f9;
  border: 1px solid #e5e5e5;
  padding: 0 0.1rem;
  color: #4993fa;
}
.myOrderPage .base_info .identify .input[disabled="disabled"] {
  opacity: 0.5;
}
.myOrderPage .base_info .identify .btn_pill.btn_pill_theme {
  position: absolute;
  right: 0.43rem;
  bottom: 0.18rem;
}
.myOrderPage .base_info .receivingInformation{
  color: rgb(51,51,51);
  font-weight: bold;
  font-size: 0.3733rem;
}
.myOrderPage .message_section {
  position: relative;
  padding-bottom: 0.38rem;
}
.myOrderPage .message_section div.textarea {
  display: block;
  border: 1px solid #e5e5e5;
  background: #fff;
  border-radius: 0.03rem;
  color: #444444;
  font-size: 0.38rem;
  line-height: 0.6rem;
  padding: 0.26666667rem;
  margin: 0.16rem 0;
}
.myOrderPage .message_section header {
  background: none;
}
.myOrderPage .message_section form {
  padding: 0 0.38rem;
}
.myOrderPage .message_section textarea {
  display: block;
  margin: 0 auto;
  width: 100%;
  height: 2.45rem;
  border-width: 0.03rem;
  border-style: solid;
  border-color: #e5e5e5;
  background: #fff;
  border-radius: 0.03rem;
  color: #444444;
  font-size: 0.38rem;
  line-height: 0.45333333rem;
  padding: 0.26666667rem;
  resize: none;
}
.myOrderPage ::-webkit-input-placeholder {
  color: #999;
  font-size: 0.38rem;
}
.myOrderPage :-moz-placeholder {
  color: #999;
  font-size: 0.38rem;
}
.myOrderPage ::-moz-placeholder {
  color: #999;
  font-size: 0.38rem;
}
.myOrderPage :-ms-input-placeholder {
  color: #999;
  font-size: 0.38rem;
}
.myOrderPage .message_section > span {
  position: absolute;
  right: 0.48rem;
  bottom: 0.44rem;
  font-size: 0.34rem;
  color: #999;
}
.myOrderPage .message_section > span span {
  color: #4993fa;
}
.myOrderPage .message_section .message {
  position: relative;
  border-top: 1px solid #e5e5e5;
  padding: 0.36rem 0.4rem;
  margin-bottom: -0.38rem;
  overflow: hidden;
}
.myOrderPage .message_section .message .label {
  float: left;
}
.myOrderPage .message_section .message .value {
  display: block;
  margin-left: 2.12rem;
  color: #999;
  max-height: 10rem;
}
.myOrderPage .favourable_section .header {
  font-size: 0.4rem;
  padding: 0.18rem 0.38rem;
  line-height: 0.8rem;
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
}
.myOrderPage .favourable_section .header .num_span {
  color: #fd1b3b;
  font-size: 0.32rem;
}
.myOrderPage .favourable_section .header .num_span span {
  font-size: 0.4rem;
}
.myOrderPage .favourable_section .header .right_text_gray {
  float: right;
  color: #666;
  font-size: 0.32rem;
}
.myOrderPage .favourable_section .header p {
  display: none;
  line-height: 0.46rem;
  color: #666;
  font-size: 0.32rem;
  padding: 0.2667rem 0.1333rem 0;
}
.myOrderPage .favourable_section .lines_Section_all {
  position: relative;
  padding: 0.06rem 0.38rem;
}
.cakeMyOrderPage .myOrderPage .favourable_section .lines_Section_all{
  padding-left: 0.58667rem;
  padding-right: 0.4533rem;
}
.cakeMyOrderPage.modify .myOrderPage .amount_section{
  padding-left: 0.58667rem;
  padding-right: 0.4533rem;
}
.myOrderPage .favourable_section .lines_Section_all .text_gray {
  font-size: 0.32rem;
  color: #999;
  margin-left: 0.16rem;
}
.myOrderPage .favourable_section .lines_Section_all.border {
  position: relative;
  border-top: 1px solid #e5e5e5;
}
.myOrderPage .favourable_section .lines_Section_all.border .text_line {
  border-top: none;
  max-height: 10rem;
}
.myOrderPage .favourable_section .lines_Section_all .fli_link_line,
.myOrderPage .favourable_section .lines_Section_all a {
  display: block;
  font-size: 0.4rem;
  line-height: 1.1rem;
  color: #444;
}
.myOrderPage .favourable_section .lines_Section_all.voucher-nopadding{
  padding: 0.133333rem 0.32rem;
}
.myOrderPage .favourable_section .lines_Section_all.voucher-nopadding.couponModeLines {
  padding: 0.133333rem 0.72rem;
}
.myOrderPage .favourable_section .lines_Section_all.voucher-nopadding.couponModeLines #couponNum{
  font-size: 0.42rem;
}
.myOrderPage .favourable_section .lines_Section_all.voucher-nopadding
.btn_submit_long.coupon-submit-long-fixed{
  position: fixed;
  bottom: 1.2rem;
  left: 50%;
  transform: translateX(-50%);
}
.myOrderPage .favourable_section .lines_Section_all.voucher-nopadding a{
  font-size: 0.42rem;
  line-height: 1.2rem;
}
.myOrderPage .favourable_section .lines_Section_all.voucher-nopadding a .right_text{
  padding-right: 0;
}
.myOrderPage .favourable_section .lines_Section_all .fli_link_line.fli_link_line:after,
.myOrderPage .favourable_section .lines_Section_all a.fli_link_line:after {
  right: 0;
  top: 0.4rem;
}
.myOrderPage .favourable_section .lines_Section_all .fli_link_line .fli_checkbox_blue,
.myOrderPage .favourable_section .lines_Section_all a .fli_checkbox_blue {
  float: right;
  margin-left: -0.2rem;
  margin-top: 0.32rem;
}
.myOrderPage .favourable_section .lines_Section_all .fli_link_line span.like_btn,
.myOrderPage .favourable_section .lines_Section_all a span.like_btn {
  display: inline-block;
  vertical-align: middle;
  height: 0.58666667rem;
  line-height: 0.58666667rem;
  color: #fff;
  background: #fd1b3b;
  padding: 0rem 0.21333333rem;
  border-radius: 0.08rem;
  font-size: 0.32rem;
  margin-left: 0.26666667rem;
  margin-top: -0.05333333rem;
}
.myOrderPage .favourable_section.new-favourable-section .lines_Section_all .fli_link_line span.like_btn,
.myOrderPage .favourable_section.new-favourable-section .lines_Section_all a span.like_btn{
  background: #ffffff;
  color: #fd1b3b;
  border: 1px solid #fd1b3b;
}
.myOrderPage .favourable_section .lines_Section_all .fli_link_line .like_btn_box,
.myOrderPage .favourable_section .lines_Section_all a .like_btn_box {
  display: inline-block;
  vertical-align: middle;
  width: 50%;
  height: 0.6rem;
  overflow: hidden;
}
.myOrderPage .favourable_section .lines_Section_all .fli_link_line .like_btn_box span.like_btn,
.myOrderPage .favourable_section .lines_Section_all a .like_btn_box span.like_btn {
   vertical-align: top;
   margin-top: 0.01rem;
   margin-right: -0.1rem;
 }
.confirm-order-page .myOrderPage .favourable_section .lines_Section_all .fli_link_line .like_btn_box span.like_btn,
.confirm-order-page .myOrderPage .favourable_section .lines_Section_all a .like_btn_box span.like_btn {
  vertical-align: top;
  margin-top: 0.01rem;
  margin-right: 0;
  margin-left: 0.12rem;
}
.myOrderPage .favourable_section .lines_Section_all .fli_link_line .right_text,
.myOrderPage .favourable_section .lines_Section_all a .right_text {
  float: right;
  padding-right: 0.32rem;
 font-size: 0.32rem;
}
.myOrderPage .favourable_section .lines_Section_all .fli_link_line .right_text span,
.myOrderPage .favourable_section .lines_Section_all a .right_text span {
  font-size: 0.32rem;
}
.myOrderPage .favourable_section .lines_Section_all .fli_link_line .right_text.red,
.myOrderPage .favourable_section .lines_Section_all a .right_text.red {
  color: #fd1b3b;
}
.myOrderPage .favourable_section .lines_Section_all .fli_link_line .right_text.gray,
.myOrderPage .favourable_section .lines_Section_all a .right_text.gray {
  color: #999;
}
.myOrderPage .favourable_section .lines_Section_all .text_line {
  position: relative;
  display: block;
  font-size: 0.4rem;
  line-height: 1.1rem;
  color: #444;
  border-top: 1px solid #e5e5e5;
}
.myOrderPage .favourable_section .lines_Section_all .text_line span {
  color: #ee0a3b;
  margin-left: 0.26666667rem;
  margin-right: 0.8rem;
}
.myOrderPage .favourable_section .lines_Section_all .text_line .radio_section_theme {
  float: right;
  margin-top: 0.26666667rem;
}
.myOrderPage .favourable_section .lines_Section_all .text_line .switch {
  float: right;
  margin-top: 0.29333333rem;
}
.myOrderPage {
  margin-top: 0.16rem;
}
.myOrderPage .order_detail_info .content {
  padding: 0.2667rem 0.4rem;
  font-size: 0.34rem;
}
.myOrderPage .order_detail_info .content p {
  line-height: 0.5333rem;
  margin-bottom: 0.2667rem;
  color: #999;
}
.myOrderPage .order_detail_info .content p span {
  margin-left: 0.2667rem;
  color: #666;
}
.myOrderPage .amount_section {
  padding: 0.4rem;
}
.myOrderPage .amount_section .detail {
  display: none;
  background: #f5f5f5;
  margin-left: -0.4rem;
  margin-right: -0.4rem;
  padding: 0.2rem 0.4rem;
}
.myOrderPage .amount_section .paytips {
  color: #ff3900;
  text-align: right;
  font-size: 0.34rem;
  line-height: 0.5rem;
  margin-top: 0.266666rem;
}
.myOrderPage .amount_section .icon-ali-jifen {
  vertical-align: baseline;
}
.myOrderPage .amount_section.has_header {
  padding: 0;
}
.myOrderPage .amount_section .content {
  padding: 0.4rem;
}
.myOrderPage .amount_section p {
  display: block;
  line-height: 0.64rem;
  font-size: 0.34rem;
  color: #444;
}
.myOrderPage .amount_section p > span {
  float: right;
  color: #ff3900;
}
.mianMyOrderPage.confirm-order-page .myOrderPage .amount_section p > span{
    color: #fd1b3b;
}
.myOrderPage .amount_section p > span .show_detail {
  color: #666;
  background: url(../img/icon_arrow_down.png) right center no-repeat;
  background-size: 0.266666rem auto;
  padding-right: 0.3rem;
  margin-right: 0.133333rem;
}
.myOrderPage .amount_section p > span .show_detail.active {
  background-image: url(../img/icon_arrow_up.png);
}
.myOrderPage .amount_section p.total {
  font-size: 0.42rem;
}
.myOrderPage .amount_section footer {
  position: relative;
  color: #999;
  text-align: right;
  line-height: 1.14666667rem;
  margin-top: -0.13333333rem;
  padding: 0 0.4rem;
  font-size: 0.34rem;
}
.myOrderPage .amount_section footer:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.myOrderPage .amount_section footer:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.multiCommentPage {
  position: relative;
  border-top: 1px solid #f2f2f2;
}
.multiCommentPage .order_items_section ul li .item .title {
  font-size: 0.36rem;
}
.multiCommentPage .btn_pill {
  padding: 0.12rem 0.32rem;
}
.multiCommentPage .btn {
  text-align: center;
  width: 2rem;
}
ul.order_list_section li {
  position: relative;
  background: #ffffff;
  padding-top: 0.32rem;
  padding-left: 0.48rem;
  padding-right: 0.26666667rem;
  padding-bottom: 0.53333333rem;
  overflow: hidden;
}
ul.order_list_section.modify-box li .info{
  height: 2rem
}
ul.order_list_section.modify-box li .info p.title{
  height: auto;
}
ul.order_list_section li:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
ul.order_list_section li .img {
  width: 3.01333333rem;
  height: 3.01333333rem;
  overflow: hidden;
  float: left;
}
ul.order_list_section li .img img {
  height: 3.01333333rem;
}
ul.order_list_section li .info {
  margin-left: 3.09333333rem;
}
ul.order_list_section li .info p {
  color: #444444;
  line-height: 0.56rem;
}
ul.order_list_section li .info p.title {
  font-size: 0.4rem;
  height: 1.09333333rem;
  overflow: hidden;
  margin-bottom: 0.06666667rem;
  margin-top: -0.06666667rem;
}
ul.order_list_section li .info p.attr {
  height: 0.56rem;
  font-size: 0.32rem;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.cakeMyOrderPage.modify ul.order_list_section li .info p.attr{
  overflow: initial;
  white-space: initial;
}
ul.order_list_section li .info p.attr span {
  margin-right: 0.21333333rem;
}
ul.order_list_section li .info p.price {
  height: 0.56rem;
  font-size: 0.34rem;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #ee0a3b;
}
ul.order_list_section.modify-box li .info p.price{
  margin-top: 0.32rem;
}
ul.order_list_section li .info p.price span {
  font-size: 0.32rem;
  color: #999;
}
ul.order_list_section li .info p.outofservice {
  color: #f00;
  height: 0.8rem;
  line-height: 0.8rem;
  font-size: 0.34rem;
}
ul.order_list_section li .save {
  position: absolute;
  right: 0.42666667rem;
  bottom: 0.53333333rem;
}
ul.order_list_section li .save span {
  display: inline-block;
  vertical-align: middle;
  color: #1875f9;
  font-size: 0.32rem;
}
ul.order_list_section li .save .icon_save {
  width: 0.74666667rem;
  height: 0.74666667rem;
  background: url(../img/pic_save.png) center center / contain no-repeat;
}
ul.order_list_section li .btn_pill.btn_pill_theme {
  position: absolute;
  right: 0.42666667rem;
  bottom: 0.4rem;
  width: 2.13333333rem;
}
.cakeMyOrderPage ul.order_list_section li .btn_pill.btn_pill_theme{
  right: 0;
  width: 1.13333333rem;
  padding: 0;
}
ul.order_list_section.small li {
  padding: 0.4rem;
}
ul.order_list_section.small li .img {
  width: 2.13333333rem;
  height: 2.13333333rem;
}
ul.order_list_section.small li .img img {
  height: 2.13333333rem;
}
ul.order_list_section.small li .info {
  margin-left: 2.29333333rem;
}
ul.order_list_section.small li .info p.title {
  height: 1.06666667rem;
  overflow: hidden;
  margin-bottom: 0.13333333rem;
}
ul.order_list_section.small li .info p.attr {
  max-width: 70%;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.shop_nav_section {
  width: 100%;
  background: #ffffff;
}
.shop_nav_section ul {
  padding: 0.2rem;
  overflow: hidden;
}
.shop_nav_section ul li {
  position: relative;
  display: block;
  width: 20%;
  float: left;
  padding-bottom: 0.2rem;
}
.shop_nav_section ul li a {
  display: block;
}
.shop_nav_section ul li a .img {
  width: 1.17333333rem;
  height: 1.17333333rem;
  margin: 0 auto;
  border-radius: 100%;
  overflow: hidden;
}
.shop_nav_section ul li a .img img {
  display: block;
  width: 100%;
  height: auto;
}
.shop_nav_section ul li a .text {
  height: 0.8rem;
  line-height: 0.8rem;
  text-align: center;
  color: #444444;
  font-size: 0.32rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.classify_goods_section {
  margin-top: 0.26666667rem;
  background: #fff;
}
.classify_goods_section header {
  padding: 0.26666667rem 0.4rem;
  border-bottom: 1px solid #ddd;
  overflow: hidden;
}
.classify_goods_section header span {
  display: block;
  float: left;
  height: 0.61333333rem;
  line-height: 0.61333333rem;
  border-left-width: 0.05333333rem;
  border-left-style: solid;
  padding-left: 0.34666667rem;
}
.classify_goods_section header a {
  font-size: 0.34rem;
  padding-right: 0.4rem;
  color: #999;
  float: right;
}
.classify_goods_section header.cart_1 span {
  color: #56d629;
  border-left-color: #56d629;
}
.classify_goods_section header.cart_2 span {
  color: #f95198;
  border-left-color: #f95198;
}
.classify_goods_section header.cart_3 span {
  color: #88400b;
  border-left-color: #88400b;
}
.classify_goods_section header.cart_4 span {
  color: #b257f8;
  border-left-color: #b257f8;
}
.classify_goods_section header.cart_5 span {
  color: #29a3d6;
  border-left-color: #29a3d6;
}
.classify_goods_section header.cart_6 span {
  color: #fc2d12;
  border-left-color: #fc2d12;
}
.classify-list-section {
  position: fixed;
  left: 0;
  top: 0;
  width: 2.4rem;
  height: 100%;
  padding: 1.18rem 0 1.32rem;
  background: url(../img/pic_cat_bg.png) right 1.18rem no-repeat #fff;
  background-size: 0.32rem 0.32rem;
  overflow: scroll;
  -webkit-overflow-scrolling: touch;
  z-index: 88;
}
.classify-list-section.cate-list-section-left{
  background: #ffffff;
  width: 2.8rem;
}
.classify-list-section.cate-list-section-left  li .text.sideMenu{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
}
.cate-list-page-new .displayBox{
  margin-left: 2.8rem;
}
.classify-list-section.cate-list-section-left li > a{
  color: rgb(102,102,102);
  font-size: 0.4rem;
}
.classify-list-section.cate-list-section-left li.active{
  position: relative;
}
.classify-list-section.cate-list-section-left li.active > a{
  color: rgb(51,51,51);
  font-weight: 600;
  background: #ffffff;
  border: none;
}
.classify-list-section.cate-list-section-left li.active > a:before{
  content: "";
  background: url("./../img/icons/cate-list-bg.png") no-repeat center center;
  background-size: contain;
  position: absolute;
  bottom: -0.02rem;
  left: 50%;
  transform: translateX(-50%);
  width: 50%;
  height: 0.3133rem;
}

.classify-list-section::-webkit-scrollbar {
  width: 0;
  height: 0;
}
.classify-list-section li {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  z-index: 99999;
}
.classify-list-section li a {
  position: relative;
  display: block;
  height: 1.146666rem;
  line-height: 1.146666rem;
  text-align: center;
  font-size: 0.373333rem;
  color: #333;
  cursor: pointer;
  border-left-width: 0.04rem;
  border-left-style: solid;
  border-left-color: transparent;
}
.classify-list-section li.active > a {
  position: relative;
  border-left-color: #fd1b3b;
  color: #fff;
  background: #416efb;
}
.displayBox {
  margin-left: 2.4rem;
}
.displayBox .cateList {
  padding: 0.2rem;
}
.displayBox .cateList .cateItem {
  margin-bottom: 0.1rem;
}
.displayBox .cateList .cateItem .title {
  color: #333;
  font-size: 0.373333rem;
  line-height: 0.8rem;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.displayBox .cateList.cateItemList .cateItem{
  background: #ffffff;
  border-radius: 0.16rem;
  margin-bottom: 0.2rem;
}
.displayBox .cateList.cateItemList .cateItem .list{
  border-radius:0 0 0.16rem 0.16rem;
}
.displayBox .cateList.cateItemList .cateItem .title {
  padding: 0.26rem 0.26rem 0;
  white-space: nowrap;
  display:-webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  overflow: hidden;
  text-overflow: ellipsis;
}
.displayBox .cateList.cateItemList .cateItem .title .category-name{
  color: rgb(51,51,51);
  font-weight: 600;
  font-size: 0.4rem;
}
.displayBox .cateList.cateItemList .cateItem .title .see-more{
  background: rgb(203,230,255);
  width: 1.36rem;
  text-align: center;
  height: 0.563rem;
  line-height: 0.563rem;
  color: rgb(101,168,255);
  font-size: 0.267rem;
  border-radius: 0.3165rem;
}
.cate-list-page-new .see-more{
  position: relative;
  padding-right: 0.2rem;
}
.cate-list-page-new .displayBox .cateList.cateItemList .cateItem .title .see-more:before{
  content: " ";
  position: absolute;
  display: inline-block;
  height: 0.16rem;
  width: 0.16rem;
  border-width:0 0 0.04rem 0.04rem ;
  border-color: #4993fa;
  border-style: solid;
  -webkit-transform: matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  transform: matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  right: 0.24rem;
  top: 0.18rem;
}
.displayBox .cateList.cateItemList .item p{
  color: rgb(102,102,102);
}
.displayBox .cateList .cateItem .list {
  overflow: hidden;
  background: #fff;
}
.displayBox .cateList .item {
  display: block;
  width: 32%;
  background: #fff;
  float: left;
  padding: 0.26rem;
  margin-bottom: 0.133333rem;
}
.displayBox .cateList .item:nth-child(3n + 2) {
  margin-left: 2%;
  margin-right: 2%;
}
.displayBox .cateList .item .img {
  width: 1.6rem;
  height: 1.6rem;
  margin: 0 auto 0.2rem;
}
.displayBox .cateList .item .img img {
  display: block;
  width: 100%;
  height: 100%;
}
.displayBox .cateList .item p {
  text-align: center;
  font-size: 0.32rem;
  color: #333;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.paymentPage {
  padding-bottom: 2.4rem;
}
.paymentPage .paymentAmount {
  line-height: 1.30666667rem;
  font-size: 1.06666667rem;
  color: #212121;
  text-align: center;
  padding-top: 0.26666667rem;
  padding-bottom: 0.13333333rem;
  background: #f0f0f0;
}
.paymentPage .paymentAmount .inner {
  position: relative;
  padding: 0.50666667rem;
  background: #fff;
}
.paymentPage .paymentAmount .inner:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.paymentPage .paymentAmount .inner:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.paymentPage .paymentAmount .icon_coin {
  display: inline-block;
  width: 1.30666667rem;
  height: 1.30666667rem;
  background: url(../img/square/icon_coin.png) center center / contain no-repeat;
  vertical-align: middle;
  margin-top: -0.08rem;
  margin-right: 0.26666667rem;
}
.paymentPage .select_payment_way {
  background: #fff;
}
.paymentPage .select_payment_way .option {
  position: relative;
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
}
[data-dpr="1"] .paymentPage .select_payment_way .option {
  border: none;
}
[data-dpr="1"] .paymentPage .select_payment_way .option:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
[data-dpr="1"] .paymentPage .select_payment_way .option:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.paymentPage .select_payment_way header {
  position: relative;
  line-height: 0.8rem;
  font-size: 0.42666667rem;
  padding: 0.32rem 0.4rem;
}
.paymentPage .select_payment_way header.fli_link_line:after {
  top: 0.6rem;
}
.paymentPage .select_payment_way header span {
  display: block;
  width: 0.8rem;
  height: 0.8rem;
  margin-right: 0.29333333rem;
  float: left;
}
.paymentPage .select_payment_way header span.icon_coin {
  background: url(../img/square/text_coin.png) center center / contain no-repeat;
}
.paymentPage .select_payment_way header span.icon_weixin {
  background: url(../img/icon_weixin.png) center center / contain no-repeat;
}
.paymentPage .select_payment_way header.active + .coin_pay_section {
  display: block;
}
.paymentPage .select_payment_way .coin_pay_section {
  display: none;
  background: #f8f7f7;
}
.paymentPage .select_payment_way .coin_pay_section .item a .right {
  color: #4993fa;
  font-weight: 800;
}
.paymentPage .select_payment_way .coin_pay_section .item a .right .radio_section input[type="checkbox"]:checked + label,
.paymentPage .select_payment_way .coin_pay_section .item a .right .radio_section input[type="radio"]:checked + label {
  background-color: #4993fa;
}
.paymentPage .order_created {
  color: #444;
}
.paymentPage .order_created .ybtAmount {
  display: none;
  text-align: center;
  color: #ff0030;
  font-size: 0.48rem;
}
.paymentPage .order_created div.time_left {
  display: none;
}
.paymentPage .order_created .img_suc {
  display: block;
  width: 2.13333333rem;
  height: auto;
  margin-top: 0.34666667rem;
  margin-bottom: 0.34666667rem;
  margin-left: auto;
  margin-right: auto;
}
.paymentPage .order_created > p {
  width: 6.66666667rem;
  font-size: 0.34rem;
  line-height: 0.48rem;
  margin-left: auto;
  margin-right: auto;
}
.paymentPage .order_created > p b {
  font-size: 0.42rem;
  color: #4993fa;
}
.paymentPage .order_created .orderCancelSpan {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.05rem;
  color: red;
}
.paymentPage .order_created .order_info {
  position: relative;
  margin-top: 0.34666667rem;
  padding: 0.26666667rem 0.4rem;
}
.paymentPage .order_created .order_info:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.paymentPage .order_created .order_info p {
  line-height: 0.61333333rem;
  max-height: 3.06666667rem;
  overflow: hidden;
}
.paymentPage .order_created .order_info p span + span {
  margin-left: 0.3rem;
}
.paymentPage .make_split {
  padding-top: 0.26666667rem;
  padding-bottom: 0.26666667rem;
  background: #f0f0f0;
}
.paymentPage .make_split .tab_theme_section {
  margin: 0;
}
.paymentPage .weixin_pay_section {
  background: #fff;
  width: 100%;
}
.paymentPage .weixin_pay_section header {
  position: relative;
  color: #4993fa;
  line-height: 1.30666667rem;
  padding-left: 0.4rem;
  background: #f9f9f9;
  margin-top: 0.24rem;
}
.paymentPage .weixin_pay_section header:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.paymentPage .weixin_pay_section header:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.paymentPage .weixin_pay_section .item {
  padding: 0rem 0.4rem;
}
.paymentPage .weixin_pay_section .item button {
  float: right;
  outline: none;
  color: #fff;
  text-align: center;
  border: none;
  height: 0.67777rem;
  line-height: 0.67777rem;
  width: auto;
  vertical-align: middle;
  font-size: 0.36rem;
  padding: 0 0.15rem;
  margin-top: 0.1rem;
  border-radius: 0.09rem;
}
.paymentPage .weixin_pay_section .item button:focus {
  outline: none;
  border: none;
}
.paymentPage .weixin_pay_section .item .weixinpay button {
  background-color: #43ad41;
}
.paymentPage .weixin_pay_section .item .alipay button {
  background-color: #4993fa;
}
.paymentPage .weixin_pay_section .item .ybtpay button {
  background-color: #e60012;
}
.paymentPage .weixin_pay_section .item .weixinpay button[disabled],
.paymentPage .weixin_pay_section .item .alipay button[disabled],
.paymentPage .weixin_pay_section .item .ybtpay button[disabled] {
  background-color: #999;
}
.paymentPage .weixin_pay_section .item a {
  position: relative;
  padding: 0.24rem 0rem;
  display: block;
  overflow: hidden;
  color: #444;
  line-height: 0.86666667rem;
  border-bottom: 1px solid #e5e5e5;
}
.paymentPage .weixin_pay_section .item a.fli_link_line:after {
  right: 0.066666rem;
}
[data-dpr="1"] .paymentPage .weixin_pay_section .item a {
  border-bottom: none;
}
[data-dpr="1"] .paymentPage .weixin_pay_section .item a:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.paymentPage .weixin_pay_section .item a img {
  display: block;
  float: left;
  width: auto;
  height: 0.86666667rem;
  margin-right: 0.24rem;
}
.paymentPage .coin_pay_section {
  background: #fff;
  width: 100%;
}
.paymentPage .coin_pay_section .item {
  padding: 0rem 0.4rem;
  margin-bottom: 0.24rem;
}
.paymentPage .coin_pay_section .item a {
  padding: 0.24rem 0rem;
  display: block;
  color: #444;
  line-height: 0.86666667rem;
}
.paymentPage .coin_pay_section .item a span {
  font-size: 0.32rem;
}
.paymentPage .coin_pay_section .item a .right {
  font-weight: 600;
  color: #ee0a3b;
}
.paymentPage .coin_pay_section .item a .right .radio_section {
  float: right;
  margin-top: 0.16rem;
  margin-left: 0.21333333rem;
}
.paymentPage .coin_pay_section .item a img {
  display: block;
  float: left;
  width: auto;
  height: 0.86666667rem;
  margin-right: 0.24rem;
}
.paymentPage .coin_pay_section .item a .total {
  height: 0.74666667rem;
  line-height: 0.74666667rem;
  padding-left: 0.82666667rem;
  background: url(../img/pic_dollar.png) left center / contain no-repeat;
}
.paymentPage .coin_pay_section .item a .total span {
  font-size: 0.42rem;
  color: #ee0a3b;
}
.paymentPage .coin_pay_section .item + .item a {
  position: relative;
}
.paymentPage .coin_pay_section .item + .item a:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.paymentPage + .submit_section {
  position: absolute;
  bottom: 0.96rem;
  left: 0;
  width: 100%;
}
.paymentPage + .submit_section .coin_total_section {
  font-size: 0.426666rem;
  color: #444;
  text-align: center;
}
.paymentPage + .submit_section .coin_total_section .icon_dollar {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.053333rem;
  margin-right: 0.1rem;
  width: 0.773333rem;
  height: 0.773333rem;
  background: url(../img/pic_dollar.png) center center / contain no-repeat;
}
.paymentPage + .submit_section .coin_total_section .num {
  color: #ee0a3b;
  margin-left: 0.04rem;
}
.noUserInfo {
  border: 1px;
  background-color: #ffe7bc;
  font-size: 0.32rem;
  padding: 0.1333rem 0.16rem;
  border-radius: 0.08rem;
}
.orderPage {
  position: relative;
}
.orderPage:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #f2f2f2;
  color: #f2f2f2;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.fixed_tab_page {
  padding-top: 1.3rem;
}
.fixed_tab_page .tab_theme_section {
  top: 1.1733rem;
  margin: 0;
}
.order_items_section {
  color: #444;
  margin-bottom: 0.2rem;
}
.order_items_section ul.hotel_order_list li .item .title {
  padding: 0;
  margin-bottom: 0.2rem;
}
.order_items_section ul.hotel_order_list li .item .attrs {
  width: 100%;
  text-align: left;
  padding: 0;
}
.order_items_section ul.hotel_order_list li .item .attrs span:first-child {
  margin-bottom: 0;
}
.order_items_section ul li {
  background: #fff;
  margin-top: 0.2rem;
  max-width: 100%;
  overflow: hidden;
}
.order_items_section ul li header {
  position: relative;
  line-height: 0.86rem;
  padding: 0.12rem 0.4rem;
  font-size: 0.38rem;
  overflow: hidden;
}
.order_items_section ul li header:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.order_items_section ul li header .status {
  float: right;
  color: #999;
}
.order_items_section ul li header .status.complete {
  color: #fd1b3b;
}
.order_items_section ul li header .icon_del {
  display: block;
  margin-top: 0.13rem;
  margin-left: 0.13rem;
  width: 0.53rem;
  height: 0.53rem;
  float: right;
  background: url(../img/icon_delete.png) center center / contain no-repeat;
}
.order_items_section ul li header .icon_del:after {
  content: url(../img/icon_delete_active.png);
  display: none;
}
.order_items_section ul li header .icon_del:active {
  background: url(../img/icon_delete_active.png) center center / contain no-repeat;
}
.order_items_section ul li .item {
  position: relative;
  display: table;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}
.order_items_section ul li .item:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.order_items_section ul li .item > div {
  display: table-cell;
  vertical-align: middle;
}
.order_items_section ul li .item .img {
  width: 2.4rem;
  height: 2.4rem;
  padding: 0.4rem;
}
.order_items_section ul li .item .img img {
  height: 1.6rem;
}
.order_items_section ul li .item .attrs {
  width: 2.66666667rem;
  font-size: 0.32rem;
  color: #999;
  text-align: center;
  padding: 0.4rem 0.13333333rem;
}
.order_items_section ul li .item .attrs > span {
  display: block;
}
.order_items_section ul li .item .attrs > span:first-child {
  margin-bottom: 0.2rem;
}
.order_items_section ul li .item .title {
  color: #444;
  padding: 0.4rem 0rem;
}
.order_items_section ul li .item .title p {
  max-height: 1.6rem;
  line-height: 0.53333333rem;
  overflow: hidden;
  font-size: 0.36rem;
}
.order_items_section ul li .item .title p.text_gray {
  font-size: 0.32rem;
  color: #999;
  margin-top: 0.06rem;
}
.order_items_section ul li .item .title p .text-primary {
  font-size: 0.32rem;
  color: #4993fa;
}
.order_items_section ul li .item .title p span.name {
  font-size: 0.32rem;
}
.order_items_section ul li footer {
  position: relative;
  padding: 0 0.4rem;
  height: 1.17333333rem;
  line-height: 1.17333333rem;
  border-top: 1px solid #e5e5e5;
}
.order_items_section ul li footer .total_price {
  color: #4993fa;
  font-size: 0.38rem;
}
.order_items_section ul li footer .btns {
  float: right;
}
.order_items_section ul li footer .btns input[type="button"] {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.05333333rem;
  border: none;
  background: transparent;
  font-size: 0.34rem;
  padding-left: 0.34666667rem;
}
.order_items_section ul li footer .btns .btn_gray {
  color: #999;
}
.order_items_section ul li footer .btns .btn_theme {
  color: #4993fa;
}
.order_items_section ul li footer .btns .btn_gray:active {
  color: rgba(153, 153, 153, 0.6);
}
.order_items_section ul li footer .btns .btn_theme:active {
  color: rgba(52, 109, 252, 0.6);
}
.userCenterPage > section {
  background: #fff;
  margin-top: 0.24rem;
}
.userCenterPage .user_info {
  padding: 0.21333333rem 0.85333333rem;
}
.userCenterPage .user_info .img {
  width: 1.38666667rem;
  height: 1.38666667rem;
  border-radius: 100%;
  overflow: hidden;
  float: left;
}
.userCenterPage .user_info .img img {
  height: 1.38666667rem;
}
.userCenterPage .user_info .info {
  height: 1.38666667rem;
  margin-left: 1.68rem;
  padding-top: 0.21333333rem;
}
.userCenterPage .user_info .info p {
  font-size: 0.34rem;
  height: 0.56rem;
  line-height: 0.56rem;
  color: #444;
}
.userCenterPage .user_info .info p:first-child {
  font-size: 0.42rem;
}
.userCenterPage .coin_section {
  padding: 0.48rem 0.4rem;
  font-size: 0.38rem;
  color: #444;
  font-weight: 800;
  overflow: hidden;
}
.userCenterPage .coin_section label {
  display: block;
  width: 1.6rem;
  float: left;
}
.userCenterPage .coin_section .coin_list {
  margin-left: 1.6rem;
}
.userCenterPage .coin_section .coin_list .item {
  float: left;
}
.userCenterPage .coin_section .coin_list .item span {
  font-weight: 100;
  margin-left: 0.61333333rem;
}
.userCenterPage .coin_section .coin_list .item span b {
  font-weight: 800;
  color: #4993fa;
}
.userCenterPage .list {
  overflow: hidden;
  padding-top: 0.26666667rem;
  padding-bottom: 0.26666667rem;
}
.userCenterPage .list li {
  width: 20%;
  float: left;
}
.userCenterPage .list li a {
  position: relative;
  display: block;
  overflow: hidden;
  text-align: center;
}
.userCenterPage .list li a span {
  display: inline-block;
  width: 0.8rem;
  height: 0.69333333rem;
}
.userCenterPage .list li a span.icon_header_1 {
  background: url(../img/userCenter/icon_header_1.png) center center / contain no-repeat;
}
.userCenterPage .list li a span.icon_header_2 {
  background: url(../img/userCenter/icon_header_2.png) center center / contain no-repeat;
}
.userCenterPage .list li a span.icon_header_3 {
  background: url(../img/userCenter/icon_header_3.png) center center / contain no-repeat;
}
.userCenterPage .list li a span.icon_header_4 {
  background: url(../img/userCenter/icon_header_4.png) center center / contain no-repeat;
}
.userCenterPage .list li a span.icon_header_5 {
  background: url(../img/userCenter/icon_header_5.png) center center / contain no-repeat;
}
.userCenterPage .list li a span.icon_header_1:after {
  display: none;
  content: url(../img/userCenter/icon_header_1a.png);
}
.userCenterPage .list li a span.icon_header_2:after {
  display: none;
  content: url(../img/userCenter/icon_header_2a.png);
}
.userCenterPage .list li a span.icon_header_3:after {
  display: none;
  content: url(../img/userCenter/icon_header_3a.png);
}
.userCenterPage .list li a span.icon_header_4:after {
  display: none;
  content: url(../img/userCenter/icon_header_4a.png);
}
.userCenterPage .list li a span.icon_header_5:after {
  display: none;
  content: url(../img/userCenter/icon_header_5a.png);
}
.userCenterPage .list li a .text {
  font-size: 0.32rem;
  color: #666;
}
.userCenterPage .list li a:hover span.icon_header_1 {
  background: url(../img/userCenter/icon_header_1a.png) center center / contain no-repeat;
}
.userCenterPage .list li a:hover span.icon_header_2 {
  background: url(../img/userCenter/icon_header_2a.png) center center / contain no-repeat;
}
.userCenterPage .list li a:hover span.icon_header_3 {
  background: url(../img/userCenter/icon_header_3a.png) center center / contain no-repeat;
}
.userCenterPage .list li a:hover span.icon_header_4 {
  background: url(../img/userCenter/icon_header_4a.png) center center / contain no-repeat;
}
.userCenterPage .list li a:hover span.icon_header_5 {
  background: url(../img/userCenter/icon_header_5a.png) center center / contain no-repeat;
}
.userCenterPage .list li a:hover .text {
  color: #4993fa;
}
.userCenterPage .functionSection {
  background: #ffffff;
  margin-top: 0.26666667rem;
}
.userCenterPage .functionSection a {
  position: relative;
  display: block;
  height: 1.33333333rem;
  line-height: 1.33333333rem;
  color: #444444;
  font-size: 0.42rem;
  padding-left: 0.26666667rem;
  padding-right: 0.26666667rem;
  border-bottom: 1px solid #e5e5e5;
}
.userCenterPage .functionSection a .num_circle {
  vertical-align: middle;
  margin-top: -0.066666rem;
}
[data-dpr="1"] .userCenterPage .functionSection a {
  border-bottom: none;
}
[data-dpr="1"] .userCenterPage .functionSection a:before {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.userCenterPage .functionSection a:active {
  background-color: rgba(0, 0, 0, 0.03);
}
.userCenterPage .functionSection a > span:first-child {
  display: inline-block;
  margin-right: 0.16rem;
  width: 0.8rem;
  height: 0.8rem;
  vertical-align: middle;
  margin-top: -0.053rem;
}
.userCenterPage .functionSection a > span.icon_list_1 {
  background: url(../img/userCenter/icon_list_1.png) center center / contain no-repeat;
}
.userCenterPage .functionSection a > span.icon_list_2 {
  background: url(../img/userCenter/icon_list_2.png) center center / contain no-repeat;
}
.userCenterPage .functionSection a > span.icon_list_3 {
  background: url(../img/userCenter/icon_list_3.png) center center / contain no-repeat;
}
.userCenterPage .functionSection a > span.icon_list_4 {
  background: url(../img/userCenter/icon_list_4.png) center center / contain no-repeat;
}
.userCenterPage .functionSection a > span.icon_list_5 {
  background: url(../img/userCenter/icon_list_5.png) center center / contain no-repeat;
}
.userCenterPage .functionSection a > span.icon_list_6 {
  background: url(../img/userCenter/icon_list_6.png) center center / contain no-repeat;
}
.userCenterPage .functionSection a > span.icon_list_7 {
  background: url(../img/userCenter/icon_list_7.png) center center / contain no-repeat;
}
.userCenterPage .functionSection a > span.icon_list_8 {
  background: url(../img/userCenter/icon_list_8.png) center center / contain no-repeat;
}
.userCenterPage .functionSection a > span.icon_list_9 {
  background: url(../img/userCenter/icon_list_9.png) center center / contain no-repeat;
}
.userCenterPage .functionSection a > span.icon_list_10 {
  background: url(../img/userCenter/icon_list_10.png) center center / contain no-repeat;
}
.userCenterPage .functionSection a > span.icon_list_11 {
  background: url(../img/userCenter/icon_list_11.png) center center / contain no-repeat;
}
.userCenterPage .functionSection a > span.icon_list_12 {
  background: url(../img/userCenter/icon_list_12.png) center center / contain no-repeat;
}
.userCenterPage .logout {
  display: block;
  width: 100%;
  height: 1.36rem;
  line-height: 1.36rem;
  background: #fff;
  margin-top: 0.24rem;
  margin-bottom: 0.24rem;
  color: #999;
  font-size: 0.42rem;
  text-align: center;
}
.lock {
  overflow: hidden;
  touch-action: none;
}
.myeweima {
  width: 100%;
  height: 100%;
}
.myeweima .codebox {
  width: 100%;
  height: 100%;
  background-color: #fff;
  z-index: 10000;
  display: none;
  overflow: hidden;
  padding-top: 0.7333rem;
}
.myeweima .codebox .showcode {
  width: 85%;
  height: auto;
  margin: 2rem auto 0;
  padding: 0.5rem 0.5rem 0.2rem;
  border-radius: 0.22rem;
  background-color: #fff;
  text-align: center;
}
.myeweima .codebox .showcode #qrcodeCanvas {
  width: 100%;
  display: none;
  z-index: -1;
}
.myeweima .codebox .showcode #qrcodeCanvas canvas {
  width: 100%;
  height: auto;
}
.myeweima .codebox .showcode #qrcodeImg {
  width: 100%;
  height: auto;
}
.myeweima .codebox .showcode p {
  width: 100%;
  font-size: 0.45rem;
  line-height: 1rem;
  margin: 0.2rem 0;
  color: #999;
}
.myeweima .codebox .showcode .down_btn_a {
  display: inline-block;
  width: 4rem;
  height: 1rem;
  line-height: 1rem;
  background-color: #4993fa;
  color: #fff;
  font-size: 0.36rem;
  text-align: center;
  border-radius: 0.13rem;
}
.userCenterPage .logout:active {
  background: rgba(0, 0, 0, 0.03);
}
.personInfoPage section {
  background: #fff;
}
.personInfoPage section .item {
  position: relative;
  min-height: 1.2rem;
  line-height: 1.2rem;
  padding: 0 0.4rem;
  color: #444444;
  font-size: 0.36rem;
  border-bottom: 1px solid #f1f1f1;
}
.personInfoPage section .item:after {
  content: " ";
  display: block;
  font-size: 0;
  height: 0;
  clear: both;
  visibility: hidden;
}
.personInfoPage section .item .switch {
  float: right;
  margin-top: 0.3rem;
}
.personInfoPage section .item:active {
  background: rgba(0, 0, 0, 0.02);
}
.personInfoPage section .item a {
  width: 63%;
  min-width: 20%;
  height: 100%;
  float: right;
  text-align: right;
  color: #444;
  font-size: 0.36rem;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.personInfoPage section .item a.fli_link_line {
  padding-right: 0.42666667rem;
}
.personInfoPage section .item a.fli_link_line:after {
  right: 0;
  top: 0.4rem;
}
.personInfoPage section .item a input,
.personInfoPage section .item a select {
  display: inline-block;
  font-size: 0.42rem;
  color: #999999;
  width: 100%;
  text-align: right;
  background: transparent;
  border: none;
}
.personInfoPage section .item a select {
  width: 2.66666667rem;
}
.personInfoPage section .item a select option {
  direction: rtl;
}
.personInfoPage section .item a .img_camera {
  display: inline-block;
  width: 1.17333333rem;
  height: 1.17333333rem;
  vertical-align: middle;
  border-radius: 100%;
  overflow: hidden;
  background: url(../img/pic_head.png) center center / contain no-repeat;
}
.personInfoPage section .item a .img_camera img {
  display: block;
  width: 100%;
  height: 1.17333333rem;
}
.addr_select {
  margin-top: 0.2rem;
  padding-bottom: 0.13333333rem;
}
.addr_select li {
  position: relative;
  background: #fff;
  overflow: hidden;
  margin-bottom: 0.026667rem;
  padding: 0.5333rem 0.77733rem;
}
.addr_select li.active {
  color: #4993fa;
  background: url("../img/dj2.png") left center no-repeat #ffffff;
  /*background-size: 0.66666667rem 0.48rem;*/
  background-size: 0.52rem 0.52rem;
  background-origin: content-box;
}

.addr_select li.active .addr_info {
  padding-left: 0.85333333rem;
}
.addr_select li .addr_info {
  width: 7.2rem;
}
.addr_select li .addr_info .user {
  margin-bottom: 0.4rem;
  color: rgb(51,51,51);
  font-size: 0.3733rem;
  font-weight: bold;

}

.addr_select li.active .addr_info .user span,.addr_select li.active .addr_info .user{
  color: #4993fa!important;
}
.weixinCouponSelectAddressPage .addr_select li.active .addr_info .user span,.weixinCouponSelectAddressPage  .addr_select li.active .addr_info .user{
  color: #ea3a52!important;
}
.weixinCouponSelectAddressPage .fli_checkbox_blue input[type="checkbox"]:checked + label,
.weixinCouponSelectAddressPage .fli_checkbox_blue input[type="radio"]:checked + label{
  background-color:#ea3a52;
}
.weixinCouponSelectAddressPage  .addr_select li.active .addr_info{
  padding-left: 0;
}
.weixinCouponSelectAddressPage .addr_select li .addr_info .user span.default{
  color: #ffffff!important;
}
.weixinCouponSelectAddressPage .addr_select li.active{
  /*background-image: url("./../img/dj3.png");*/
  background-image:initial;
}
.weixinCouponSelectAddressPage .addr_select li .addr_info{
  width: calc(100% - 1.4rem);
}
.weixinCouponSelectAddressPage .addr_select li .addr_edit{
  width: 0.6rem;
  height: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.addr_select li .addr_info .user span {
  margin-left: 0.4267rem;
  color: rgb(153,153,153)!important;
  font-weight: normal;
}
.addr_select li .addr_info .user span.default{
background: rgb(255,83,31);
  color: rgb(255,255,255)!important;
  font-size: 0.24rem;
  display: block;
  /*width: 0.64rem;*/
  /*height: 0.32rem;*/
  border-radius: 0.08rem;
  text-align: center;
  /*line-height: 0.32rem;*/
  padding: 0.02rem 0.03rem;
}
.addr_select li .addr_info .addr {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.32rem;
  color: rgb(102,102,102);

}
.addr_select li .addr_info .addr .default {
  display: inline-block;
  vertical-align: middle;
  color: #fff;
  background: #fd1b3b;
  line-height: 0.48rem;
  padding-left: 0.26666667rem;
  padding-right: 0.26666667rem;
}
.addr_select li .addr_edit {
  /*position: absolute;*/
  /*top: 0.37333333rem;*/
  /*right: 0.26666667rem;*/
  /*width: 1.73333333rem;*/
  /*height: 1.22666667rem;*/
  /*padding-left: 0.42666667rem;*/
  /*padding-top: 0.08rem;*/
}
.addr_select li .addr_edit:before {
  /*content: " ";*/
  /*position: absolute;*/
  /*left: 0;*/
  /*top: 0;*/
  /*bottom: 0;*/
  /*width: 1px;*/
  /*border-left: 1px solid #999;*/
  /*color: #999;*/
  /*-webkit-transform-origin: 0 0;*/
  /*transform-origin: 0 0;*/
  /*-webkit-transform: scaleX(0.5);*/
  /*transform: scaleX(0.5);*/
}
.addr_select li .addr_edit .icon_edit {
  display: inline-block;
  width: 0.2667rem;
  height: 0.32rem;
  background: url("../img/cake/other/bianji_icon.png") center center / cover no-repeat;
}
.addrList .btns {
  padding: 1rem 0;
}
.addrList .btns .btn_submit_long {
  margin: 0 auto;
}
.select_address_page  .addr_select{
  padding-bottom: 1.8rem;
}
.select_address_page #addAdressBtn{
  position: fixed;
  bottom: 0.42rem;
  left: 50%;
  transform: translateX(-50%);
}
.select_address_page .addr_select li{
  margin: 0 0.32rem 0.24rem;
  border-radius: 0.23rem;
}
.addr_list {
  padding-bottom: 0.4rem;
}
.addr_list li {
  position: relative;
  background: #fff;
  overflow: hidden;
  margin-top: 0.2rem;
  padding: 0.45rem 0.4rem;
}
.addr_list li .addr_info {
  padding-bottom: 0.5rem;
}
.addr_list li .addr_info .user {
  font-size: 0.42rem;
  height: 0.5rem;
  line-height: 0.5rem;
  margin-bottom: 0.13rem;
}
.addr_list li .addr_info .user span {
  margin-left: 1.1rem;
}
.addr_list li .addr_info .addr {
  /*font-size: 0.34rem;*/
  /*color: #999;*/
  max-height: 1.92rem;
  line-height: 0.48rem;
  font-size: 0.32rem;
  color: rgb(102,102,102);
}
.addr_list li .addr_opration {
  position: relative;
  font-size: 0.34rem;
  color: #999;
  padding-top: 0.33rem;
  overflow: hidden;
}
.addr_list li .addr_opration:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.addr_list li .addr_opration .set_default {
  float: left;
  padding-top: 0.05rem;
}
.addr_list li .addr_opration .set_default .radio_section {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.05rem;
  margin-right: 0.13rem;
}
.addr_list li .addr_opration .right {
  overflow: hidden;
}
.addr_list li .addr_opration .right a {
  display: block;
  float: left;
  color: #999;
  line-height: 0.75rem;
  margin-left: 0.6rem;
}
.addr_list li .addr_opration .right a:active {
  color: #4993fa;
}
.addr_list li .addr_opration .right a span {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.05rem;
  margin-right: 0.13rem;
  /*width: 0.75rem;*/
  /*height: 0.75rem;*/
  width: 0.2667rem;
  height: 0.32rem;
}
.addr_list li .addr_opration .right a span.icon_edit {
  background: url(../img/cake/other/bianji_icon.png) center center / contain no-repeat;
}
.addr_list li .addr_opration .right a span.icon_edit:after {
  content: url(../img/icon_edit_active.png);
  display: none;
}
.addr_list li .addr_opration .right a span.icon_del {
  background: url(../img/icon_delete.png) center center / cover no-repeat;
}
.addr_list li .addr_opration .right a span.icon_del:after {
  content: url(../img/icon_delete_active.png);
  display: none;
}
.addr_list li .addr_opration .right a:active span.icon_edit {
  background: url(../img/icon_edit_active.png) center center / contain no-repeat;
}
.addr_list li .addr_opration .right a:active span.icon_del {
  background: url(../img/icon_delete_active.png) center center / contain no-repeat;
}
.serviceForm .service_section {
  font-size: 0.32rem;
  padding-bottom: 0.4rem;
}
.serviceForm .service_section > .item {
  margin: 0.16rem 0;
  padding: 0.266666rem;
  background: #fff;
}
.serviceForm .service_section > .item header {
  position: relative;
  margin-bottom: 0.266666rem;
}
.serviceForm .service_section > .item header .title_tips {
  font-size: 0.28rem;
  color: #999;
  margin-left: 0.266666rem;
}
.serviceForm .service_section > .item .input_section .item_radio_btn {
  display: inline-block;
  padding: 0 20px;
  min-width: 2rem;
  line-height: 0.666666rem;
  border: 1px solid #999;
  border-radius: 0.066666rem;
  text-align: center;
  margin: 0 0.266666rem 0.133333rem 0;
  color: #999;
  font-size: 0.28rem;
}
.serviceForm .service_section > .item .input_section .item_radio_btn.active {
  border: 1px solid #4993fa;
  color: #4993fa;
}
.serviceForm .service_section > .item .input_section.must {
  margin-right: 0;
}
.serviceForm .service_section > .item .textarea {
  position: relative;
}
.serviceForm .service_section > .item .textarea textarea {
  display: block;
  width: 100%;
  height: 4rem;
  background: #f5f5f5;
  padding: 0.266666rem 0.266666rem 0.5rem;
  font-size: 0.28rem;
  line-height: 0.4rem;
  border: none;
  border-radius: 0.106666rem;
  resize: none;
}
.serviceForm .service_section > .item .textarea .text_limit {
  position: absolute;
  right: 0.266666rem;
  bottom: 0.266666rem;
  font-size: 0.28rem;
  color: #999;
}
.serviceForm .service_section > .item .img_list {
  padding: 0.266666rem;
  background: #f5f5f5;
  border-top: 1px solid #ddd;
}
.serviceForm .service_section > .item .img_list a {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  border: 1px solid #ddd;
  width: 1.333333rem;
  height: 1.333333rem;
  margin-right: 0.266666rem;
  background: #fff;
}
.serviceForm .service_section > .item .img_list a img {
  display: block;
  width: 100%;
  height: auto;
}
.serviceForm .service_section > .item .img_list a .del {
  display: block;
  position: absolute;
  right: -0.2rem;
  top: -0.2rem;
  width: 0.4rem;
  height: 0.4rem;
  background: url(../img/icon_close_circle.png) center center / contain no-repeat;
}
.serviceForm .service_section > .item .icon_camera {
  display: block;
  margin-top: 0.266666rem;
  margin-left: 0.266666rem;
  width: 0.733333rem;
  height: 0.586666rem;
  background: url(../img/icon_camera.png) center center / contain no-repeat;
}
.serviceForm .service_section > .item .form_section {
  margin: 0 -0.266666rem -0.266666rem;
}
.serviceForm .service_section > .item .form_section .item.fli_link_line:after {
  top: 0.8rem;
}
.serviceForm .service_section > .item .form_section .item .inputs .input_section .item_radio_btn {
  margin: 0 0.133333rem 0.133333rem 0;
}
.serviceForm .service_section > .item .form_section label {
  font-size: 0.32rem;
}
.serviceForm .service_section > .item .form_section .input {
  font-size: 0.28rem;
  width: 7.333333rem;
}
.serviceForm .service_section > .item .form_section .input_section {
  display: inline-block;
}
.serviceForm .service_section > .item .form_section .input_section.label_fixed_right {
  position: absolute;
  right: 0;
  top: 0.2667rem;
}
[data-dpr="1"] .serviceForm .service_section > .item .input_section .item_radio_btn {
  position: relative;
  border: none;
  padding: 0 0.133333rem;
}
[data-dpr="1"] .serviceForm .service_section > .item .input_section .item_radio_btn:after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid #ddd;
  -webkit-transform: scale(0.5);
  transform: scale(0.5);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: 10px;
  z-index: 0;
  pointer-events: auto;
}
[data-dpr="1"] .serviceForm .service_section > .item .input_section .item_radio_btn.active:after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid #4993fa;
  -webkit-transform: scale(0.5);
  transform: scale(0.5);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: 10px;
  z-index: 0;
  pointer-events: auto;
}
.retrive_change {
  font-size: 0.34rem;
  line-height: 0.4rem;
  padding: 0.4rem;
  text-align: left;
}
.retrive_change .margin_bottom {
  margin-bottom: 0.4rem;
}
.retrive_change .margin_top {
  margin-top: 0.4rem;
}
.retrive_change .btn_submit_long {
  width: 3.36rem;
  height: 0.88rem;
  line-height: 0.88rem;
}
.ticketPage .tab_theme_section {
  margin-top: 0;
}
.ticket_list ul.unavailable li {
  background: url(../img/bg_ticket_disabled.png) left top / contain no-repeat #ffffff;
  color: #9d9d9d;
}
.ticket_list ul.unavailable li .ticket_info .amount p {
  color: #9d9d9d;
}
.ticket_list ul.unavailable li .ticket_type {
  color: #9d9d9d;
}
.ticket_list ul li {
  position: relative;
  width: 9.2rem;
  height: 3.73333333rem;
  margin-bottom: 0.26666667rem;
  border-width: 0.03rem;
  border-style: solid;
  border-color: #fff;
  border-top-width: 0;
  background: url(../img/bg_ticket.png) left top / contain no-repeat #ffffff;
  background-origin: border-box;
  margin-left: auto;
  margin-right: auto;
  border-radius: 0.13333333rem;
}
.ticket_list ul li .ticket_info {
  padding-top: 0.4rem;
}
.ticket_list ul li .ticket_info .amount {
  width: 3rem;
  float: left;
  padding-left: 0.37333333rem;
}
.ticket_list ul li .ticket_info .amount p {
  color: #ee0a3b;
}
.ticket_list ul li .ticket_info .amount p:first-child {
  font-size: 0.64rem;
  font-weight: 800;
  margin-bottom: -0.13rem;
  margin-left: -0.11rem;
}
.ticket_list ul li .ticket_info .amount p:first-child.text {
  margin: 0.16rem 0;
}
.ticket_list ul li .ticket_info .amount p span {
  font-size: 1rem;
}
.ticket_list ul li .ticket_info .limit {
  padding-top: 0.13rem;
  margin-left: 3rem;
}
.ticket_list ul li .ticket_info .limit p {
  font-size: 0.38rem;
  max-width: 90%;
  line-height: 0.53rem;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ticket_list ul li .ticket_type {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1.22666667rem;
  line-height: 1.22666667rem;
  text-align: center;
  font-size: 0.48rem;
  color: #ee0a3b;
  border-top-width: 0.03rem;
  border-top-style: dashed;
  border-top-color: #d1d1d1;
}
.ticket_list ul li .ticket_type:before {
  content: " ";
  position: absolute;
  top: -0.28rem;
  left: -0.28rem;
  width: 0.56rem;
  height: 0.56rem;
  border-radius: 100%;
  background: #f0f0f0;
  z-index: 99;
}
.ticket_list ul li .ticket_type:after {
  content: " ";
  position: absolute;
  top: -0.28rem;
  right: -0.28rem;
  width: 0.56rem;
  height: 0.56rem;
  border-radius: 100%;
  background: #f0f0f0;
  z-index: 99;
}
.ticket_list ul li.active {
  border-color: #ee0a3b;
}
.ticket_list ul li.active .ticket_type:before,
.ticket_list ul li.active .ticket_type:after {
  border-width: 0.03rem;
  border-style: solid;
  border-color: #ee0a3b;
}
.ticket_list ul li.active:before {
  content: " ";
  position: absolute;
  bottom: 0.88rem;
  left: -0.57333333rem;
  width: 0.56rem;
  height: 0.61333333rem;
  background: #f0f0f0;
  z-index: 999;
}
.ticket_list ul li.active:after {
  content: " ";
  position: absolute;
  bottom: 0.88rem;
  right: -0.57333333rem;
  width: 0.56rem;
  height: 0.61333333rem;
  background: #f0f0f0;
  z-index: 999;
}
.ticket_list ul li.about_to_expire:before {
  content: " ";
  position: absolute;
  right: 0;
  top: 0;
  width: 1.94666667rem;
  height: 1.94666667rem;
  background: url(../img/pic_tag_1.png) top right / contain no-repeat;
}
.ticket_list ul li.expired:before {
  content: " ";
  position: absolute;
  right: 0;
  top: 0;
  width: 1.94666667rem;
  height: 1.94666667rem;
  background: url(../img/pic_tag_2.png) top right / contain no-repeat;
}
.ticket_list ul li.noEffect:before {
  content: " ";
  position: absolute;
  right: -0.1333rem;
  top: 0;
  width: 2.16rem;
  height: 2.16rem;
  background: url(../img/noEffect.png) top right / contain no-repeat;
}
.timeLimitPage .img_activity {
  margin-bottom: 0.2rem;
  height: 5.17333333rem;
  overflow: hidden;
}
.timeLimitPage .img_activity img {
  display: block;
  width: auto;
  height: 100%;
}
.timeLimitPage .count_time {
  text-align: center;
}
.timeLimitPage .count_time img {
  display: inline-block;
  vertical-align: middle;
  width: 0.64rem;
  height: 0.74666667rem;
  margin-top: -0.03rem;
  margin-right: 0.13333333rem;
}
.timeLimitPage .count_time .clock {
  display: inline-block;
  vertical-align: middle;
  font-size: 0.32rem;
  color: #fe6029;
}
.getTicketSection {
  width: 100%;
  padding: 0.4rem 0 0.4rem 0.2933rem;
  padding: 0.2667rem 0;
  background: #fff;
}
.getTicketSection .swiper-slide {
  width: 6.26666667rem;
}
.getTicketSection .ticketItem {
  position: relative;
  display: block;
  width: 5.86666667rem;
  height: 1.6rem;
  color: #fff;
  padding: 0.2667rem 0.16rem;
  font-size: 0.26666667rem;
  line-height: 0.4rem;
  background: url(../img/bg_quan.png) center center no-repeat;
  background-size: contain;
}
.getTicketSection .ticketItem p {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 75%;
}
.getTicketSection .ticketItem p.amount {
  position: absolute;
  right: 0;
  top: 0.2rem;
  font-size: 0.58666667rem;
  width: 1.46666667rem;
  height: 1.06666667rem;
  line-height: 0.8rem;
  text-align: center;
  font-weight: 800;
}
.getTicketSection .ticketItem .icon_obtained {
  position: absolute;
  right: 1.73333333rem;
  top: 0.2rem;
  display: block;
  width: 0.77333333rem;
  height: 0.72rem;
  background: url(../img/yilingqu.png) center center no-repeat;
  background-size: contain;
}
.getTicketSection .ticketItem div {
  position: absolute;
  right: 0;
  bottom: 0.16rem;
  width: 1.53333333rem;
  font-size: 0.32rem;
  text-align: center;
}
.getTicketSection .ticketItem div span {
  display: inline-block;
  color: #ed3c8b;
  background: #fff;
  height: 0.4rem;
  line-height: 0.4rem;
  padding: 0 0.1333rem;
  border-radius: 0.4rem;
}
.getTicketSection .ticketItem.end {
  background: url(../img/bg_quan_over.png) center center no-repeat;
  background-size: contain;
}
.getTicketSection .ticketItem.end .icon_obtained {
  background: url(../img/yilingwan.png) center center no-repeat;
  background-size: contain;
}
.getTicketSection .ticketItem.end div span {
  color: #909090;
}
.layui-layer-order-changed {
  border: 1px solid #369ff2;
}
.layui-layer-order-changed .layui-layer-btn {
  text-align: center;
  padding-bottom: 20px;
}
.layui-layer-order-changed .layui-layer-btn .layui-layer-btn0 {
  height: 32px;
  line-height: 32px;
  border-radius: 20px;
  padding: 0 24px;
}
.order_goods_has_changed {
  margin: -0.4rem -0.4rem -1rem;
}
.order_goods_has_changed .header {
  text-align: center;
}
.order_goods_has_changed .header p {
  text-align: center;
  color: #999;
  margin: 0;
  line-height: 0.75rem;
}
.order_goods_has_changed .header p .OLD {
  color: #4993fa;
}
.order_goods_has_changed .header p .NEW {
  color: #f0423f;
}
.order_goods_has_changed .header p:last-child {
  margin-top: 0.4rem;
  color: #4993fa;
}
.order_goods_has_changed .list {
  padding: 0 0.266666rem;
  margin-top: 0.533333rem;
  max-height: 10rem;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}
.order_goods_has_changed .list .item {
  text-align: center;
  margin-bottom: 0.24rem;
  overflow: hidden;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;
}
.order_goods_has_changed .list img {
  display: block;
  width: 1.6rem;
  height: 1.6rem;
  border: 1px solid #e8e8e8;
  float: left;
}
.order_goods_has_changed .list .title {
  font-size: 0.346666rem;
  color: #333;
  line-height: 0.4rem;
  max-height: 1.6rem;
  text-align: left;
  overflow: hidden;
  padding: 0 0.24rem;
}
.concel_reason_list {
  text-align: left;
}
.concel_reason_list .header {
  margin: 0.266666rem;
  background: #f3faff;
  color: #369ff2;
  padding: 0.133333rem 0.266666rem;
  border: 0.066666rem;
  font-size: 0.32rem;
  line-height: 0.4rem;
}
.weixinCoupon_cancel_reason_list.concel_reason_list .tip{
    font-size: 0.32rem;
    padding:0 0.266666rem ;
    color: #ea3a52;
    margin: 0.8rem 0 0.32rem;
    opacity: 0.8;
}
.weixinCoupon_cancel_reason_list.concel_reason_list .tip img{
    width: 0.42rem;
    margin-right: 0.12rem;
}
.weixinCoupon_cancel_reason_list.concel_reason_list .header{
    margin-top: 0;
}
.concel_reason_list .header + p {
  font-size: 80%;
  border-bottom: 1px solid #f4f5f9;
  padding: 0 0.32rem;
}
.concel_reason_list .reason_title {
  line-height: 0.8rem;
  color: #ff6c00;
  padding: 0 0.266666rem;
}
.concel_reason_list .item {
  position: relative;
  display: block;
  padding-left: 0.266666rem;
  line-height: 0.933333rem;
}
.concel_reason_list .item .radio_section {
  float: right;
  margin-right: 0.266666rem;
  margin-top: 0.16rem;
}
.concel_reason_list .item + .item {
  border-top: 1px solid #f4f5f9;
}
.layui-m-layer-pop-radio-list.layui-m-layerchild .layui-m-layercont {
  padding: 0;
}
.fly_order_info_section.train .section_2 .icon_add {
  top: 0.22rem;
}
.fly_order_info_section.train .agreenment_section .fli_checkbox_blue {
  margin-top: 0;
}
.fly_order_info_section header {
  line-height: 0.933333rem;
  color: #333;
  font-size: 0.373333rem;
  padding: 0 0.4rem;
  border-top: 1px solid #e1e1e1;
  border-bottom: 1px solid #e1e1e1;
  background: #fff;
  margin-top: 0.133333rem;
}
.fly_order_info_section .infoheader {
  margin-top: 1.2rem;
}
.fly_order_info_section .preson_info {
  padding: 0.2rem 0.4rem;
  border-top: 1px solid #e1e1e1;
  border-bottom: 1px solid #e1e1e1;
  background: #fff;
}
.fly_order_info_section .preson_info .item {
  padding: 0.1rem 0;
  line-height: 0.56rem;
}
.fly_order_info_section .preson_info .item label {
  display: block;
  width: 1.6rem;
  float: left;
  font-size: 0.36rem;
  color: #333;
}
.fly_order_info_section .preson_info .item .values {
  font-size: 0.32rem;
  margin-left: 1.6rem;
  overflow: hidden;
}
.fly_order_info_section .preson_info .item .values span {
  display: block;
  float: left;
  min-width: 1.6rem;
  margin-right: 0.266666rem;
}
.fly_order_info_section .preson_info .item + .item {
  border-top: 1px solid #e1e1e1;
}
.fly_order_info_section .orderPersoninfo {
  margin-top: 1.22rem;
}
.fly_order_info_section .plane_change_header {
  margin-top: 1.22rem;
}
.fly_order_info_section header .fli_checkbox_blue,
.fly_order_info_section .preson_list .fli_checkbox_blue {
  display: inline-block;
  vertical-align: middle;
  margin-right: 0.133333rem;
  margin-top: -0.053333rem;
}
.fly_order_info_section .preson_list {
  background: #fff;
}
.fly_order_info_section .preson_list .item {
  padding: 0.24rem 0.4rem;
  line-height: 0.56rem;
}
.fly_order_info_section .preson_list .item > label {
  display: block;
  width: 1.9rem;
  float: left;
  font-size: 0.36rem;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 0.1rem;
}
.fly_order_info_section .preson_list .item .values {
  font-size: 0.32rem;
  margin-left: 2rem;
  overflow: hidden;
}
.fly_order_info_section .preson_list .item + .item {
  border-top: 1px solid #e1e1e1;
}
.fly_order_info_section .preson_list.baoxian .item > label {
  width: 3rem;
}
.fly_order_info_section .rules {
  line-height: 0.88rem;
  padding: 0 0.4rem;
  font-size: 0.32rem;
  color: #999;
  background: #fff;
  border-top: 1px solid #e1e1e1;
  border-bottom: 1px solid #e1e1e1;
  margin-bottom: 0.133333rem;
}
.fly_order_info_section .form_section {
  background: #fff;
}
.text_normal_tips {
  background: #fff;
  padding: 0.4rem;
  font-size: 0.32rem;
  line-height: 0.48rem;
  margin: 0.133333rem 0;
}
.layer_content_box {
  padding: 0.4rem;
  line-height: 0.6rem;
  font-size: 0.32rem;
}
.ticket_exchange {
  position: relative;
  display: block;
  width: 9.2rem;
  height: 1.7067rem;
  margin: 0.4rem auto;
  padding: 0.32rem 0.7733rem;
  background: url(../img/activity_template/duihuanStyle_mobile_1.png) center center / contain no-repeat;
}
.ticket_exchange p {
  color: #32af2a;
  font-size: 0.32rem;
  line-height: 0.4rem;
  font-weight: bolder;
}
.ticket_exchange p.ticket_big_text {
  font-size: 0.42rem;
  line-height: 0.6667rem;
}
.ticket_exchange p.ticket_amount {
  font-size: 0.56rem;
  line-height: 0.6667rem;
  margin-left: -0.1333rem;
}
.ticket_exchange .btn_text {
  position: absolute;
  right: 1rem;
  bottom: 0.26rem;
  display: block;
  width: 1.8rem;
  font-size: 0.24rem;
  color: #fff;
  text-align: center;
}
.ticket_exchange:before {
  content: " ";
  display: block;
  position: absolute;
  right: 42%;
  top: 20%;
  width: 0.9467rem;
  height: 0.7733rem;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  z-index: 99;
}
.ticket_exchange.active:before {
  background-image: url(../img/activity_template/geted.png);
}
.ticket_exchange.over:before {
  background-image: url(../img/activity_template/over.png);
}
.ticket_exchange.converted:before {
  background-image: url(../img/activity_template/converted.png);
}
.self_support_index_page .logo_and_search {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: #fff;
  padding: 0.1867rem 0.3333rem;
  z-index: 1000;
}
.self_support_index_page .logo_and_search .logo {
  display: block;
  width:1.733rem;
  /*float: left;*/
  /*overflow: hidden;*/
}
.self_support_index_page .logo_and_search .logo img {
  display: block;
  height: 0.8rem;
  width: auto;
  margin: 0 auto;
}
.self_support_index_page .logo_and_search .search_section {
  position: relative;
  /*width: 7.2rem;*/
  height: 0.8rem;
  background: #f4f4f4;
  margin: 0 0.3rem;
  /*border-radius: 0.4rem;*/
  border-radius: 0.2133rem;
  /*float: left;*/
}
.xingyun-index-page.overseasOutsourcingPage  .self_support_index_page .logo_and_search .search_section{
  margin-right: 0;
}
.self_support_index_page .logo_and_search .cart_customer{
  width: 1.56rem;
  height: 0.8rem;
  position: relative;

}
.cart_customer .cart_num{
 left: 0rem;
}
.cart_customer .icon_tool_kefu{
  position: absolute;
  right: 0rem;
  width: 0.64rem;
  height: 0.5866667rem;
  background: url(../img/jd/kefu_icon.png) center center no-repeat;
  background-size: contain;
}
.modify .logo_and_search .cart_customer .cart_nums{
  display: block;
  width: 0.88rem;
  /*height: 0.88rem;*/
  position: relative;
}
.modify .logo_and_search .cart_customer .num_circle{
  display: block;
  /*width: 0.88rem;*/
  /*height: 0.88rem;*/
  position: absolute;
  line-height: 0.46rem;
  right: -0.08rem;
  top: 0.008rem;
}
.modify .logo_and_search .cart_customer .icon_carts{
  width: 0.66666667rem;
  height: 0.66666667rem;
  background: url(../img/jd/gouwche_icon.png) center center no-repeat;
  background-size: contain;
}
.modify .product_classification_list ul li .product_describe p:first-child{
    height: 1.2rem;
}
.self_support_index_page .logo_and_search .search_section .input {
  display: block;
  width: 80%;
  line-height: 0.6rem;
  font-size: 0.32rem;
  color: #999;
  padding: 0.1rem 0 0.1rem 0.2rem;
  background: transparent;
  color: #333;
}
.self_support_index_page .logo_and_search .search_section .icon_search {
  position: absolute;
  right: 0.24rem;
  top: 0.1333rem;
  width: 0.5467rem;
  height: 0.5333rem;
  background: url(../img/self/icon_search.png) center center / contain no-repeat;
}
.self_support_index_page .logo_and_search .search_section .icon_clear {
  position: absolute;
  right: 1rem;
  top: 0.14rem;
}
.section_common_card{
  background: #ffffff;
  border-radius: 0.1333rem;
  margin: 0 0.3333rem ;
}
.collect_coupons{
  padding: 0 0.426667rem  0.346667rem ;
}
.collect_coupons .title{
  font-size: 0.4266667rem;
  color: #f84f3f;
  font-weight: bolder;
}
.collect_coupons a.tomorepage{
  color: rgb(153,153,153);
  font-size: 0.3733333rem;
}
.collect_coupons .coupons_list{
  width: 100%;
  height: 2.6666667rem;
  background:url("../img/jd/sy_hongbaobeijing.png") center center no-repeat ;
  background-size: cover;
  padding: 0 0.16rem;
}
.collect_coupons .coupons_list .coupons_item{
  display: block;
  width: 1.3rem;
  height: 1.7066667rem;
  position: relative;
  background: url("../img/jd/sy_hongbao.png") center center no-repeat;
  background-size: cover;
  margin-right: 0.16rem;
}
.collect_coupons .coupons_list .more_coupons{
  display: block;
  width: 1.3rem;
  height: 1.7066667rem;
  border-radius: 0.13333333333rem;
  background: url("../img/jd/sy_lingqugengduo.png") center center no-repeat;

}
.collect_coupons .coupons_list .coupons_item .type{
  top: 0;
  position: absolute;
  font-size: 0.1866667rem;
  color: rgb(159,111,64);
  display: block;
  width: 100%;
  text-align: center;
}
.collect_coupons .coupons_list .coupons_item .money{
  position: absolute;
  font-size: 0.24rem;
  color: rgb(248,79,63);
  top: 0.4266667rem;
  display: block;
  width: 100%;
  text-align: center;
  font-weight: bolder;
}
.collect_coupons .coupons_list .coupons_item .money span{
  font-size: 0.346667rem;
}
.collect_coupons .coupons_list .coupons_item .activity{
position: absolute;
  font-size: 0.24rem;
  color: #ffffff;
  bottom: 0.4266667rem;
  display: block;
  width: 100%;
  text-align: center;
}
.promotion {
  padding: 0 0.426667rem  0.346667rem ;
  margin-bottom: 0.3333rem;
}

.promotion header,.collect_coupons header{
  padding: 0.4266667rem 0;
}

.promotion .title {
  font-size: 0.4266667rem;
  color: rgb(51,51,51);
  font-weight: bolder;
  /*position: relative;*/
}
.promotion a.tomorepage{
  color: rgb(153,153,153);
  font-size: 0.3733333rem;
}

.promotion .promotion_content a{
  display: block;
  width: 32%;
  height: auto;
  border-radius: 0.1333rem;
  margin-top: 0.2133333rem;
}
.promotion .promotion_content a:first-child{
  width: 100%;
  height: auto;
  border-radius: 0.1333rem;
  margin-top: 0;
}
.promotion .promotion_content img{
  width: 100%;
  height: auto;
  border-radius: 0.1333rem;
  margin-top: 0;
}
.self_support_index_page .broadcast{
  height: 0.573333333rem;
  margin: 0.1866667rem 0.3333rem 0.1867rem;
  background-color: #4c6991;
  color: rgb(253,254,254);
  padding-left: 0.4rem;
  position: relative;
  border-radius:0.285rem;
}
.self_support_index_page .broadcast img {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0.08rem;
}
.self_support_index_page .broadcast marquee {
  height: 100%;
  line-height: 0.573333333rem;
}
.self_support_index_page .nav_list {
  position: relative;
  margin: -0.4rem 0.3333rem 0.2667rem;
  background: #fff;
  border-radius: 0.1333rem;
  padding: 0.15rem 0.2667rem 0.4rem;
  overflow: hidden;
  z-index: 999;
}
.self_support_index_page .nav_list.four a {
  width: 25%;
}
.self_support_index_page .nav_list a {
  display: block;
  width: 20%;
  margin-top: 0.26rem;
  float: left;
}
.self_support_index_page .nav_list a .img {
  width: 1.32rem;
  height: 1.32rem;
  margin: 0 auto;
}
.self_support_index_page .nav_list a .img img {
  display: block;
  width: 100%;
  height: 100%;
}
.self_support_index_page .nav_list a p {
  font-size: 0.2933rem;
  color: #333;
  text-align: center;
  padding: 0.1333rem;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.self_support_index_page .banner_section .swiper-pagination-bullet {
  width: 0.16rem;
  height: 0.16rem;
}
.self_support_index_page .banner_section .swiper-container-horizontal > .swiper-pagination {
  text-align: center;
  bottom: 1.5rem;
}
.self_support_index_page .banner_section .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 0.08rem;
}
.self_support_index_page .banner_section .swiper-pagination-bullet-active {
  background: #fff;
}
.self_support_index_page a.to_cate_page {
  display: block;
  position: absolute;
  right: 0;
  top: 0.06rem;
  width: 2.6667rem;
  height: 1.0667rem;
  font-size: 0.32rem;
  color: #fff;
  padding-right: 0.8rem;
  padding-top: 0.42rem;
  line-height: 0.4rem;
  text-align: right;
}
.self_support_index_page a.to_cate_page:after {
  content: "";
  display: inline-block;
  height: 0.26rem;
  width: 0.26rem;
  border-width: 0.04rem 0.04rem 0 0;
  border-color: #fff;
  border-style: solid;
  -webkit-transform: matrix(0.71, 0.71, -0.71, 0.71, -2, 0);
  transform: matrix(0.71, 0.71, -0.71, 0.71, -4, 0);
  position: relative;
  top: -0.04rem;
  position: absolute;
  right: 0.4rem;
  top: 0.46rem;
}
.self_support_index_page .new_arrivals_section {
  position: relative;
  border-radius: 0.1333rem;
  background: #fff;
  margin: 0 0.3333rem 0.2667rem;
}
.self_support_index_page .new_arrivals_section header {
  padding: 0.3733rem 0.3733rem 0;
}
.self_support_index_page .new_arrivals_section header .title {
  position: relative;
  font-size: 0.3467rem;
  font-weight: bolder;
  color: #333;
  line-height: 0.5333rem;
  padding-left: 0.24rem;
}
.self_support_index_page .new_arrivals_section header .title:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0.0667rem;
  width: 0.1333rem;
  height: 0.3733rem;
  background: #fcb341;
  border-radius: 0.08rem;
}
.self_support_index_page .new_arrivals_section header a.to_cate_page {
  font-size: 0.32rem;
  color: #999;
}
.self_support_index_page .new_arrivals_section header a.to_cate_page:after {
  border-color: #999;
}
.self_support_index_page .link_more {
  display: block;
  width: 9.36rem;
  height: 1.8267rem;
  margin: 0.3333rem auto;
}
.self_support_index_page .link_more img {
  display: block;
  width: 100%;
  height: auto;
}
.self_support_index_page .images_recommend_section {
  margin: 0.32rem;
  overflow: hidden;
}
.self_support_index_page .images_recommend_section a {
  display: block;
  float: left;
}
.self_support_index_page .images_recommend_section a img {
  display: block;
}
.self_support_index_page .images_recommend_section a:first-child {
  margin-right: 0.1067rem;
}
.self_support_index_page .images_recommend_section a:first-child img {
  width: 4.6133rem;
  height: 5.1733rem;
}
.self_support_index_page .images_recommend_section a:first-child + a {
  margin-bottom: 0.11rem;
}
.self_support_index_page .images_recommend_section a:first-child + a img {
  width: 4.64rem;
  height: 2.5333rem;
}
.self_support_index_page .images_recommend_section a:first-child + a + a img {
  width: 4.64rem;
  height: 2.5333rem;
}
.self_support_index_page .images_recommend_section2 {
  margin: 0.32rem;
  overflow: hidden;
  background: #fff;
  border-radius: 0.1333rem;
  padding: 0.16rem;
}
.self_support_index_page .images_recommend_section2 a {
  display: block;
  float: left;
  margin: 0.08rem;
}
.self_support_index_page .images_recommend_section2 a img {
  display: block;
  width: 2.12rem;
  height: 2.4267rem;
}
.self_support_index_page .images_recommend_section2 a:first-child,
.self_support_index_page .images_recommend_section2 a:nth-child(4) {
  margin-right: 0.1067rem;
}
.self_support_index_page .images_recommend_section2 a:first-child img,
.self_support_index_page .images_recommend_section2 a:nth-child(4) img {
  width: 4.28rem;
  height: 2.4267rem;
}
.self_index_goods_list {
  position: relative;
  display: block;
  width: 9.36rem;
  height: 10.36rem;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  margin: 0 auto 0.4rem;
  padding-top: 1.12rem;
  overflow: hidden;
}
.overseasOutsourcingPage .self_index_goods_list{
  height: 10.76rem;
}
.overseasOutsourcingPage .self_index_goods_list h2{
  top: 0.2rem;
}
.overseasOutsourcingPage .self_support_index_page a.to_cate_page{
  top: 0.2rem;
}
.self_index_goods_list h2 {
  display: block;
  position: absolute;
  left: 0.2rem;
  top: 0;
  width: 2.4rem;
  height: 0.96rem;
  line-height: 0.96rem;
  font-size: 0.3467rem;
  color: #fff;
  text-align: center;
}
.myOrderPage section.exchange_goods_info {
  position: relative;
  padding: 0.3733rem 0.32rem 0;
  background: #fbd4d7;
  margin-bottom: 0.8rem;
}
.order-convert-page .myOrderPage section.exchange_goods_info{
  background: initial;
  margin-bottom: 0;
}
.order-convert-page .myOrderPage section.exchange_goods_info:after{
  display: none;
}
.myOrderPage section.exchange_goods_info:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: -0.6rem;
  width: 100%;
  height: 1.7rem;
  background: url(../img/activity_template/table_bg.png) center bottom no-repeat;
  background-size: contain;
  z-index: -1;
}
.myOrderPage section.exchange_goods_info .inner {
  position: relative;
  padding: 0.48rem 0.2933rem 0.8rem;
  background: #fff4f5;
  overflow: hidden;
}
.myOrderPage section.exchange_goods_info .inner + .inner {
  margin-top: 0.1333rem;
}
.myOrderPage section.exchange_goods_info .img_box {
  display: block;
  width: 2.3333rem;
  height: 2.3333rem;
  overflow: hidden;
  float: left;
}
.myOrderPage section.exchange_goods_info .img_box img {
  display: block;
  width: 100%;
  height: 100%;
}
.myOrderPage section.exchange_goods_info .goods_info {
  position: relative;
  margin-left: 2.56rem;
}
.myOrderPage section.exchange_goods_info .goods_info p.title {
  font-size: 0.3467rem;
  color: #333;
  font-weight: bolder;
  max-height: 1.2267rem;
  line-height: 0.6133rem;
  overflow: hidden;
}
.myOrderPage section.exchange_goods_info .goods_info .text_ticket {
  position: relative;
  display: inline-block;
  font-size: 0.24rem;
  color: #fff;
  height: 0.4533rem;
  line-height: 0.4533rem;
  padding: 0 0.2133rem;
  margin: 0.1333rem 0;
  background: #ff6c6c;
}
.myOrderPage section.exchange_goods_info .goods_info .text_ticket:before,
.myOrderPage section.exchange_goods_info .goods_info .text_ticket:after {
  content: " ";
  position: absolute;
  top: 50%;
  margin-top: -0.0933rem;
  display: block;
  width: 0.1867rem;
  height: 0.1867rem;
  border-radius: 100%;
  background: #f9f6e4;
}
.myOrderPage section.exchange_goods_info .goods_info .text_ticket:before {
  left: -0.0933rem;
}
.myOrderPage section.exchange_goods_info .goods_info .text_ticket:after {
  right: -0.0933rem;
}
.myOrderPage section.exchange_goods_info .goods_info .text_gray {
  font-size: 0.2667rem;
  color: #666;
}
.myOrderPage section.exchange_goods_info .goods_info p {
  font-size: 0.32rem;
}
.myOrderPage section.exchange_goods_info .goods_info p.num {
  color: #999;
}
.myOrderPage section.exchange_goods_info .goods_info p.num span {
  font-size: 0.4rem;
}
.myOrderPage section.exchange_goods_info .goods_info p.attr {
  color: #999;
}
.myOrderPage section.exchange_goods_info .goods_info p.attr span {
  margin-right: 0.24rem;
}
.myOrderPage section.exchange_goods_info .goods_info p.outofservice {
  color: #ff6c6c;
}
.myOrderPage section.exchange_goods_info .goods_info a {
  position: relative;
  display: inline-block;
  padding: 0 0.2667rem;
  background: #ff6c6c;
  line-height: 0.4533rem;
  border-radius: 0.4533rem;
  font-size: 0.32rem;
  color: #fff;
  text-align: center;
}
.myOrderPage section.exchange_goods_info .tag_exchange {
  position: absolute;
  right: 0.32rem;
  bottom: 0.4rem;
}
.myOrderPage section.exchange_goods_info .tag_exchange .icon_exchange {
  display: inline-block;
  vertical-align: middle;
  width: 0.6rem;
  height: 0.6rem;
  background: url(../img/icon_exchange.png) center center / contain no-repeat;
}
.myOrderPage section.exchange_goods_info .tag_exchange .text {
  font-size: 0.32rem;
  color: #369ff2;
}
.sxfContact {
  position: fixed;
  right: 0.2rem;
  bottom: 30%;
  display: block;
  width: 1.5733rem;
  height: 1.5733rem;
  background: url(../img/sxf_consult.png) center center no-repeat;
  background-size: contain;
  z-index: 999;
}
.multiTransport .list {
  padding: 0.2933rem 0.32rem;
}
.multiTransport .list .item {
  margin-bottom: 0.2rem;
  background: #fff;
  border-radius: 0.2rem;
  padding: 0.2667rem;
}
.multiTransport .list .item .header {
  position: relative;
  display: block;
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 0.32rem;
  padding-right: 0.32rem;
}
.multiTransport .list .item .header .no {
  font-size: 0.2933rem;
  color: #666;
  float: right;
  line-height: 0.72rem;
}
.multiTransport .list .item .header span {
  display: inline-block;
  font-size: 0.3733rem;
  color: #fff;
  background: #5fb2f3;
  border-radius: 0.08rem;
  width: 1.28rem;
  text-align: center;
  padding: 0.0533rem 0;
}
.multiTransport .list .item .header:after {
  content: " ";
  display: inline-block;
  height: 0.26rem;
  width: 0.26rem;
  border-width: 0.04rem 0.04rem 0 0;
  border-color: #d8d8d8;
  border-style: solid;
  -webkit-transform: matrix(0.71, 0.71, -0.71, 0.71, -2, 0);
  transform: matrix(0.71, 0.71, -0.71, 0.71, -4, 0);
  position: relative;
  top: -0.04rem;
  position: absolute;
  right: 0;
  top: 0.2rem;
}
.multiTransport .list .item .content {
  padding-top: 0.32rem;
  overflow: hidden;
}
.multiTransport .list .item .content .swiper-container {
  width: 80%;
  float: left;
  height: 2.4rem;
}
.multiTransport .list .item .content .swiper-container a {
  display: block;
  margin: 0 auto;
}
.multiTransport .list .item .content .swiper-container a .img {
  display: block;
  width: 1.2rem;
  height: 1.2rem;
  margin: 0 auto 0.2667rem;
}
.multiTransport .list .item .content .swiper-container a .img img {
  display: block;
  width: 1.2rem;
  height: 1.2rem;
  margin: 0 auto;
}
.multiTransport .list .item .content .swiper-container a p {
  font-size: 0.2667rem;
  color: #666;
  line-height: 0.32rem;
  height: 0.6rem;
  overflow: hidden;
  padding: 0 0.12rem;
}
.multiTransport .list .item .content .swiper-container .swiper-pagination {
  text-align: left;
  bottom: -0.1rem;
}
.multiTransport .list .item .content .swiper-container .swiper-pagination .swiper-pagination-bullet {
  width: 0.16rem;
  height: 0.16rem;
  margin: 0 0.08rem;
}
.multiTransport .list .item .content .swiper-container .swiper-pagination .swiper-pagination-bullet:only-child {
  display: none;
}
.multiTransport .list .item .content .total {
  line-height: 2.4rem;
  text-align: center;
  color: #666;
  font-size: 0.32rem;
  width: 20%;
  float: left;
}
.infoTuanSection {
  background: #fff;
  padding-bottom: 0.32rem;
}
.infoTuanSection .tuanHeader {
  font-size: 0.4rem;
  line-height: 0.5333rem;
  padding: 0.32rem;
}
.infoTuanSection .tuanHeader span {
  color: #f96472;
}
.infoTuanSection .tuanHeader .right {
  float: right;
}
.infoTuanSection .imgs {
  text-align: center;
  padding: 0 0.4267rem;
}
.infoTuanSection .imgs .img {
  display: inline-block;
  vertical-align: middle;
  width: 1rem;
  height: 1rem;
  overflow: hidden;
  border-radius: 100%;
  margin: 0.1rem;
}
.infoTuanSection .imgs .img img {
  display: block;
  width: 100%;
  height: 100%;
}
.infoTuanSection .imgs a {
  color: #f96472;
  font-size: 0.4rem;
}
.infoTuanSection .imgs.suc {
  text-align: left;
}
.infoTuanSection .imgs.suc .img + .img {
  margin-left: -0.5rem;
}
.fliSkinToDark .tab_switch_section article ul li:hover {
  box-shadow: 0 0 0.2rem rgba(77, 169, 144, 0.1);
}
.fliSkinToDark .goods_list_section ul li:hover {
  box-shadow: 0 0 0.2rem rgba(77, 169, 144, 0.1);
}
.fliSkinToDark .CartPage .sale_header a {
  color: #4da990;
}
.fliSkinToDark .CartPage .cartFooter .sale_header a {
  color: #4da990;
}
.fliSkinToDark .CartPage .cartFooter .FreightBox .img {
  background-image: url(../img/colorfulSkin/dark/angle-down.png);
}
.fliSkinToDark .CartPage .cartFooter .FreightBox span {
  color: #4da990;
}
.fliSkinToDark .CartPage .cartFooter .right .btn_submit {
  background: #4da990;
}
.fliSkinToDark .CartPage .cartFooter .right .btn_del.btn_theme {
  background: #4da990;
}
.fliSkinToDark .FreightMask .content .lists .item .txtright {
  color: #4da990;
}
.fliSkinToDark .myOrderPage > section > header span:first-child {
  border-left-color: #4da990;
}
.fliSkinToDark .myOrderPage .base_info .identify .input {
  color: #4da990;
}
.fliSkinToDark .myOrderPage .message_section > span span {
  color: #4da990;
}
.fliSkinToDark .myOrderPage .amount_section .FreightTxt {
  color: #4da990;
}
.fliSkinToDark .myOrderPage .amount_section .FreightTxt .img {
  background-image: url(../img/colorfulSkin/dark/angle-down-old.png);
}
.fliSkinToDark .myOrderPage .freightMask .Content .lists .item .txtright {
  color: #4da990;
}
.fliSkinToDark .myOrderPage .link_btn_section a {
  background-color: #4da990;
}
.fliSkinToDark .myOrderPage > section.card_info .list .item .table_section table tr td .text {
  color: #4da990;
}
.fliSkinToDark .myOrderPage > section.card_info .list .item .table_section table tr td .text.disabled {
  color: #999;
}
.fliSkinToDark ul.classify-list-section li.active > a {
  background: #4da990;
}
.fliSkinToDark .addr_select li.active {
  color: #4da990;
  background-image: url(../img/colorfulSkin/dark/icon_selected_big.png);
}
.fliSkinToDark ul.order_list_section li .save span {
  color: #4da990;
}
.fliSkinToDark .order_items_section ul li header .icon_del:after {
  content: url(../img/colorfulSkin/dark/icon_delete_active.png);
}
.fliSkinToDark .order_items_section ul li header .icon_del:active {
  background: url(../img/colorfulSkin/dark/icon_delete_active.png) center center / contain no-repeat;
}
.fliSkinToDark .order_items_section ul li footer .total_price {
  color: #4da990;
}
.fliSkinToDark .order_items_section ul li footer .btns .btn_theme {
  color: #4da990;
}
.fliSkinToDark .order_items_section ul li footer .btns .btn_theme:active {
  color: rgba(77, 169, 144, 0.6);
}
.fliSkinToDark .weekly_welfare_section .header {
  color: #4da990;
}
.fliSkinToDark .weekly_welfare_section .swiper-container.weekly_scroll .time.active {
  border-bottom-color: #4da990;
}
.fliSkinToDark .weekly_welfare_section .swiper-container.weekly_scroll .time.active p {
  color: #4da990;
}
.fliSkinToDark .sortSection li.active a {
  border-bottom-color: #4da990;
}
.fliSkinToDark .sortSection li.active.top a i {
  background-image: url(../img/colorfulSkin/dark/icon_sort_top.png);
}
.fliSkinToDark .sortSection li.active.down a i {
  background-image: url(../img/colorfulSkin/dark/icon_sort_down.png);
}
.fliSkinToDark .search_condition_filter_section .address_selection .content {
  color: #4da990;
  background-image: url(../img/colorfulSkin/dark/icon_location_theme.png);
}
.fliSkinToDark .paymentPage .order_created {
  padding: 0.8rem 0;
}
.fliSkinToDark .paymentPage .order_created img {
  display: none;
}
.fliSkinToDark .paymentPage .order_created .ybtAmount {
  display: block;
}
.fliSkinToDark .paymentPage .order_created > p {
  display: none;
}
.fliSkinToDark .paymentPage .order_created div.time_left {
  display: block;
  text-align: center;
  margin: 0.24rem 0 0.8rem;
}
.fliSkinToDark .paymentPage .order_created div.time_left b {
  font-size: 0.42rem;
  color: #4da990;
}
.fliSkinToDark .paymentPage .order_created div.time_left .orderCancelSpan {
  color: #ff0030;
}
.fliSkinToDark .paymentPage .order_created .order_info {
  border-top: 1px solid #3C4451;
  padding-top: 0.8rem;
}
.fliSkinToDark .paymentPage .select_payment_way .coin_pay_section .item a .right {
  color: #4da990;
}
.fliSkinToDark .paymentPage .select_payment_way .coin_pay_section .item a .right .radio_section input[type="checkbox"]:checked + label,
.fliSkinToDark .paymentPage .select_payment_way .coin_pay_section .item a .right .radio_section input[type="radio"]:checked + label {
  background-color: #4da990;
}
.fliSkinToDark .paymentPage .order_created > p b {
  color: #4da990;
}
.fliSkinToDark .paymentPage .weixin_pay_section header {
  color: #4da990;
}
.fliSkinToDark .paymentPage .weixin_pay_section .item .weixinpay button {
  background-color: #4da990;
}
.fliSkinToDark .paymentPage .weixin_pay_section .item .alipay button {
  background-color: #4da990;
}
.fliSkinToDark .userCenterPage .coin_section .coin_list .item span b {
  color: #4da990;
}
.fliSkinToDark .userCenterPage .list li a:hover .text {
  color: #4da990;
}
.fliSkinToDark .myeweima .codebox .showcode .down_btn_a {
  background-color: #4da990;
}
.fliSkinToDark .addr_list li .addr_opration .right a span.icon_del:after {
  content: url(../img/colorfulSkin/dark/icon_delete_active.png);
}
.fliSkinToDark .addr_list li .addr_opration .right a span.icon_edit:after {
  content: url(../img/colorfulSkin/dark/icon_edit_active.png);
}
.fliSkinToDark .addr_list li .addr_opration .right a:active {
  color: #4da990;
}
.fliSkinToDark .addr_list li .addr_opration .right a:active span.icon_del {
  background-image: url(../img/colorfulSkin/dark/icon_delete_active.png);
}
.fliSkinToDark .addr_list li .addr_opration .right a:active span.icon_edit {
  background: url(../img/colorfulSkin/dark/icon_edit_active.png) center center / contain no-repeat;
}
.fliSkinToDark .serviceForm .service_section > .item .input_section .item_radio_btn.active {
  border-color: #4da990;
  color: #4da990;
}
.fliSkinToDark .concel_reason_list .header {
  color: #4da990;
}
.fliSkinToDark .myOrderPage section.exchange_goods_info .tag_exchange .text {
  color: #4da990;
}
.fliSkinToDark .myOrderPage > section > header span:first-child {
  border-left-color: #4da990;
}
.fliSkinToDark .multiTransport .swiper-pagination-bullet-active {
  background: #4da990;
}
.fliSkinToDark .multiTransport .list .item .header span {
  background: rgba(77, 169, 144, 0.6);
}
.afterSaleDetail {
  padding-bottom: 0.2667rem;
}
.afterSaleDetail .btn_simple {
  display: inline-block;
  font-size: 0.32rem;
  color: #4993fa;
  padding: 0.12rem 0.24rem;
  border-radius: 0.0533rem;
  background-color: transparent;
  border: 1px solid #4993fa;
}
.afterSaleDetail .btn_simple.sm {
  font-size: 0.28rem;
  padding: 0.06rem 0.12rem;
}
.afterSaleDetail .top_section {
  position: relative;
  padding: 0.24rem;
  margin: 0.24rem;
  border-radius: 0.16rem;
  padding: 0.32rem;
  background-color: #fff;
}
.afterSaleDetail .top_section .red {
  color: #f00;
  float: right;
  font-size: 0.32rem;
  line-height: 0.8rem;
}
.afterSaleDetail .top_section .btns {
  text-align: right;
  padding: 0.16rem 0;
}
.afterSaleDetail .top_section p {
  font-size: 0.4rem;
  line-height: 0.8rem;
}
.afterSaleDetail .infoBox {
  background: #fff;
  margin: 0.24rem;
  border-radius: 0.16rem;
  padding: 0.32rem;
  font-size: 0.32rem;
  color: #999;
  line-height: 0.6rem;
}
.afterSaleDetail .infoBox p:first-child {
  font-size: 0.34rem;
  color: #333;
}
.afterSaleDetail .detailCompTable {
  margin: 0.24rem;
  padding: 0.24rem;
  background: #fff;
  border-radius: 0.16rem;
}
.afterSaleDetail .detailCompTable table {
  border-collapse: collapse;
  width: 100%;
  background-color: #fff;
  border-radius: 0.16rem;
}
.afterSaleDetail .detailCompTable table tr th,
.afterSaleDetail .detailCompTable table tr td {
  border: 1px solid #f2f2f2;
  font-size: 0.34rem;
  padding: 0.16rem 0.08rem;
  line-height: 0.5333rem;
}
.afterSaleDetail .detailCompTable table tr th {
  width: 25%;
}
.afterSaleDetail .detailCompTable table tr td {
  width: 75%;
}
.afterSaleDetail .detailCompTable table tr td .line_status {
  padding: 0.12rem 0.3rem;
  line-height: 0.6rem;
}
.afterSaleDetail .detailCompTable table tr td .line_status.paddingL{
  position: relative;
  padding-left: 0.6rem;
}
.afterSaleDetail .detailCompTable table tr td .line_status.paddingL .btn_simple{
  position: absolute;
  right: 0.2667rem;
  top: 0.16rem;
}
.afterSaleDetail .detailCompTable table tr td .line_status p + p {
  color: #f00;
}
.afterSaleDetail .detailCompTable table tr td .line_address {
  position: relative;
  background: url(../img/icon_location.png) 0.12rem center no-repeat;
  background-size: 0.32rem 0.4267rem;
  padding: 0.2667rem 0 0.2667rem 0.6rem;
}
.afterSaleDetail .detailCompTable table tr td .line_address.modifypadding{
  padding-top: 0.12rem;
}
.afterSaleDetail .detailCompTable table tr td .line_address p {
  line-height: 0.48rem;
}
.afterSaleDetail .detailCompTable table tr td .line_address p + p {
  margin-top: 0.16rem;
}
.afterSaleDetail .detailCompTable table tr td .line_address .btn_simple {
  position: absolute;
  right: 0.2667rem;
  top: 0.16rem;
}
.afterSaleDetail .detailCompTable table tr td .line_info {
  position: relative;
  color: #999;
  font-size: 0.28rem;
  padding-left: 0.4rem;
}
.afterSaleDetail .detailCompTable table tr td .line_info:before {
  content: " ";
  position: absolute;
  left: 0.16rem;
  top: 0.18rem;
  width: 0.12rem;
  height: 0.12rem;
  background-color: #999;
  border-radius: 100%;
}
.afterSaleDetail .detailTable {
  margin: 0.24rem;
  padding: 0.24rem;
  background: #fff;
  border-radius: 0.16rem;
}
.afterSaleDetail .detailTable.return_trans_info table caption {
  background: url(../img/icon_arrow_down.png) 96% center no-repeat #f2f2f2;
  background-size: 0.4267rem 0.24rem;
}
.afterSaleDetail .detailTable.return_trans_info table tr {
  display: none;
}
.afterSaleDetail .detailTable.return_trans_info.show table caption {
  background-image: url(../img/icon_arrow_up.png);
}
.afterSaleDetail .detailTable.return_trans_info.show table tr {
  display: table-row;
}
.afterSaleDetail .detailTable table {
  border-collapse: collapse;
  width: 100%;
  background-color: #fff;
  border-radius: 0.16rem;
}
.afterSaleDetail .detailTable table caption {
  background: #f2f2f2;
  padding: 0.32rem;
  line-height: 0.4rem;
  text-align: left;
  font-size: 0.4rem;
}
.afterSaleDetail .detailTable table tr th,
.afterSaleDetail .detailTable table tr td {
  border: 1px solid #f2f2f2;
  font-size: 0.34rem;
  padding: 0.16rem 0.08rem;
  line-height: 0.5333rem;
}
.afterSaleDetail .detailTable table tr th {
  width: 25%;
}
.afterSaleDetail .detailTable table tr td {
  width: 75%;
  white-space: normal;
  word-break: break-all;
}
.afterSaleDetail .detailTable table .product .img {
  width: 1.6rem;
  height: 1.6rem;
  overflow: hidden;
  float: left;
}
.afterSaleDetail .detailTable table .product .img img {
  display: block;
  width: 100%;
  height: 100%;
}
.afterSaleDetail .detailTable table .product p {
  font-size: 0.4rem;
  line-height: 0.5333rem;
  max-height: 1.6rem;
  overflow: hidden;
  margin-left: 1.8rem;
}
.search-section-list{
  display: block;
  position: relative;
  top: -1rem;
  height: 0.88rem;
  margin-left: 1.4rem;
  margin-right: 1.4rem;
  -webkit-border-radius: 0.08rem;
  -moz-border-radius: 0.08rem;
  -ms-border-radius: 0.08rem;
  -o-border-radius: 0.08rem;
  border-radius: 0.08rem;
  overflow: hidden;
  background: #fff;
}
.search-section-list .input {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: block;
  width: 100%;
  font-size: 0.34rem;
  line-height: 0.61333333rem;
  padding: 0.133333rem 0.2667rem 0.133333rem 0.88rem;
  background: transparent;
  color: #666;
}
.search-list-text{
  position: absolute;
  right: 0.13333333rem;
  top: 0.19rem;
  height: 0.88rem;
  width: 1.17333333rem;
  -webkit-border-radius: 0.08rem;
  -moz-border-radius: 0.08rem;
  -ms-border-radius: 0.08rem;
  -o-border-radius: 0.08rem;
  border-radius: 0.08rem;
  overflow: hidden;
  background: #4993fa;
  color: #fff;
  z-index: 10000;
  font-size: 0.32rem;
  border: 1px solid #fff;
}
.search-list-button{
  position: absolute;
  right: 0.2rem;
  top: 0.1rem;
  display: block;
  width: 1rem;
  height: 1rem;
  background: url(../img/jd/icon_top_search_d.png) center center no-repeat;
  background-size: 0.4267rem 0.44rem;
  border: none;
}
.search-section-list .icon_search {
  position: absolute;
  left: 0;
  top: 0;
  display: block;
  width: 0.88rem;
  height: 0.88rem;
  background: url(../img/jd/icon_top_search_d.png) center center no-repeat;
  background-size: 0.4267rem 0.44rem;
  border: none;
}
.search-section-list .icon_clear {
  display: none;
  position: absolute;
  width: 0.5rem;
  height: 0.5rem;
  background: url(../img/icon_close_circle.png) center center / contain no-repeat;
  z-index: 999;
  top: 0.21333333rem;
  right: 0.2rem;
}
.group_buying{
  padding: 0.1866667rem 0;
  margin-bottom: 0.3333rem;
}
.group_buying .title_tips {
 height: 0.72rem;
  padding: 0 0.3733333rem;

}
.group_buying .title_tips .title_logo{
  display: block;
  height: 0.66rem;
  width: 2rem;
  background: url(../img/cIndex/cashGoHeader.png) center no-repeat;
  background-size: 2.0133rem 0.4rem;
}
.selfShopNew2Page .group_buying .title_tips .title_logo{
  height: 0.528rem;
  width: 2.25rem;
  background-size: 2.25rem 0.528rem;
}
.group_buying .title_tips .limit{
  display: block;
  width: 0.92rem;
  height: 0.4267rem;
  line-height: 0.4267rem;
  background-color: #333333;
  font-size: 0.3rem;
  color: #fff;
  text-align: center;
  border-radius: 0.4267rem;
  margin: 0 0.1466667rem 0 0.266666667rem;
}
.group_buying .title_tips .goods_timer{
  font-size: 0.32rem;
  color: #f71f02;
  font-weight: 600;
}
.group_buying .title_tips .title_right{
  font-size: 0.3733333rem;
  color: rgb(153,153,153);;
  font-weight: normal;
}
.group_buying .group_buying_swiper .swiper-wrapper dl{
  display: block;
 width: 10rem;
  height: 1.2rem;
}
fill{
  display: none;
}
.product_classification_header{
  margin-bottom: 0.3333rem;
  height: 1.12rem;
  padding: 0.0533333rem 0.426667rem 0;
}
#product_classification_nav ,#group_buying_list {
  width: 100%;
  overflow: hidden;
}
#group_buying_list .swiper-slide{
  width:2.4rem;
  height: 3.4666667rem;
}
#group_buying_list .swiper-slide .product_img{
  width:2.4rem;
  height: 2.32rem;
}
#group_buying_list .swiper-slide .product_img img{
  width:1.3333333333rem;
  height: 1.3333333333rem;
}
#group_buying_list .swiper-slide .product_describe{
   width:2.4rem;
   height: 1.1466667rem;

 }
#group_buying_list .swiper-slide .product_describe p{
  font-size: 0.32rem;
  width: 100%;
  text-align: center;
  height: 0.453333rem;
  line-height: 0.453333rem;
}
#group_buying_list .swiper-slide .product_describe p:first-child{
  color: #333333;
  font-weight: bolder;
}
#group_buying_list .swiper-slide .product_describe p:last-child{
  color: #b0b9c6;
}
#product_classification_nav .swiper-slide {
  padding: 0 0.06666667rem;
  letter-spacing:0.02666667rem;
  width:1.84rem;
  text-align:center;
}
#product_classification_nav .swiper-slide span{
  height: 1.06666667rem;
  line-height: 1.06666667rem;
  transition:all .3s ease;
  display:block;
  font-size: 0.3733333rem;
  color: #666666;
  font-family: PingFang-SC-Mediu
}
#product_classification_nav .active span{
  color: #416efb;
  border-bottom: 0.05333333rem solid #416efb;
}
#product_classification_nav .active {
font-weight: bolder;
  /*background: url("../img/jd/fillet.png") center 0.6333333rem no-repeat ;*/
  color: #416efb;
}

.product_classification_list{
  background: #f2f2f2;
}
.product_classification_list .nav{
  width: 100%;
  height: 1.12rem;
  background: #ffffff;
  border-radius: 0.13333rem;
}
.product_classification_list .title{
  display: block;
  left: 0.2rem;
  top: 0;
  width: 2.4rem;
  height: 0.96rem;
  line-height: 0.96rem;
  font-size: 0.3467rem;
  color: #666666;
  text-align: center;
}
.product_classification_list .to_more{
  display: block;
  right: 0;
  top: 0.06rem;
  width: 2.6667rem;
  height: 1.0667rem;
  font-size: 0.32rem;
  color: #333;
  padding-right: 0.8rem;
  padding-top: 0.42rem;
  line-height: 0.4rem;
  text-align: right;
}
.product_classification_list .active .title{
  color: #1875f9;
}
.product_classification_list ul{
  width: 100%;
}

.product_classification_list ul li{
  border-radius: 0.0666667rem;
  width: 48%;
  height: 5.95333333rem;
  background: #ffffff;
  margin-bottom: 0.16rem;
}
.product_classification_list ul li .product_img{
  height: 3.8933333rem;
  position: relative;
}
.product_classification_list ul li .product_img img{
  display: block;
  width: 3.2133333rem;
  height: 3.2133333rem;
}
.product_classification_list ul li .product_describe{
  height: 1.76rem;
  padding: 0 0.32rem;

}
.product_classification_list ul li .product_describe p:first-child{
  height: 1.2rem;
  line-height: 0.4rem;
  font-size: 0.32rem;
  /*text-overflow: -o-ellipsis-lastline;*/
  overflow: hidden;
  /*text-overflow: ellipsis;*/
  /*display: -webkit-box;*/
  /*-webkit-line-clamp: 2;*/
  /*line-clamp: 2;*/
  /*-webkit-box-orient: vertical;*/
  /*margin-bottom: 0.2666667rem;*/
}
.product_classification_list ul li .product_describe p:last-child span{
  color: #f86242;
  font-size: 0.32rem;
}
.product_classification_list ul li .product_describe p:last-child .add_cart{
  display: block;
  width: 0.5466667rem;
  background: url("../img/jd/icon_top_cart_d.png") center center no-repeat;
  background-size: contain;
}
.fixedtop{
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100000;
}
.freshShopPage {
  background: url("../img/freshShop/bg.png") top center no-repeat;
  background-size: 100%  5.52rem;
}
.freshShopPage .leftArrow{
  width: 0.24rem;
  height: 0.4rem;
  margin-right: 0.16rem;
}

.freshShopPage .self_support_index_page .logo_and_search .logo img{
    width: 2rem;
    height: 0.48rem;
    display: block;
}
.freshShopPage .self_support_index_page .logo_and_search .logo{
    width: 2rem;
}
.freshShopPage .self_support_index_page .nav_list{
  margin-top: -0.55rem;
}
.freshShopPage .self_support_index_page .logo_and_search {
  background: transparent;
}
.freshShopPage .self_support_index_page .banner_section .swiper-container img{
  display: block;
  width: 9.3867rem;
  height: 4.28rem;
  margin: 0 auto;
  border-radius: 0.16rem;
}
.freshShopPage .advertisement{
  margin: 0  0.3333rem;
  /*position: relative;*/
}
.freshShopPage .advertisement a:first-child{
    width: 100%;
    height: 2.32rem;
    display: block;
    background-position: center center!important;
    background-size: cover!important;
    background-repeat: no-repeat!important;
}
.freshShopPage .advertisement .advertisementList{

}
.freshShopPage .advertisement .advertisementList a{
    display: block;
    width: 50%;
    height: 2.7733333rem;
    display: block;
    background-position: center center!important;
    background-size: cover!important;
    background-repeat: no-repeat!important;

}

.freshShopPage #product_classification_nav .swiper-slide{
    width: 2rem;
}
.freshShopPage .fresh_new_arrivals_section{
  margin: 0.15333333rem 0 0.32rem;
}
.freshShopPage .fresh_new_arrivals_section .headerTopTitle{
  width: 100%;
  height: 1.0666667rem;
  line-height: 1.0666667rem;
  background: #ffffff;
  text-align: center;
  color: rgb(102,102,102);
  font-size: 0.37333333rem;
}
.freshShopPage .fresh_new_arrivals_section .headerTopTitle .noteBold{
  font-size: 0.42666667rem;
  font-weight: bold;
  color: rgb(51,51,51);
}
.freshShopPage .fresh_new_arrivals_section .fli_product_list{
  background: #ffffff;
  margin-top: 0.133333333rem;
}
.freshShopPage  .product_classification_header{
  height: 1.62666667rem;
  background: #fff;
  margin-bottom: 0.32rem;
}
.freshShopPage  .product_classification_header .freshTypeName{
  color: rgb(51,51,51);
  font-size: 0.37333333rem;
  font-weight: bold;
}
.freshShopPage  .product_classification_header .active .freshTypeName{
  color: rgb(22,85,244);
}
.freshShopPage  .product_classification_header .active  .origin{
  color: #ffffff;
  background: rgb(22,85,244);
}
.freshShopPage  .product_classification_header  .origin{
  color: rgb(102,102,102);
  padding: 0 0.16rem;
  font-size: 0.29333333rem;
  display: inline-block;
  height: 0.42666667rem;
  line-height: 0.42666667rem;
  border-radius: 0.21333333rem;
}
.freshShopPage .fresh_new_arrivals_section #group_buying_list{
    background: #ffffff;
    margin-top: 0.05333333rem;
}
.freshShopPage .fresh_new_arrivals_section #group_buying_list .fli_product_list li{
    width: 30%;
}
.freshShopPage .fresh_new_arrivals_section #group_buying_list .fli_product_list{
    overflow: initial;
}
.freshShopPage .self_support_index_page .logo_and_search .search_section .icon_search{
    background: url("../img/freshShop/searchicon1.png") center center / contain no-repeat;
}
.freshShopPage .product_classification_list ul li .product_describe p:last-child .add_cart{
    background: rgb(22,85,244);
    width: 0.53333333rem;
    height: 0.53333333rem;
    border-radius: 50%;
    text-align: center;
    position: relative;
}
.freshShopPage .product_classification_list ul li .product_describe p:first-child{
    height: 1.2rem;
}
.freshShopPage #group_buying_list .swiper-slide{
    height: 100%;
}
.freshShopPage .product_classification_list ul li .product_describe p:last-child .add_cart img{
    position: absolute;
    display: block;
    width:0.26666667rem;
    height: 0.26666667rem;
    top:0.133333334rem ;
    left: 0.133333334rem;
}
.freshShopPage .logo_and_search .cart_customer .cart_nums{
  display: block;
  width: 0.88rem;
  position: relative;
}
.freshShopPage .self_support_index_page .logo_and_search .cart_customer .icon_carts{
  width: 0.66666667rem;
  height: 0.66666667rem;
  background: url("../img/freshShop/totalgwc.png") center center no-repeat;
  background-size: contain;
}
.freshShopPage .self_support_index_page .logo_and_search .cart_customer{
  width: auto;
}
.freshShopPage .self_support_index_page .logo_and_search .cart_customer .num_circle{
  position: absolute;
  right: -0.08rem;
  top: 0.008rem;
}
.modifyMyOrderPage .all_text_copy{
  color: #369ff2;
}
.modifyMyOrderPage .all_text_copy.disabled{
  color: #999;
}
.modifyMyOrderPage .transfer_information,.modifyMyOrderPage .electronicCardEmpty{
  background: #ffffff;
  padding: 0.4133rem 0.4rem;
  font-size: 0.34rem;
  color: rgb(51,51,51);
}
.modifyMyOrderPage .transfer_information .title,.modifyMyOrderPage .electronicCardEmpty .title{
  font-size: 0.42rem;
}
.modifyMyOrderPage .electronicCardEmpty .emContent{
  margin-top: 0.1867rem;
  background: rgb(242,242,242);
  border-radius: 0.16rem;
  height: 2.24rem;
  color: rgb(153,153,153);
}
.modifyMyOrderPage .transfer_information .item{
  height: 0.90667rem;
}
.modifyMyOrderPage .transfer_information .item img.acquirerImg{
  width: 0.4rem;
  height: 0.4533rem;
  margin-right: 0.1067rem;
}
.modifyMyOrderPage .rotateB{
  transform: rotate(90deg);
}
.modifyMyOrderPage .transfer_information .labelText .des{
  display: inline-block;
  width: 1.5rem;
  text-align: justify;
  text-align-last: justify;
  text-align-all: justify;
  margin-right: 0.05rem;
  white-space: normal;
  height: 0.8267rem;
  line-height: 0.8267rem;
  overflow: hidden;
}
.modifyMyOrderPage .transfer_information .labelText .des span{
  display: inline-block;
  width: 100%;
}
.modifyMyOrderPage .transfer_information .note{
  color: rgb(251,123,36);
}
.fli_pub_pop_form.pay-info-tips{
  font-size: 0.3467rem;
  margin-bottom: -1rem;
  padding-bottom: 0;
  margin-top: -0.9rem;
}
.fli_pub_pop_form.pay-info-tips .notes{
  color: #999999;
  text-align: left;
  margin-bottom: 0.16rem;
}
.fli_pub_pop_form.pay-info-tips  #phoneNum{
  width: 100%;
  height: 0.88rem;
  line-height: 0.88rem;
  text-align: center;
  font-size: 0.68rem;
  font-weight: bold;
  margin-bottom: 0.12rem;
}
.order_list_returns_page .gearDate{
  z-index: 999999999;
}
.order_list_returns_page .fli_pub_pop_form .item .select_chose{
  text-align:left;
  color: #444444;
}
.order_list_returns_page .fli_pub_pop_form .item .input.text_left{
  text-align: left;
}
.status-tip{
  font-size: 0.32rem;
  color: #fd7945;
}
.coupon_list_new_page .ticket_list  li{
  background-color: #ffe7e7;
  border-radius: 0.32rem;
  position: relative;
  overflow: hidden;
  height: 2.8rem;
  padding-left: 0.26666667rem;
  background-image: none;
}
.coupon_list_new_page .tab_theme_section .item.active a{
  border-bottom:none;
  font-weight: bold;
  color: #333;
  font-size: 0.42rem;
}
.coupon_list_new_page .ticket_list .available .text{
  padding-right: 0.32rem;
}
.coupon_list_new_page .ticket_list .unavailable .text{
  padding-right: 0.32rem;
}
.coupon_list_new_page .ticket_list .available .text  .fli_checkbox_blue input[type="checkbox"]:checked + label{
  background-color: #eb143a;
}
.coupon_list_new_page .ticket_list .available .text .fli_checkbox_blue label{
  border-color: #ff9090;;
}
.coupon_list_new_page .ticket_list .fli_checkbox_blue{
  top: -0.4rem;
}
.coupon_list_new_page{
  padding-bottom: 1.6rem;
}
.coupon_list_new_page .btn-option{
  position: fixed;
  /*bottom: 0.42rem;*/
  /*margin: 0 0.32rem;*/
  /*width: calc(100% - 0.64rem);*/
  /*height: 1.2rem;*/
  /*background: #4993fa;*/
  /*border-radius: 0.6rem;*/
  height: 1.56rem;
  bottom: 0;
  padding: 0.32rem;
  width: 100%;
  background: #fff;
  box-shadow: 0 -0.067rem 0.267rem 0.0267rem #f5f5f5;
}
.coupon_list_new_page .btn-option .btn-option-left{
  width: 70%;
  height: 1.2rem;
  line-height: 1.2rem;
  /*background: black;*/
  /*color: #fff;*/
  border-radius: 0.6rem 0 0 0.6rem;
  color: #666;
  font-size: 0.37333rem;
  /*padding-left: 0.42rem;*/
}
.coupon_list_new_page .btn-option .btn-option-right{
 width: 2.667rem;
 height: 0.9067rem;
 line-height: 0.9067rem;
  text-align: center;
  /*color: #000;*/
  color: #fff;
  background: #eb143a;
  border-radius: 0.16rem;
}
.coupon_list_new_page .ticket_list ul li.expired:before,
.coupon_list_new_page .ticket_list ul li.noEffect:before{
  content: '';
  width: 0.42rem;
  height: 0.42rem;
  border-radius: 50%;
  position: absolute;
  left: -0.16rem;
  top: 50%;
  margin-top: -0.16rem;
  background-color: #ffffff;
  background-image: none;
}
.coupon_list_new_page .ticket_list  .unavailable  li{
  background-color: #ffe7e7;
}
.coupon_list_new_page .ticket_list ul.unavailable li{
 /*background-image: url("./../img/bg_ticket.png");*/
  opacity: 0.5;
  background-image: none;
}
.coupon_list_new_page .ticket_list  li:before{
  content: '';
  width: 0.42rem;
  height: 0.42rem;
  border-radius: 50%;
  position: absolute;
  left: -0.16rem;
  top: 50%;
  margin-top: -0.16rem;
  background-color: #ffffff;
}
.coupon_list_new_page{
  background: #ffffff;
}
.coupon_list_new_page .ticket_list  li:after{
  content: '';
  width: 0.42rem;
  height: 0.42rem;
  border-radius: 50%;
  position: absolute;
  right: -0.16rem;
  top: 50%;
  margin-top: -0.16rem;
  background-color: #ffffff;
}
.coupon_list_new_page .ticket_list  p.num{
  color: #eb143a;
  min-width: 1.867rem;
  font-size: 0.52rem;
  font-weight: 800;
  max-width: 2.6rem;
}
.coupon_list_new_page .ticket_list  p.num .discountNum{
  padding-left: 0.16rem;
}
.coupon_list_new_page .ticket_list  p.num .discountNum span{
  font-size: 0.32rem;
  position: relative;
  top: -0.04rem;
  left: 0.14rem;
}
.coupon_list_new_page .ticket_list  p.num i{
  font-weight: normal;
  font-style: normal;
  font-size: 0.52rem;
  top: -0.02rem;
  position: relative;
}
.coupon_list_new_page .ticket_list  p.num span{
  margin-left: -0.09rem;
  vertical-align: inherit;
  font-size: 0.8rem;
}
.coupon_list_new_page .ticket_list  p.num.exchange-num span{
  font-size: 0.68rem;
}
.coupon_list_new_page .ticket_list  .content-right p:first-of-type{
  font-family: SourceHanSansSC-Regular;
  font-size: 0.4rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #eb143a;
  width: 100%;
  max-width: 100%;
}
.coupon_list_new_page .ticket_list  .content-right p:last-of-type{
  font-family: SourceHanSansSC-Regular;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.0167rem;
  color: #eb143a;
  margin-top: 0.06rem;
  width: 100%;
  max-width: 100%;
}
.coupon_list_new_page .ticket_list  .category-of-use{
  display: inline-block;
  font-family: SourceHanSansSC-Regular;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #eb143a;
  height: 0.48rem;
  background-color: #fcc8c8;
  border-radius: 0.2267rem;
  line-height: 0.48rem;
  padding: 0 0.24rem;
  margin-top: 0.24rem;
}
.coupon_list_new_page .ticket_list  .btn{
  width: 1.31rem;
  height: 0.7067rem;
  text-align: center;
  line-height: 0.7067rem;
  border: 1px solid #eb143a;
  border-radius: 0.16rem;
  top: 50%;
  margin-top: -0.3533rem;
  right: 0.2667rem;
  z-index: 11;
  background: #ffffff;
  color: #eb143a;
  font-size: 0.32rem;
  position: absolute;
}
.selfShopNew2Page .swiper-container#swiperbg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5.5rem;
  z-index: 0;
}
.selfShopNew2Page .swiper-container#swiperbg .swiper-slide {
  width: 100%;
  height: 100%;
  background-position: center top;
  background-repeat: no-repeat;
  background-size: 100% 4.5733rem;
}
.selfShopNew2Page #swiperbg .swiper-slide img.itemImg{
  width: 100%;
  height: auto;
  min-height:  5.6rem!important;
  -webkit-filter:blur(25px);
  -moz-filter:blur(25px);
  -ms-filter: blur(25px);
  -o-filter:blur(25px);
  filter: blur(25px);
  -webkit-transform: scale(1.5);
  -moz-transform: scale(1.5);
  -ms-transform: scale(1.5);
  -o-transform: scale(1.5);
  transform: scale(1.5);
}
.selfShopNew2Page #swiperbg .swiper-slide .zhezhao{
  bottom: -0.5rem;
  left: 0;
  width: 100%;
  height:2.5rem;
  position: absolute;
}
.selfShopNew2Page .self_support_index_page .logo_and_search{
  background: transparent;
}
.selfShopNew2Page .banner_section{
  height: 3.8rem;
  padding-top: 0.12rem;
  box-sizing: border-box;
}
.selfShopNew2Page .banner_section .swiper-container{
  height: 100%;
}
.selfShopNew2Page .banner_section .swiper-container a.swiper-slide img{
  display: block;
  width: 9.3867rem!important;
  height:3.44rem!important;
  margin: 0 auto;
  border-radius: 0.16rem;
}
.selfShopNew2Page .banner_section  .swiper-pagination-bullet{
  width: 0.4rem!important;
  height: 0.1067rem!important;
  background-color: #fafafa;
  border-radius: 0;
  opacity: 1;
}
.selfShopNew2Page .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #160838;
}
.selfShopNew2Page .shop_nav_section ul.shop_nav_section_ul {
  padding-top: .42rem;
  padding-bottom: .22rem;
  overflow-x: scroll;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch
}

.selfShopNew2Page .shop_nav_section ul.shop_nav_section_ul::-webkit-scrollbar {
  display: none;
  height: 0;
}

.selfShopNew2Page .shop_nav_section ul li {
  width: 20%;
  display: inline-block;
  float: inherit;
  padding-bottom: 0;
}

.selfShopNew2Page .shop_nav_section {
  position: relative;
  padding-bottom: .12rem;
  background: #fff;
  border-radius: .13rem;
  margin: 0 .32rem .32rem;
  width: calc(100% - 0.64rem);
}

.selfShopNew2Page .shop_nav_section .scroll-bar-lists {
  position: absolute;
  left: 50%;
  bottom: .18rem;
  transform: translateX(-50%);
  height: .06rem;
  border-radius: .12rem;
}

.selfShopNew2Page .shop_nav_section .scroll-bar-lists p.item {
  display: inline-block;
  background: #9c9c9c;
  width: .16rem;
  height: .06rem;
}

.selfShopNew2Page .shop_nav_section .scroll-bar-lists p.bar {
  display: inline-block;
  width: .64rem;
  background: #1875f9;
  height: .06rem;
  position: absolute;
  left: 0;
  bottom: 0;
}

.selfShopNew2Page .shop_nav_section ul li a .img {
  width: 1.32rem;
  height: 1.32rem;
  margin: 0 auto;
}

.selfShopNew2Page .shop_nav_section ul li a .img img {
  display: block;
  width: 100%;
  height: 100%;
}

.selfShopNew2Page .shop_nav_section ul li a .text {
  font-size: .32rem;
  height: .73333333rem;
  line-height: .6rem;
  text-align: center;
  color: #444;
}
.selfShopNew2Page .self_support_index_page .link_more{
  height: auto;
}
.selfShopNew2Page .product_classification_list ul li .product_img img{

}
.selfShopNew2Page .product_classification_list ul li .product_img{
  height:3.8933333rem;
  position: relative;
  /*margin-bottom: 0.32rem;*/
}
.selfShopNew2Page  .product_classification_list .waterfall-flow-content  ul{
  width: 50%;
}
.selfShopNew2Page  .product_classification_list .waterfall-flow-content  ul:first-of-type{
  padding-right: 0.1rem;
}
.selfShopNew2Page  .product_classification_list .waterfall-flow-content  ul:last-of-type{
  padding-left: 0.1rem;
}
.selfShopNew2Page .self_support_index_page .banner_section .swiper-container-horizontal > .swiper-pagination{
 bottom: 0.6rem;
}
.selfShopNew2Page .cart_customer .icon_tool_kefu{
  background-image: url("./../img/jd/kefu_icon_w.png");
}
.modify.selfShopNew2Page .logo_and_search .cart_customer .icon_carts{
  background-image: url("./../img/jd/gouwche_icon_w.png");
}
.selfShopNew2Page .goods_list_section ul li{
  padding: 0;
  /*height: auto;*/
  /*float: inherit;*/
  /*width: 100%;*/
  /*margin-left: 0!important;*/
  /*margin-right: 0!important;*/
  /*margin-bottom: 0.2rem;*/
  border-radius: 0.16rem;
}
.selfShopNew2Page .product_classification_list ul li .product_describe p:first-child{
  font-family: SourceHanSansSC-Medium;
  /* font-size: 0.3733rem; */
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.16rem;
  word-break: break-all;
}
.selfShopNew2Page .product_classification_list ul li .product_describe{
  height: auto;
  padding-bottom: 0.12rem;
}
.selfShopNew2Page .cashGoSection {
  background: #fff;
  overflow: hidden;
  border-radius: 0.2133rem;
  margin-bottom: 0.16rem;
  padding: 0.32rem;
  background-image: linear-gradient(0deg, #ffe9d9 0%, #fff4ec 0%, #ffffff 0%, #fffaf6 0%, #fff5ed 0%, #ffe9d9 100%), linear-gradient( #ffe9d9, #ffe9d9);
  background-blend-mode: normal, normal;
}
.selfShopNew2Page .cashGoSection .old_price p{
    text-align: center;
    width: 100%;
    display: inherit;
    font-size: .32rem;
    color: #999;
    text-decoration: line-through;
    line-height: 0.46rem;
}
.selfShopNew2Page .cashGoSection .more.old_price_more{
    height: 2.801rem;
    padding: 0.5rem 0.3rem;
    margin: 0.2rem auto;
}
.selfShopNew2Page .cashGoSection .old_price{
    padding: 0.32rem 0 0;
}
.selfShopNew2Page .cashGoSection .old_price p.price{
    font-size: .3733rem;
    color: #333;
    margin-top: 0;
    font-weight: 800;
    text-decoration: inherit;
}
.selfShopNew2Page  .cashGoSection #group_buying_list .des{
  font-size: 0.32rem;
  margin-top: 0.2rem;
  padding: 0 0.2133rem;
}
.selfShopNew2Page .cashGoSection .des .productName{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #222222;
  line-height: 0.4rem;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  display: block;
}
.selfShopNew2Page .cashGoSection .des .original-price{
  padding: 0.06rem 0 0;
}
.selfShopNew2Page .cashGoSection .des .price{
  height: 0.62rem;
}
.selfShopNew2Page  .cashGoSection .des .original-price span:first-of-type{
  color: #ff2c25;
}
.selfShopNew2Page  .cashGoSection .des .original-price span:last-of-type{
  transform: scale(0.8);
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #ff6825;
}
.selfShopNew2Page  .cashGoSection .item .des p.price{
  margin-top: 0;
  font-size: 0.32rem;
}
.selfShopNew2Page   .cashGoSection .des .price span.price-num{
  display: inline-block;
  height: 0.85rem;
  background-image: linear-gradient(90deg, #fff5ed 0%, #ffefe3 0%, #ffe9d9 0%, #ff8b7f 0%, #ff2c25 0%, #ff8052 100%), linear-gradient( #ff2c25, #ff2c25);
  background-blend-mode: normal, normal;
  border-radius: 0.4267rem;
  line-height: 0.85rem;
  padding: 0 0.32rem;
  color: #fff;
  font-weight: normal;
  font-size: 0.46rem;
  transform: translateX(-25%) scale(0.5);
  min-width: 2.2rem;
  text-align: center;
}
.selfShopNew2Page   .cashGoSection .des .price span.price-num span{
  font-size: 0.6733rem;
}
.selfShopNew2Page   .cashGoSection .des .price span.spell-num{
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.0167rem;
  color: #999999;
  transform: scale(0.8);
  text-align: right;
  position: absolute;
  right: 0;
}
.selfShopNew2Page  .van-grid.cateSection .van-grid-item__content .icon_box .van-image__img{
  width: auto;
}
.selfShopNew2Page .cashGoSection .header {
  display: flex;
  margin: 0.12rem 0 0.32rem;
  height: 0.4rem;
  position: initial;
  background: transparent;
  background-size: 0 0;
  overflow: hidden;
}
.selfShopNew2Page  .cashGoSection .header img.cashGoSectionImg{
  height: 0.4267rem;
}
.selfShopNew2Page  .cashGoSection .header .countDownSection {
  position: initial;
  left: initial;
  top: initial;
}
.selfShopNew2Page  .cashGoSection .header .right {
  font-size: 0.2933rem;
  color: #999;
  font-weight: normal;
  float: right;
  padding: 0.2rem 0.2rem 0 0;
}
.selfShopNew2Page  .cashGoSection #group_buying_list .swiper-slide {
  width: 3.2rem;
  background: #fff;
  margin-right: 0.16rem;
  border-radius:0.1333rem;
}
.selfShopNew2Page  .cashGoSection.oldCashGoSection #group_buying_list .swiper-slide{
    width: 2rem;
    margin-right: 0;
    border-radius: 0;
    padding: 0.4rem 0;
}
.selfShopNew2Page .cashGoSection.oldCashGoSection #group_buying_list .swiper-slide .img-box{
    width: 1.8rem;
    height: 1.8rem
}
.selfShopNew2Page .cashGoSection #group_buying_list .swiper-slide {
  position: relative;
  padding: 0;
  height: auto;
}
.selfShopNew2Page .cashGoSection #group_buying_list .swiper-slide  .img-box{
  width: 3.2rem;
  height: 3.2rem;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}
.selfShopNew2Page  .cashGoSection #group_buying_list .swiper-slide  .img-box img{
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
  width: 100%;
  height: auto;
  max-height: 100%;
  border-radius: 0.1333rem 0.1333rem 0 0;
}
.selfShopNew2Page .cashGoSection .item p{
  display: flex;
  line-height: initial;
}
.selfShopNew2Page .cashGoSection .more {
  display: block;
  width: 1rem;
  height: 4.3rem;
  background-color: #f5f5f5;
  color: #666;
  padding:1.07rem 0.3rem;
  text-align: center;
  font-size: 0.2667rem;
  margin: 0.4rem auto;
  line-height: 0.4rem;
}
.selfShopNew2Page .cashGoSection #group_buying_list .swiper-slide:last-child{
  width: 1rem;
}
.selfShopNew2Page .group_buying .title_tips{
  height: auto;
  margin: 0 0 0.2rem;
  padding: 0;
}
.selfShopNew2Page .countDownSection {
  width: auto;
  padding-right: 0.08rem;
  height: 0.4267rem;
}
.selfShopNew2Page .group_buying .title_tips .limit{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0.01rem;
  color: #222222;
  background: transparent;
  width: auto;
  margin: 0 0 0 0.12rem;
}
.selfShopNew2Page .group_buying .title_tips .goods_timer{
  margin-left:0;
}
.selfShopNew2Page .group_buying .title_tips .goods_timer .block-item{
  background: #fff;
  padding: 0.01rem 0.08rem;
  margin: 0 0.08rem;
  border-radius: 0.12rem;
  opacity: 0;
}
.selfShopNew2Page .group_buying .title_tips .goods_timer .colon{
  display: none;
}
.selfShopNew2Page .cashGoSection .des .coinConvertPrice{
  margin-top: 0.12rem;
}
.selfShopNew2Page .product_classification_header{
  margin: 0 0 0.3333rem;;
  padding: 0 0.32rem;
  width: 100%;
  background: #ffffff;
}
.selfShopNew2Page #product_classification_nav .active span {
  color: #1875f9;
  border: none;
}
.order-list-page.orderPage .public_top_header,
.multiCommentPage .public_top_header{
  background: #f2f2f2;
  box-shadow: none;
}
.order-list-page.orderPage .search_section{
  background-color: #f5f5f5;
}
.order-list-page.orderPage .public_top_header .search_section .input,
.multiCommentPage .public_top_header .search_section .input{
  background-color: #f5f5f5;
  border-radius: 0.2133rem;
  border: solid 0.0267rem #1875f9;
}
.order-list-page.orderPage .public_top_header  .return_back:before,
.multiCommentPage .public_top_header  .return_back:before{
  border-color: #000000;
}
.order-list-page.orderPage .tab_theme_section.fixed,
.multiCommentPage .tab_theme_section.fixed{
  background: #f2f2f2;
  border: none;
}
.order-list-page.orderPage .tab_theme_section.fixed:before,
.multiCommentPage  .tab_theme_section.fixed:before{
  display: none;
}
.order-list-page.orderPage .tab_theme_section .item.active a,
.multiCommentPage .tab_theme_section .item.active a{
  border: none;
  font-family: SourceHanSansSC-Bold;
  font-size: 0.4267rem;
  font-weight: bold;
  color: #333333;
}
.order-list-page.orderPage .tab_theme_section .item a,
.multiCommentPage .tab_theme_section .item a{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0;
  color: #666666;

}
.order-list-page.orderPage .order_items_section ul li .item {
  padding: 0 0.32rem;
  margin-bottom: 0.2rem;
}
.order-list-page.orderPage .order_items_section ul li a.item{
  display:-webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}
.order-list-page.orderPage .order_items_section ul li a.item .order-detail{
  -webkit-box-flex: 1;
  -moz-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
.order-list-page.orderPage .order_items_section ul li .item:last-of-type{
  margin-bottom:0;
}
.order-list-page.orderPage .order_items_section ul li .item > div{
  vertical-align: top;
  display: block;
}
.order-list-page.orderPage .order_items_section ul li .item > div.order-detail{
  padding-left: 0.32rem;
  position: relative;
}
.order-list-page.orderPage .order_items_section ul li .item .order-detail .title {
  padding: 0;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
}
.order-list-page.orderPage .order_items_section ul li .item .order-detail .title p{
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
.order-list-page.orderPage .order_items_section ul li .item .img{
  width: 2.03rem;
  height: 2.03rem;
  padding: 0;
  border: 1px solid #f5f5f5;
  border-radius: 0.16rem;
  vertical-align: middle;
}
.order-list-page.orderPage .order_items_section ul li .item .img img{
  border-radius: 0.16rem;
  height: 100%;
  width: auto;
  max-width: 100%;
}
.order-list-page.orderPage .order_items_section ul li{
  margin: 0.2rem 0.32rem 0;
  border-radius: 0.23rem;
  padding-bottom: 0.32rem;
}
.order-list-page.orderPage  .order_items_section ul li header .no{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
  font-weight: bold;
}
.order-list-page.orderPage .order_items_section ul li .item .attrs{
  width: auto;
  text-align: left;
}
.order-list-page.orderPage  .sales-price,
.favorite-list-page .sales-price{
  font-family: SourceHanSansSC-Bold;
  font-size: 0.32rem!important;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0.0133rem;
  color: #ff2c25;
}
.order-list-page.orderPage .sales-price span,
.favorite-list-page .sales-price span{
  font-size: 0.4267rem;
}
.order-list-page.orderPage .order_items_section ul li .item .attrs .quantity{
  font-family: KozGoPr6N-Regular;
  font-size: 0.4267rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.013rem;
  color: #666666;
}
.order-list-page.orderPage .order_items_section ul li .item .title .enterprise_payment{
  font-weight: normal;
}
.order-list-page.orderPage .order_items_section ul li .item  .total_price{
  font-family: SourceHanSansSC-Medium;
  font-size:0.3733rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0.013rem;
  color: #333333;
  position: absolute;
  bottom: 0;
  right: 0.32rem
}
.order-list-page.orderPage .order_items_section ul li .item  .total_price .sales-price{
  color: #333333;
}
.order-list-page.orderPage .order_items_section ul li footer{
  border-top: none;
  height: inherit;
  line-height: inherit;
  margin-top: 0.22rem;
}
.order-list-page.orderPage .order_items_section ul li footer .btns input[type="button"]{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.013rem;
  color: #666666;
  vertical-align:inherit;
  background-color: #ffffff;
  border-radius: 0.36rem;
  /*height: 0.72rem;*/
  line-height: 0.72rem;
  border: solid 0.0267rem #999999;
  padding: 0 0.32rem;
  margin-left: 0.32rem;
}
.order-list-page.orderPage .order_items_section ul li footer .btns input[type="button"]:last-of-type{
  border-color: #1875f9;
  color: #1875f9;
}
.order-list-page.orderPage .order_items_section ul li .item:before{
  border-top: none;
}
.order-list-page.orderPage #pop_cut  .select-reason-lists,
.multiCommentPage #pop_cut  .select-reason-lists,
.product-content-page #pop_cut  .select-reason-lists{
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: auto;
  background: #FFFFFF;
  border-radius: 0.32rem 0.32rem 0 0;
}
.order-list-page.orderPage #pop_cut  .select-reason-lists .item,
.multiCommentPage #pop_cut  .select-reason-lists .item,
.product-content-page #pop_cut  .select-reason-lists .item{
  height: 1.3rem;
  line-height: 1.3rem;
  text-align: center;
  font-size: 0.3733rem;
  border-bottom: 1px solid #f2f2f2;
}
.order-list-page.orderPage #pop_cut  .select-reason-lists .item a,
.multiCommentPage #pop_cut  .select-reason-lists .item a,
.product-content-page #pop_cut  .select-reason-lists .item a{
  color: #444;
}
.order-list-page.orderPage #pop_cut  .select-reason-lists .item.active a,
.multiCommentPage #pop_cut  .select-reason-lists .item.active a,
.product-content-page #pop_cut  .select-reason-lists .item.active a{
  color: #50a5fd;
}
.order-list-page.orderPage #pop_cut  .select-reason-lists .item:last-of-type,
.multiCommentPage #pop_cut  .select-reason-lists .item:last-of-type,
.product-content-page #pop_cut  .select-reason-lists .item{
  border-bottom: none;
}
.product-content-page #pop_cut  .select-reason-lists header{
  height: 1.58rem;
  line-height: 1.58rem;
  text-align: center;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2;
}
.product-content-page #pop_cut .select-reason-lists{
  height: 70%;
}
.product-content-page #pop_cut .select-reason-lists ul{
  max-height: calc(100% - 1.6rem - 1.5rem);
  overflow-y: auto;
}
.product-content-page #pop_cut .select-reason-lists .btn-option{
  width: 100%;
  height: 1.5rem;
  background: #fff;
  border-top: 1px solid #f2f2f2;
}
.product-content-page #pop_cut .select-reason-lists .btn-option .selected-add{
  width: 8.67rem;
  height: 0.9067rem;
  line-height: 0.9067rem;
  background-color: #416efb;
  border-radius: 0.133rem;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.013rem;
  color: #ffffff;
}
.product-content-page #pop_cut .select-reason-lists .item{
  text-align: left;
  padding: 0 0.32rem;
  overflow: hidden;
}
.product-content-page #pop_cut  .select-reason-lists .btn-option{
  position: absolute;
  bottom: 0;
}
.product-content-page #convertCoupon{
  margin: 0.8rem auto 0;
}
.my-order-detail-page{
  margin: 0.16rem 0.32rem 0;
}
.my-order-detail-page .card-rd,
.common-order-info-page .card-rd{
  border-radius: 0.2133rem;
}
.common-order-info-page .margin-side{
  margin: 0.12rem 0.32rem 0;
}
.common-order-info-page .info-footer{
  background: #fff;
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 0.22rem 0.32rem;
  box-sizing: border-box;
}
.common-order-info-page .info-footer .total span{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #ff2c25;
}
.common-order-info-page .info-footer .total .money-total span{
  font-family: SourceHanSansSC-Bold;
  font-size: 0.64rem;
  font-weight: bold;
}
.common-order-info-page .info-footer  .total{
    text-align: center;
}
.common-order-info-page .info-footer  .total p{
    font-family: SourceHanSansSC-Medium;
    font-size: 0.32rem;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #999999;
}
.common-order-info-page .occupant_section.margin-side .lines_Section_all{
  padding: 0.06rem 0.38rem;
  line-height: initial;
}
.common-order-info-page .occupant_section.margin-side .lines_Section_all  .right_text{
  padding-right: 0;
  color: #444;
}
.common-order-info-page .info-footer .btn_submit_long{
  width: 2.667rem;
  height: 0.9067rem;
  line-height: 0.9067rem;
  border-radius: 0.12rem;
  margin: 0;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.013rem;
  color: #ffffff;
}
.common-order-info-page .margin-side label{
  min-width: 1.56rem;
  font-weight: 700;
  font-size: 0.3733rem;
}
.common-order-info-page .margin-side .right_text{
  float: right;
  padding-right: 0.32rem;
  color: #fd1b3b;
}
.common-order-info-page .margin-side .right_text  .like_btn{
  display: inline-block;
  vertical-align: middle;
  height: 0.58666667rem;
  line-height: 0.58666667rem;
  color: #fff;
  background: #fd1b3b;
  padding: 0 0.21333333rem;
  border-radius: 0.08rem;
  font-size: 0.32rem;
  margin-left: 0.26666667rem;
  margin-top: -0.05333333rem;
}
.common-order-info-page .margin-side .item{
  height: 0.78rem;
}
.common-order-info-page .margin-side .item.plane_info_page{
  height: auto;
  min-height: 0.78rem;
}
.common-order-info-page .margin-side  .lines_Section_all{
  padding: 0;
  line-height: 0.78rem;
}
.common-order-info-page .margin-side  .fli_link_line:after{
  top: 0.2rem;
  right: -0.03rem;
}
.base_info{
  margin-bottom: 0.32rem;
}
.myOrderPage.my-order-detail-page .card-rd header{
  background: transparent;
}
.myOrderPage.my-order-detail-page .card-rd header span:first-child{
  font-weight: 700;
  font-size: .3733rem;
  line-height: .78rem;
  border-left: none;
  padding-left: 0;
}
.myOrderPage.my-order-detail-page .orderCreate {
  border-radius: 0.2133rem 0.2133rem  0 0;
}
.myOrderPage.my-order-detail-page .base_info{
  border-radius:0 0 0.2133rem 0.2133rem;
  margin-top: 0;
}
.myOrderPage.my-order-detail-page ul.order_list_section li{
  background: transparent;
}
.myOrderPage.my-order-detail-page ul.order_list_section.small li .img{
  width: 3.01333333rem;
  height: 3.01333333rem;
  margin-right: 0.24rem;
}
.myOrderPage.my-order-detail-page ul.order_list_section.small li .img img{
  width: 100%;
  height: 100%;
}
.myOrderPage.my-order-detail-page ul.order_list_section.small li .info p.title{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
.myOrderPage.my-order-detail-page .message_section .message .value{
  margin-left: 1.1rem;
  color: #666;
  font-size: 0.3733rem;
}
.tab_theme_section .item.evaluation.active a{
  border: none;
  font-family: SourceHanSansSC-Bold;
  font-size: 0.4267rem;
  font-weight: bold;
  color: #333333;
}
.myOrderPage.my-order-detail-page .message_section .message .label{
  font-weight: 700;
  font-size: 0.3733rem;
}
.myOrderPage.my-order-detail-page  .amount_section.has_header .content p{
  font-weight: 700;
  font-size: 0.3733rem;
  line-height: 0.78rem;
}
.myOrderPage.my-order-detail-page .exchange_goods_info.card-rd{
  border-radius: 0.2133rem 0.2133rem 0 0;
}
.myOrderPage.my-order-detail-page section.exchange_goods_info:after{
  bottom: -0.5rem;
}
.myOrderPage.my-order-detail-page .base_info .orderCreate a.no_trans{
  padding: 0 0.4rem;
}
.confirm-order-page .public_top_header,
.my-order-detail-page .public_top_header{
  background: #ffffff;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.48rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing:0.0133rem;
  color: #333333;
}
.confirm-order-page .myOrderPage{
  margin: 0 0.32rem;
}
.confirm-order-page .card-rd{
  border-radius: 0.2133rem;
}
.confirm-order-page  .return_back:before,
.my-order-detail-page .return_back1:before{
  border-color:#000000;
}
.confirm-order-page .addr-content .addr-img,.my-order-detail-page .addr-content .addr-img{
  width: 0.8rem;
  height: 0.8rem;
  margin-right: 0.33rem;
}
.confirm-order-page .addr-content .addr-info .per,
.my-order-detail-page .addr-content .addr-info .per{
  font-family: SourceHanSansSC-Bold;
  font-size: 0.4267rem;
  font-weight: bold;
  letter-spacing: 0px;
  color: #333333;
}
.confirm-order-page .addr-content .addr-info .per span,
.my-order-detail-page .addr-content .addr-info .per span{
  color: #666;
  font-weight: normal;
  /*font-size: 0.3733rem;*/
}
.mianMyOrderPage.confirm-order-page{
  padding-bottom: 1.32rem;
}
.mianMyOrderPage.confirm-order-page .myOrderPage  .base_info a p.addr,
.my-order-detail-page .base_info a p.addr {
  margin-top: 0.24rem;
  color: #666;
  font-size: 0.3733rem;
  word-break: break-all;
}
.mianMyOrderPage.confirm-order-page .myOrderPage .base_info a.fli_link_line:after{
  top: 49%;
}
.mianMyOrderPage.confirm-order-page .myOrderPage .base_info{
  margin-bottom: 0.32rem;
}

.mianMyOrderPage.confirm-order-page .myOrderPage .base_info:after,
.my-order-detail-page .base_info:after{
  content: '';
  width: calc(100% - 0.32rem);
  height: 0.1067rem;
  background-image: url("./../img/line.png");
  display: inline-block;
  position: absolute;
  bottom: -0.1rem;
  background-size: 100% 100%;
  left: 0.12rem;

}
.confirm-order-page.pay-info-page .myOrderPage .favourable_section.new-favourable-section{
  border-radius: 0.16rem;
}
.confirm-order-page.pay-info-page .myOrderPage .favourable_section .lines_Section_all.voucher-nopadding a{
  padding: 0 0.08rem 0 0.4rem;
}
.confirm-order-page.pay-info-page .myOrderPage .favourable_section .lines_Section_all.voucher-nopadding a label{
  font-weight: 700;
  font-size: 0.3733rem;
}
.confirm-order-page.pay-info-page ul.order_list_section li.tr{
  /*background: #ffffff;*/
}
.confirm-order-page ul.order_list_section li {
  background-color: transparent;
}
.confirm-order-page ul.order_list_section li{
  padding-left: 0.32rem;
}
.confirm-order-page ul.order_list_section li .img{
  margin-right: 0.24rem;
}
.confirm-order-page ul.order_list_section li:last-of-type:after{
  /*border-bottom: none;*/
}
.mianMyOrderPage.confirm-order-page .topMiddle{
  position: relative;
  padding-top: 0.32rem;
  font-size: .3733rem;
  min-height: 1rem;
  height: auto;
  margin: 0 0.32rem;
}
.mianMyOrderPage.confirm-order-page .topMiddle label{
  min-width: 1.56rem;
  font-weight: 700;
}
.mianMyOrderPage.confirm-order-page .topMiddle textarea{
  font-size: .32rem;
  border: none;
  min-height: 1.2rem;
  text-align: left;
  resize: none;
}
.mianMyOrderPage.confirm-order-page .topMiddle textarea::placeholder{
  text-align: right;
}
.mianMyOrderPage.confirm-order-page .topMiddle textarea::-webkit-input-placeholder{
  text-align: right;
}
.mianMyOrderPage.confirm-order-page .topMiddle textarea::-moz-placeholder{
  text-align: right;
}
.mianMyOrderPage.confirm-order-page .topMiddle textarea::-ms-input-placeholder{
  text-align: right;
}
.mianMyOrderPage.confirm-order-page .topMiddle .delMemo{
  position: absolute;
  bottom: 0.1rem;
  width: 0.46rem;
  left: 0.1rem;
  display: none;
}
.mianMyOrderPage.confirm-order-page .topMiddle .numBox{
  position: absolute;
  right: 0;
  bottom: 0;
  font-size: .2667rem;
  padding-bottom: 0.09rem;
}
.mianMyOrderPage.confirm-order-page .topMiddle .numBox span{
  font-size: .32rem;
}
.mianMyOrderPage.confirm-order-page  .contentBox{
  padding-bottom: 0.32rem;
}
.mianMyOrderPage.confirm-order-page .amount_section .lines_Section_all{
  padding: 0;
}
.confirm-order-page .amount_section .item{
  height: 0.78rem;
}
.mianMyOrderPage.confirm-order-page .amount_section label,
.pay-info-page.confirm-order-page .amount_section label{
  min-width: 1.56rem;
  font-weight: 700;
  font-size: 0.3733rem;
}
.mianMyOrderPage.confirm-order-page .amount_section .freightMask .Content .lists label{
  font-weight: normal;
  font-size: 0.32rem;
}
.mianMyOrderPage.confirm-order-page .myOrderPage .amount_section p,
.confirm-order-page .myOrderPage .amount_section p{
  display: flex;
}
.mianMyOrderPage.confirm-order-page .amount_section .item.FreightTxt span>label{
  color: #333;
}
.mianMyOrderPage.confirm-order-page .myOrderPage  .favourable_section.amount_section .lines_Section_all .fli_link_line{
  line-height: 0.78rem;
}
.mianMyOrderPage.confirm-order-page .myOrderPage .favourable_section.amount_section .lines_Section_all .fli_link_line.fli_link_line:after{
  top: 0.25rem;
  right: -0.06rem;
}
.confirm-order-page  .info-footer{
  background: #fff;
  position: fixed;
  bottom: 0;
  width: 100%;
  left: 0;
  padding: 0.22rem 0.32rem;
  box-sizing: border-box;
}
.confirm-order-page  .info-footer .btn_submit_long{
  width: 2.667rem;
  height: 0.9067rem;
  line-height: 0.9067rem;
  border-radius: 0.12rem;
  margin: 0;
  font-family: SourceHanSansSC-Medium;
  font-size:0.3733rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.013rem;
  color: #ffffff;
}
.confirm-order-page  .info-footer .total{
  text-align: center;
}
.confirm-order-page  .info-footer .total span{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #ff2c25;
}
.confirm-order-page  .info-footer .total  p{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #999999;
}
.confirm-order-page  .info-footer .total .money-total span{
  font-family: SourceHanSansSC-Bold;
  font-size: 0.64rem;
  font-weight: bold;
}
.mianMyOrderPage.confirm-order-page ul.order_list_section li .info p.title,
.confirm-order-page .myOrderPage.simple ul.order_list_section li .info p.title{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  height: auto;
}
.mianMyOrderPage.confirm-order-page ul.order_list_section li .info .price{
  height: 0.74666667rem;
  line-height: 0.74666667rem;
}
.mianMyOrderPage.confirm-order-page ul.order_list_section li .info .num_count_box{
  position: relative;
  display: inline-block;
  margin-left: 0.32rem;
}
.carIndexListPage.car-index-list-page-modify .CartPage .item .col2 .img{
  width: 2.43rem;
  height:2.43rem;
}
.carIndexListPage.car-index-list-page-modify .emptyCart{
  margin: 0.56rem auto 2.6rem;
}
.carIndexListPage.car-index-list-page-modify .CartPage .tab_theme_section{
  margin: 0.13333rem 0.32rem 0;
  border-radius: 0.16rem;
}
.carIndexListPage.car-index-list-page-modify .goods_list_section ul{
  padding: 0.08rem 0.32rem;
}
.carIndexListPage.car-index-list-page-modify .CartPage .item .col2 .img img{
  width: 100%;
  height: 100%;
}
.carIndexListPage.car-index-list-page-modify .CartPage .item .flex_line{
  height: auto;
}
.carIndexListPage.car-index-list-page-modify .CartPage .item .flex_line .col2{
  width: 2.43rem;
}
.carIndexListPage.car-index-list-page-modify .CartPage .item .flex_line .col3{
  padding-left: 0.32rem;
  box-sizing: border-box;
}
.carIndexListPage.car-index-list-page-modify .CartPage .item{
  margin: 0.213rem 0.32rem;
  border-radius: 0.16rem;
  width: auto;
}
.carIndexListPage.car-index-list-page-modify .CartPage .item .col3 .title{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 0.85333334rem;
  height: auto;
  word-break: break-all;
}
.carIndexListPage.car-index-list-page-modify .CartPage .item .col3 .attr{
  margin: 0.24rem 0;
}
.carIndexListPage.car-index-list-page-modify  .CartPage .item .col3 .num_count_box{
  position: relative;
  right: inherit;
  bottom: inherit;
}
.carIndexListPage.car-index-list-page-modify .CartPage .public_top_header{
  background: #fff;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.48rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0.013rem;
  color: #333333;
}
.carIndexListPage.car-index-list-page-modify .CartPage .return_back:before{
  border-color: #000000;
}
.carIndexListPage.car-index-list-page-modify .CartPage .public_top_header .text{
  color: #333333;
}
.carIndexListPage.car-index-list-page-modify .public_right_nav .icon_more{
  background-image: url("./../img/icon_more_b.png");
}
.carIndexListPage.car-index-list-page-modify  .sales-price,
.confirm-order-page  .sales-price,
.my-order-detail-page  .sales-price,
.product_list_page  .sales-price,
.selfShopNew2Page  .sales-price
{
  font-family: SourceHanSansSC-Bold;
  font-size: 0.32rem!important;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0.0133rem;
  color: #ff2c25!important;
}
.carIndexListPage.car-index-list-page-modify  .sales-price span,
.my-order-detail-page  .sales-price span,
.confirm-order-page  .sales-price span,
.product_list_page  .sales-price span,
.selfShopNew2Page  .sales-price span{
  font-size: 0.4267rem!important;
  color: #ff2c25!important;
}
.carIndexListPage.car-index-list-page-modify .CartPage .cartFooter .right .btn_submit{
  width: 2.88rem;
  height: 0.9067rem;
  /*background-color: #1875f9;*/
  border-radius: 0.12rem;
  line-height: 0.9067rem;
  right: 0.267rem;
  top: 50%;
  transform: translateY(-50%);
}
.carIndexListPage.car-index-list-page-modify .CartPage .cartFooter{
  padding-top: 0;
}
.carIndexListPage.car-index-list-page-modify .CartPage .cartFooter .FreightBox{
  margin-right: 0.12rem;
}
.carIndexListPage.car-index-list-page-modify .CartPage .cartFooter .footer-right-content{
  display: flex;
  flex-direction: column;
  align-items: baseline;
  justify-content: center;
  height: 1.33333333rem;
  padding-right: 0.32rem;
}
.carIndexListPage.car-index-list-page-modify .CartPage .cartFooter .right p.total {
  margin-top: 0;
  padding-right: 2.91rem
}
.carIndexListPage.car-index-list-page-modify .CartPage .item .col3 .limit{
  width: 100%;
  color: #999;
  margin-top: 0.2rem;
}
.carIndexListPage.car-index-list-page-modify .CartPage .sale_header{
  margin: 0.1067rem 0.32rem 0;
  border-radius: 0.16rem 0.16rem 0 0;
}
.carIndexListPage.car-index-list-page-modify .CartPage .item{
  border-radius: 0;
  margin: 0 0.32rem;
}
.carIndexListPage.car-index-list-page-modify .CartPage .item:last-of-type{
 border-radius: 0 0 0.16rem 0.16rem;
}
.car-index-list-page-modify .tab_theme_section_content.goods_list_section>ul{
  /*-webkit-column-count: 2;*/
  /*-moz-column-count: 2;*/
  /*column-count: 2;*/
  /*-webkit-column-gap: 0.16rem;*/
  /*-moz-column-gap: 0.16rem;*/
  /*column-gap: 0.16rem;*/
}
.car-index-list-page-modify .tab_theme_section_content.goods_list_section>ul li{
    /*break-inside: avoid;*/
    /*width: 100%;*/
    /*height: auto;*/
    padding: 0;
    height: 5.95333333rem;
}
.car-index-list-page-modify .tab_theme_section_content.goods_list_section>ul li p.price{
    margin-top: 0.12rem;
}
.car-index-list-page-modify .tab_theme_section_content.goods_list_section>ul li a .img{
    height: 3.8933333rem;
    position: relative;
    margin-bottom: 0;
}
.car-index-list-page-modify .tab_theme_section_content.goods_list_section>ul li a .img img{
    /*width: 100%;*/
    /*border-radius: 0.24rem 0.24rem 0 0;*/
    /*object-fit: initial;*/
    display: block;
    width: 3.2133333rem;
    height: 3.2133333rem;
}
.car-index-list-page-modify .tab_theme_section_content.goods_list_section>ul li p.price{
    padding: 0 0.32rem 0.12rem;
}
.car-index-list-page-modify .tab_theme_section_content.goods_list_section>ul li .title{
    padding: 0 0.32rem;
    font-family: SourceHanSansSC-Medium;
    /* font-size: 0.3733rem; */
    font-weight: bold;
    font-stretch: normal;
    letter-spacing: 0;
    color: #333333;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    height: 1.2rem;
    line-height: 0.4rem;
}
.layui-m-layer1 .layui-m-layerchild.layui-m-layer-promotion{
  width: 100%;
  border-radius: 0.16rem 0.16rem 0 0;
}
.product_list_page .sortSection li a{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing:0.04rem;
  color: #666666;
  border: none;
}
.product_list_page .sortSection li.active a{
  border: none;
  font-weight: bold;
}
.product_list_page .public_top_header{
  background: #ffffff;
  box-shadow: none;
}
.product_list_page .public_top_header .search_section .input{
  background-color: #f5f5f5;
  border-radius: 0.2133rem;
  border: solid 0.0267rem #1875f9;
}
.product_list_page .goods_list_section ul.productUl{
  /*column-count: 2;*/
  /*column-gap: 0.16rem;*/
}
.product_list_page .goods_list_section ul li{
  break-inside: avoid;
  width: 100%;
  height: auto;
  padding: 0;
}
.product_list_page .goods_list_section ul li  a .img img{
  width: 100%;
  height: auto;
  max-height: 4.5rem;
  min-height: 2.4rem;
  border-radius: 0.24rem 0.24rem 0 0;
}
.product_list_page .goods_list_section ul li  a .img{
  height: auto;
}
.product_list_page .goods_list_section ul li p.price{
  margin-top: 0;
  padding: 0 0.32rem 0.16rem
}
.product_list_page .goods_list_section ul li a p.title{
  padding: 0 0.32rem;
  height: auto;
  font-family: SourceHanSansSC-Medium;
  /*font-size: 0.3733rem;*/
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.16rem;
  word-break: break-all;
}
.product_list_page .goods_list_section ul li:nth-child(2n-1){
  margin-right: 0;
}
.product_list_page .goods_list_section ul li:nth-child(2n){
  margin-left: 0;
}
.product_list_page .goods_list_section ul li .market,
.favorite-list-page .market{
  display: inline-block;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.0133rem;
  color: #ff2c25;
  text-decoration: inherit;
  position: relative;
  margin:0 0.32rem 0.16rem;
}
.favorite-list-page .market{
  margin: 0;
}
.favorite-list-page .CartPage .item .flex_line{
  height: auto;
  min-height: 2.568rem;
}
.product_list_page .goods_list_section ul li .market:after,
.favorite-list-page .market:after{
  content: "";
  width: 100%;
  height: 0.02rem;
  background-color: #ff2c25;
  position: absolute;
  left: 0;
  top: 0.2rem;
  transform: rotate(-4deg);
}
.favorite-list-page .market:after{
  top: 0.3rem;
}
.favorite-list-page .CartPage .public_top_header .text_right{
  color: #333;
}
.favorite-list-page .CartPage .item .col2 .img{
  width:2.8rem;
  height: 2.8rem;
}
.favorite-list-page .CartPage .item .col2 .img img{
  width: auto;
  max-width: 100%;
  height:2.8rem;
}
.favorite-list-page .CartPage .item .col2{
  width: 3rem;
}
.favorite-list-page .CartPage .item .col3{
  height: 2.8rem;
  padding: 0.12rem;
}
.favorite-list-page .CartPage .item .col3 .price-content{
  position: absolute;
  bottom: 0.16rem;
  width: calc(100% - 0.12rem);
}
.favorite-list-page .CartPage .item .col3 a.icon_fav_cart{
  background-image: url("./../img/icon_cart_detail.png");
}
.pay-info-page.confirm-order-page ul.order_list_section li .info p.price{
  display: flex;
}
.pay-info-page.confirm-order-page ul.order_list_section li .info p.price span{
  color: #444;
  font-size: 0.42rem;
}
.pay-info-page.confirm-order-page .amount_section span{
    color: #fd1b3b;
}
#myOrderPage.pay-info-page.confirm-order-page .exchange-notice{
  margin-left: 0.32rem;
  margin-right: 0.32rem;
}
.confirm-order-page.pay-info-page .myOrderPage > section.form_input_item{
  margin-top: -0.32rem;
}
.confirm-order-page.pay-info-page .myOrderPage .favourable_section .lines_Section_all{
  padding: 0;
  line-height: 0.78rem;
}
.confirm-order-page.pay-info-page .lines_Section_all.fli_link_line:after{
  top: 0.25rem;
  right: -0.06rem;
}
.confirm-order-page.pay-info-page .myOrderPage .favourable_section .lines_Section_all .right_text{
  float: right;
  padding-right: 0.32rem;
  color: #fd1b3b;
  font-size: 0.34rem;
}
.confirm-order-page.pay-info-page .myOrderPage .favourable_section .lines_Section_all .right_text  .like_btn{
  display: inline-block;
  vertical-align: middle;
  height: 0.58666667rem;
  line-height: 0.58666667rem;
  color: #fff;
  background: #fd1b3b;
  padding: 0rem 0.21333333rem;
  border-radius: 0.08rem;
  font-size: 0.32rem;
  margin-left: 0.26666667rem;
  margin-top: -0.05333333rem;
}
.confirm-order-page.pay-info-page .myOrderPage .form_input_item.tel-card label{
  font-weight: 700;
  font-size: 0.3733rem;
  width: 1.5rem;
  white-space: nowrap;
}
.confirm-order-page.pay-info-page .myOrderPage .form_input_item.tel-card .inputs{
  margin-left: 1.833333rem;
  text-align: right;

}
.confirm-order-page.pay-info-page .myOrderPage .form_input_item.tel-card .inputs #mobile{
  text-align: right;
}
.product_list_page .goods_list_section .productUl{
  display: flex;
  padding: 0 0.24rem;
  justify-content: space-between;
}
.product_list_page .goods_list_section .productUl ul{
  width: calc(50% - 0.08rem);
  padding: 0;
}
.favorite-list-page .CartPage .item{
  margin: 0.24rem 0.32rem 0;
  border-radius: 0.23rem;
  width: initial;
}
.favorite-list-page .CartPage .item  .col3 a p{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
.address-management-page .addr_list li{
  margin: 0 0.32rem 0.24rem;
  border-radius: 0.23rem;
}
.address-management-page .addr_list li .addr_opration .right a span{
  width: 0.32rem;
}
.address-management-page .addrList .btns{
  position: fixed;
  bottom: 0.42rem;
  left: 50%;
  transform: translateX(-50%);
  padding: 0;
}
.address-management-page{
  padding-bottom: 1.8rem;
}
.order_select_coin_common .make_split{
    margin: 0;
    padding: 0;
}
.coupon_list_new_page .paymentPage .make_split{
    margin: 0;
    padding: 0;
}
.coupon_list_new_page .btn-option .btn-option-left span.integralDeduction{
    color: #ee0a3b;
}
.coupon_list_new_page .btn-option .com_btn{
    position: relative;
    display: block;
    color: #fff;
    border: none;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    overflow: hidden;
    width: 2.667rem;
    height: 0.9067rem;
    line-height: 0.9067rem;
    font-size: 0.42rem;
    background: #ee0a3b;
    color: #fff;
    text-align: center;
    border-radius: 0.16rem;
}
.coupon_list_new_page.order_select_coin_common .btn-option .btn-option-right.btn_submit_long{
    width: 2.667rem;
    height: 0.9067rem;
    line-height: 0.9067rem;
    font-size: 0.42rem;
}
.coupon_list_new_page.order_select_coin_common .btn-option .btn-option-right{
    margin-top: 0;
}
.sortSection.common_sort_section{
    box-shadow: none;
}
.sortSection.common_sort_section li.active a{
    border: none;
    font-weight: bold;
}
.sortSection.common_sort_section li a{
    font-family: SourceHanSansSC-Medium;
    font-size: 0.3733rem;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0.04rem;
    color: #666666;
    border: none;
}
.public_top_header.no_box_shadow{
    box-shadow: none;
}
.car-index-list-page-modify .supplier-header{
  background: #fff;
  margin: 0.2rem 0.32rem 0;
  height: 1rem;
  padding: 0.2rem 0.32rem;
  border-radius: 0.16rem 0.16rem 0 0;
}
.car-index-list-page-modify .supplier-header .title-name{
  font-family: SourceHanSansSC-Bold;
  font-size: 0.4267rem;
  font-weight: bold;
  color: #333333;
}
.car-index-list-page-modify  .supplier-header .for-free,
.car-index-list-page-modify  .supplier-header .no-free{
  font-size: 0.36rem;
  color: #ff2c25;
  padding-left: 0.32rem;
}
.car-index-list-page-modify  .supplier-header .no-free .btn{
  width: 1.88rem;
  height: 0.7067rem;
   background-color: #f3c9ca;
  border-radius: 0.35rem;
  line-height: 0.7067rem;
  color: #ff2c25;
  font-size: 0.36rem;
  border: none;
}
.walletPaymentContent .item_title{
  font-size: 0.3733rem;
  font-weight: 700;
  margin-bottom: 0.12rem;
}
.walletPaymentContent .fli_checkbox_blue.corner{
  margin-right: 0.12rem;
}
.walletPaymentContent .fli_checkbox_blue.corner label{
  border-radius: 50%;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layercont{
  padding: 0  0.52rem  0.32rem;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layerbtn{
  display: flex;
  justify-content: center;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layerbtn span{
  -moz-box-flex:initial;
  box-flex:initial;
  -webkit-box-flex:initial;

}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layerbtn span:first-of-type{
  height: 0.8rem;
  line-height: 0.8rem;
  font-size: 0.32rem;
  border-radius: 0.4rem;
  border: 0.0266667rem solid rgb(153,153,153);
  color: rgb(51,51,51);
  margin-right: 0.533333rem;
  width: 3rem;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layerbtn span:last-of-type{
  color: rgb(255, 255, 255);
  background: rgb(20, 143, 240);
  border-radius: 0.4rem;
  height: 0.8rem;
  line-height: 0.8rem;
  font-size: 0.32rem;
  width: 3rem;
  display: block;
  border: 0.0266667rem solid  rgb(20, 143, 240);

}
.walletPaymentContent .walletList{
  padding: 0 0.52rem;
}
.walletPaymentContent .walletList .item{
  height: 0.9rem;
  border-bottom: 1px solid #f2f2f2;
  font-size: 0.34rem;
}
.walletPaymentContent .walletList .item .value{
  font-size: 0.32rem;
}
.walletPaymentContent .walletList .item .dredge{
  color: #1875f9;
}
.walletPaymentContent .tripartitePayment{
  margin-top: 0.32rem;
}
.walletPaymentContent .tripartitePayment .tip{
  font-size: 0.29rem;
  color: #CCCCCC;
  margin: 0.32rem 0;
}
.tripartitePayment .tripartitePaymentList {
  font-size: 0.32rem;
  padding: 0.12rem 0.52rem 0;
}
.tripartitePayment .tripartitePaymentList>div{
    white-space: nowrap;
}
.tripartitePayment .tripartitePaymentList img{
  margin-right: 0.24rem;
  height: 0.52rem;
}
.walletPaymentContent .fli_checkbox_blue.corner input[type="checkbox"] + label{
  background: url(../img/recharge/icon_checked_w.png) center center / contain no-repeat #bfbfbf;
}
.walletPaymentContent .fli_checkbox_blue.corner input[type="checkbox"]:checked + label{
  background: url(../img/recharge/icon_checked_w.png) center center / contain no-repeat #1875f9;
}
.walletPaymentContent .item_title_link:after{
  content: " ";
  display: inline-block;
  height: .23rem;
  width: .23rem;
  border-width: .04rem .04rem 0 0;
  border-color: #999;
  border-style: solid;
  -webkit-transform: matrix(-.71, -.71, .71, -.71, 0, 0) rotate(-90deg);
  transform: matrix(-.71, -.71, .71, -.71, 0, 0) rotate(-90deg);
  position: relative;
  left: 0.04rem;
  top: -0.06rem;
}
.walletPaymentContent .item_title_link.up:after{
  -webkit-transform: matrix(-.71, -.71, .71, -.71, 0, 0) rotate(90deg);
  transform: matrix(-.71, -.71, .71, -.71, 0, 0) rotate(90deg);
  top: 0.06rem;
}
.coupon_list_new_page_modify.coupon_list_new_page .ticket_list li{
  height: 2.4533rem;
  background-color: #fff;
  padding-left: 0;
  border: none;
}
.coupon_list_new_page_modify.coupon_list_new_page .ticket_list li:before,
.coupon_list_new_page_modify.coupon_list_new_page .ticket_list li:after{
  background-color: #f2f2f2;
}
.coupon_list_new_page_modify.coupon_list_new_page .ticket_list ul li .ticket_info{
  height: 100%;
  padding-top: 0;
}
.coupon_list_new_page_modify.coupon_list_new_page .ticket_list ul .text,
.coupon_list_new_page_modify.coupon_list_new_page .ticket_list ul .text>div{
  height: 100%;
}
.coupon_list_new_page_modify.coupon_list_new_page .ticket_list .numLeft{
  background: #eb143a;

  height: 100%;
  width: 2.2rem;
}
.coupon_list_new_page_modify.coupon_list_new_page .ticket_list p.num{
  color: #ffffff;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.4rem;
  font-weight: normal;
  letter-spacing: 0.0267rem;
  max-width: 100%;
  min-width: initial;
}
.coupon_list_new_page_modify.coupon_list_new_page .ticket_list p.num .discountNum span{
  font-size: 0.32rem;
}
.coupon_list_new_page_modify.coupon_list_new_page .ticket_list p.num span{
  font-size: 0.5333rem;
}
.coupon_list_new_page_modify.coupon_list_new_page .ticket_list p.num i{
  font-size: 0.4rem;
  top: 0;
}
.coupon_list_new_page_modify.coupon_list_new_page .ticketPage{
  background: #f2f2f2;
}
.coupon_list_new_page_modify.coupon_list_new_page .ticket_list .btn{
  width: 1.61rem;
  height: 0.5667rem;
  line-height: 0.5667rem;
  border-radius: 0.28335rem;
  position: initial;
  margin-top: 0.12rem;
  font-family: SourceHanSansSC-Regular;
}
.coupon_list_new_page_modify.coupon_list_new_page  .ticket_list  .content-right{
  padding-left: 0.2667rem;
  width: calc(100% - 2.2rem);
}
.coupon_list_new_page .ticket_list .content-right p:first-of-type{
  color: #333333;
  font-weight: bold;
  padding-left: 0.17rem;
}
.coupon_list_new_page .ticket_list .content-right p:nth-of-type(2){
  font-family: SourceHanSansSC-Regular;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.013rem;
  color: #666666;
  margin: 0.08rem 0;
  padding-left: 0.17rem;
}
.coupon_list_new_page .ticket_list .content-right p.category-of-use{
  background: #f2f2f2;
  color: #999999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.29rem;
}
.coupon_list_new_page_modify.coupon_list_new_page   .ticket_list p.num.exchange-num{
  text-align: center;
  margin-left: 0.08rem;
  margin-bottom: 0.12rem;
}
.coupon_list_new_page_modify.coupon_list_new_page   .ticket_list p.num.exchange-num span{
  font-size: 0.58rem;
}
.coupon_list_new_page .ticket_list .conversion-type .content-right p:first-of-type{
margin-bottom: 0.12rem;
}
.coupon_list_new_page.coupon_list_new_page_modify .ticket_list .available .text .fli_checkbox_blue label{
  border-color: #333;
  top: .68rem;
  left: -.2rem;
}
.coupon_list_new_page_modify.coupon_list_new_page{
  background: #f2f2f2;
}
.coupon_list_new_page_modify.coupon_list_new_page.select_coupon_list_new_page_modify .ticket_list ul .text, .coupon_list_new_page_modify.coupon_list_new_page.select_coupon_list_new_page_modify .ticket_list ul .text>div {
  width: 100%;
}
.coupon_list_new_page_modify.coupon_list_new_page.select_coupon_list_new_page_modify .ticket_list ul .text div.fli_checkbox_blue{
  width: 0.453333rem;
  height: 0.45333rem;
  right: 0.32rem;
  position: absolute;
}
.selfShopNew2Page.modify .product_classification_list .title{
  width: initial;
  text-align: initial;
  padding-left: 0.42rem;
}
.cate-list-page-new.cate-list-page-new-zj .displayBox{
  margin-left: 0;
}
.cate-list-page-new-zj .displayBox .cateList .item{
  box-sizing: border-box;
  position: relative;
  display: block;
  width: calc(33.33% - .16rem);
  height: .8rem;
  line-height: .8rem;
  background: #f3f3f3;
  text-align: center;
  border-radius: .0667rem;
  color: #333;
  float: left;
  font-size: .32rem;
  overflow: hidden;
  margin: .08rem;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 0;
}
.cate-list-page-new-zj .displayBox .cateList .item.active {
  padding-left: 0.26666667rem;
  color: #1875f9;
  background: url(../img/icon_band_selected_blue.png) 0.133333rem center no-repeat #fff;
  background-size: 0.24rem 0.24rem;
  border: 1px solid #1875f9;
}
.cate-list-page-new-zj .displayBox .cateList{
background: #ffffff;
}
.cate-list-page-new-zj  footer{
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0.32rem 0.42rem;
  box-shadow: 0 0 0.2rem rgba(0, 0, 0, 0.1);
  background: #fff;
  text-align: center;
}
.cate-list-page-new-zj  footer input{
  display: block;
  width: calc(50% - 0.24rem);
  float: left;
  height: 0.9067rem;
  line-height: 0.9067rem;
  border-radius: 0.133rem;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.013rem;
  color: #1875f9;
  background: #fff;
  border: 1px solid #1875f9;
}
.cate-list-page-new-zj  footer input:last-of-type{
  background:#1875f9;
  color: #ffffff;
  float: right;
}
.cate-list-page-new-zj  .changeChild{
  padding: 0 0.32rem;
}
.cate-list-page-new-zj  .changeChild img{
  transition: all  0.3s ease;
  height: 0.24rem;
}
.cate-list-page-new-zj .changeChild.imgUp img{
  transform: rotate(180deg);
}
.act_promotion_page_modify .fixRight{
  position: fixed;
  right: 0.22rem;
  bottom: 3.65rem;
  z-index: 9999;
  height: 1.18rem;
}
.purchase-option{
  float: right;
}
.purchase-option span{
  white-space: nowrap;
  margin-right: 0.24rem;
}
.personInfoPage section .item .purchase-option .switch{
  margin-top: 0;
  margin-right: 0.12rem;
}
.fli_pub_pop_form.pay-info-tips.alipay-info #phoneNum{
    height: initial;
}
.cakeCartPage.CartPage  .productName{
  margin: 0.13333rem 0.32rem 0;
}
.cakeCartPage.CartPage div.productName:nth-of-type(1){
  margin-top: 0.13333rem;
}
.carIndexListPage.car-index-list-page-modify .cakeCartPage.CartPage .item .col3 .num_count_box{
  right: -0.4rem;
}
.pintoto-info-page .ticketFaceName{
  font-style: normal;
  color: #999999;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: bold;
  font-stretch: normal;
}
.pintoto-info-page .total-price{
  justify-content: end;
}
.selfShopNew2Page.taobaoPage .banner_section{
  height: 4.8266rem;
  position: relative;
  top: 0;
  width: 100%;
  padding-top: 0;
}
.selfShopNew2Page.taobaoPage{
  padding-top: 0;
  background: #f5f5f5;
}
.selfShopNew2Page.taobaoPage .banner_section .swiper-container a.swiper-slide img{
width: 100%!important;
  height:4.8266rem!important;
  border-radius: 0;
}
.selfShopNew2Page.taobaoPage .self_support_index_page .logo_and_search .logo{
  width: 0.7333rem;
}
.selfShopNew2Page.taobaoPage .self_support_index_page .logo_and_search .logo img{
  height: 0.6133rem;
}
.selfShopNew2Page.taobaoPage .van-notice-bar{
padding-left: 0.7867rem;
}
.selfShopNew2Page.taobaoPage .self_support_index_page .logo_and_search .search_section .icon_search{
  background: initial;
  width: 1.386rem;
  height: 0.66rem;
  background-color: #34bd23;
  border-radius: 0.1466rem;
  text-align: center;
  line-height: 0.66rem;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.2933rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #ffffff;
  top: 0.07rem;
}
.selfShopNew2Page.taobaoPage .self_support_index_page .logo_and_search .search_section .icon_search_img{
  position: absolute;
  left: 0.24rem;
  top: 0.195rem;
  width: 0.41rem;
  height: 0.41rem;
  background: url(../img/taobao/search_logo.png) center center / contain no-repeat;
}
.selfShopNew2Page.taobaoPage .self_support_index_page .logo_and_search .search_section{
  margin: 0 0 0 0.3rem;;
}
.selfShopNew2Page.taobaoPage  .searchRotation .notice-swipe .van-swipe-item{
  color: #222222;
  font-size: 0.426rem;
}
.selfShopNew2Page.taobaoPage .self_support_index_page .logo_and_search .search_section .input{
  padding-left: 0.7867rem;
  color: #222222;
  font-size: 0.426rem;
}
.selfShopNew2Page.taobaoPage .self_support_index_page .logo_and_search .search_section .input::placeholder{
  color: #222222;
  font-size: 0.426rem;
}
.selfShopNew2Page.taobaoPage  .self_support_index_page .logo_and_search.fixedtop{
  background: #23bd39;
}
.selfShopNew2Page.taobaoPage .self_support_index_page .nav_list{
  margin: 0 0.3333rem 0.2667rem;
}
.selfShopNew2Page.taobaoPage .group_buying .title_logo_title{
  font-family: DOUYINSANSBOLD-GB;
  font-size: 0.4rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #222222;
}
.selfShopNew2Page.taobaoPage .group_buying .btn{
  margin-left: 0.2933rem;
  width: 1.546rem;
  height: 0.48rem;
  line-height: 0.48rem;
  text-align: center;
  background-image: linear-gradient(-14deg,
  #fe551a 0%,
  #fe3030 100%),
  linear-gradient(
          #fe3a40,
          #fe3a40);
  background-blend-mode: normal,
  normal;
  border-radius: 0.133rem;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0;
  color: #ffffff;
}
.selfShopNew2Page.taobaoPage .group_buying .title_tips .title_right{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0;
  color: #666666;
}
.selfShopNew2Page.taobaoPage .cashGoSection #group_buying_list .swiper-slide{
  background: transparent;
  width: 2.12rem;
}
.selfShopNew2Page.taobaoPage .cashGoSection #group_buying_list .swiper-slide .img-box{
  width: 1.67rem;
  height: 1.67rem;
}
.selfShopNew2Page.taobaoPage .cashGoSection{
height: 3.76rem;
  padding: 0.24rem 0.32rem;
  background-image: linear-gradient(0deg,
  #ffffff 60%,
  #ffe5dc 100%),
  linear-gradient(
          #ffffff,
          #ffffff);
  margin-bottom: 0.2667rem;
}
.selfShopNew2Page.taobaoPage .cashGoSection .des .coinConvertPrice{
  width: 100%;
  height: 0.56rem;
  background: url(../img/taobao/price-bg.png) center center / contain no-repeat;
  color: #FFFFFF;
  font-weight: bold;
  padding-right: 0.24rem;
}
.selfShopNew2Page .cashGoSection #group_buying_list .des{
  padding: 0;
}
.selfShopNew2Page.taobaoPage .cashGoSection .des .coinConvertPrice .price-num{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.24rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #ffffff;
}
.selfShopNew2Page.taobaoPage .cashGoSection .des .coinConvertPrice .price-num span{
  font-size: 0.36rem;
}
.selfShopNew2Page.taobaoPage .self_support_index_page .logo_and_search .cart_customer{
  width: 1.12rem;
 justify-content: end;
}
.taobaoPage   .hotList ul li{
  position: relative;
  float: initial;
  width: 100%;
  margin-left: 0;
}
.taobaoPage   .hotList ul{
  width: calc(50% - 0.16rem);
}
.taobaoPage   .hotList ul li .imgBox{
  height: auto;
  position: relative;
}
.taobaoPage   .hotList ul li img{
  width: 100%;
  border-radius: 0.16rem 0.16rem 0 0;
  height: auto;
  max-height: 4.5rem;
  min-height: 2.4rem;
}
.taobaoPage   .hotList ul li .brandName{
  position: absolute;
  top: 0;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.2933rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0;
  color: #ffffff;
  background-color: #fb2818;
  border-radius: 0.2133rem 0;
  left: 0;
  padding: 0.06rem 0.32rem;
  max-width: 98%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 2.2rem;
  text-align: center;
}
.taobaoPage .hotList ul li .des{
  margin-top: 0.24rem;
}

.taobaoPage .hotList ul li .des   .tabList{
  margin: 0.12rem 0;
}

.taobaoPage .hotList{
  padding: 0 0.32rem;
}
.taobaoPage .hotList .nav{
  height: 1.42667rem;
  padding: 0 0.32rem;

}
.taobaoPage .hotList .nav .leftContent img{
  width: 0.58666667rem;
}
.taobaoPage .hotList .nav .rightContent{
  color: rgb(153,153,153);
  font-size: 0.32rem;
  font-weight: bold;
}
.taobaoPage .hotList ul li{
  margin-bottom: 0.32rem;
  background: #ffffff;
  border-radius: 0.16rem;
}
.taobaoPage .hotList ul li .imgBox{
  width: 100%;
  height: 4.74666667rem;
}
.taobaoPage .hotList ul li img{
  width: 3.3066667rem;
}
.taobaoPage .hotList ul li .des{
  width: 100%;
  padding: 0 0.32rem;
}
.taobaoPage .hotList ul li .des p:first-child{
  width: 100%;
  font-weight: bold;
  font-size: 0.34666667rem;
  font-family: SourceHanSansSC-Medium;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.16rem;
  word-break: break-all;
}
.taobaoPage .hotList ul li .des p:nth-child(2){
  color: rgb(255,87,38);
  font-size: 0.32rem;
  /*margin: 0.34666667rem 0 0.4rem;*/
  margin: 0.24rem 0;
  /*padding-left: 0.32rem;*/
}
.taobaoPage .hotList ul li .des p:last-child{
  color: rgb(255,87,38);
  font-size: 0.32rem;
  margin-bottom: 0.37333333rem;
}
.taobaoPage .hotList ul li .des p:last-child span:first-child span{
  font-size: 0.42666667rem;
  font-weight: bold;
}
.taobaoPage .hotList ul li .des p:last-child span.sale{
  font-size: 0.34666667rem;
  color: rgb(153,153,153);
  text-decoration: line-through;
}
.taobaoPage  .van-loading__text{
  font-size: 0.32rem;
}
.taobaoPage  .van-list__finished-text,
.taobaoPage .van-list__loading{
  height: 1.5rem;
  font-size: 0.32rem;
}
.taobaoPage  .icon_cart{
  width: .5466667rem;
}