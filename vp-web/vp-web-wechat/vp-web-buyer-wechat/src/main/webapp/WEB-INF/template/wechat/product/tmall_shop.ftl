<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>天猫商城</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/plugins/swiper-4.5.0/css/swiper.min.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" />
    <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/shopCommon.js"></script>
    <script src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/swiper-4.5.0/js/swiper.min.js" ></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/dropload.min.js" ></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/jquery.fly.min.js" ></script>
    <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>

</head>
[#if member??&&member.sourceUIVersion==0]
    [#assign versionType="old"]
[#else ]
    [#assign versionType="new"]
[/#if]
<body class="has_fixed_footer modify selfShopNew2Page taobaoPage" >

   <div id="self_support_new_index_page" class="self_support_index_page" v-cloak>
          [#--[@product_category_root_list supplierId = supplier.id]--]
    <header class="logo_and_search flexbox align-items-c">
        <div class="logo" @click="goBack">
            	<img src="${base}/resources/wechat/img/taobao/goback.png"/>
        </div>
        <div class="search_section standard_input flex1">
            <form id="productSearchForm" action="${base}/product/search.jhtml" method="get">
                <input type="hidden" name="supplierId" value="${(supplier.id)!}"/>
                <input type="hidden" name="keyword" id="searchValue" value=""/>
                <input @keypress="searchBtn" name="keyword2" type="search" @focus="goSearch" @blur="showRotation" class="input fixedPositionInput" id="input_search" value="" :placeholder="sreachWordtList.length>0?'':'请输入搜索内容'">
                <span class="icon_search_img"></span>
                <span class="icon_search">搜索</span>
                <span class="icon_clear"></span>
            </form>
            <div v-if="sreachWordtList.length>0" class="searchRotation announcementBox" style="position: absolute;height: 0.8rem;top: 0">
                <van-notice-bar
                        :scrollable="false"
                        @click="onClickSearch"
                        background="transparent"
                >
                    <van-swipe
                            ref="searchRotationSwipe"
                            vertical
                            class="notice-swipe"
                            :autoplay="6000"
                            :show-indicators="false"
                            @change="onChangeSearch"
                    >
                        <van-swipe-item v-for="(item,index) in sreachWordtList" v-cloak :key="'sreachWordtList'+index" >
                            {{item}}
                        </van-swipe-item>
                    </van-swipe>
                </van-notice-bar>
            </div>
        </div>
        <div class="cart_customer flexbox align-items-c ">
            	   [#if purchaseMemberFlag??&&purchaseMemberFlag]
                       <a href="javascript:;" @click="jump('/wechat/purchase/index.jhtml')" class="cart_nums" id="enterShoppingCart">
                           <div class="icon_carts"></div>
                       </a>
                   [#else ]
                        <a href="javascript:;" @click="jump('/cart/list.jhtml')" class="cart_nums" id="enterShoppingCart">
                            <div class="icon_carts"></div>
                            <label class="num_circle" id="cart_quantity">0</label>
                        </a>
                   [/#if]
        </div>
    </header>


    <div class="banner_section">
        <div class="swiper-container">
            <div class="swiper-wrapper">
                <a class="swiper-slide" :href="ite.links?ite.links:'#'" v-for="(ite,index) in bannerList">
                    <img :src="ite.img"/>
                </a>
            </div>
            <div class="swiper-pagination"></div>
        </div>
    </div>

       <div class="nav_list">
                [#if productCategories!=null && productCategories?size>0]
                    [#list productCategories as rootProductCategory]
                        [#if rootProductCategory_index<=8]
                        <a href="${base}/product/catelist.jhtml?productCategoryId=${rootProductCategory.id}&supplierId=${supplier.id}">
                            <div class="img">
                                <img src="${base}${rootProductCategory.icon}" />
                            </div>
                            <p>${rootProductCategory.name}</p>
                        </a>
                        [/#if]
                    [/#list]
                [/#if]

           <a href="${base}/product/catelist.jhtml?supplierId=${supplier.id}&productCategoryId=${(productCategories.get(0).id)!''}">
               <div class="img">
                   <img src="${base}/resources/wechat/img/taobao/icon_more.png" />
               </div>
               <p>全部分类</p>
           </a>
       </div>
<!--拼团抢购-->

		<div class="group_buying section_common_card cashGoSection [#if versionType=='old']oldCashGoSection[/#if]">
            <div onclick="goGroup()" class="title_tips flexbox justify-content-space-between align-items-c">
                    <div class="title_left flexbox align-items-c" >
                        <span class="title_logo_title">
                            优惠精选
                        </span>
                            <span class="btn">全网低价</span>
                    </div>
                    <div class="title_right flexbox align-items-c countDownSection ">
                        更多>
                    </div>
            </div>
            <div id="group_buying_list" class="swiper-container">
                <div class="swiper-wrapper">
                [#if act??&&act.actProducts??&&act.actProducts?size>0]

        		[#list act.actProducts as actProduct]
        			[#assign p=actProduct.products]
                    [#if p.isList]
                    [#assign productImage = setting.siteUrlImg + setting.defaultThumbnailProductImage]
                    [#if (p.supplierId.id == 68 || p.supplierId.id == 181) && p.attributeValue8 != null]
                        [#assign productImage = p.attributeValue8]
                    [#elseif p.image!=null]
                        [#assign productImage = setting.siteUrlImg + p.image]
                    [#elseif com_person_product_default_img != null]
                        [#assign productImage = com_person_product_default_img]
                    [/#if]
                    <div class="swiper-slide " onclick="location.href='${base}${p.path}';">
                        <div class="img-box flexbox justify-content-c align-items-c">
                            <img  src="${productImage}" alt=""/>
                        </div>
                        [#if versionType=='new']
                        <div class="des">
                                <p class="coinConvertPrice flexbox align-items-c  justify-content-c">
                                    [#if productShowRate??]
                                    ${coinConvert(p.price, productShowRate, true, false)}
                                    [#else]
                                        <span class="price-num">￥<span>${currency(p.price, false, false, true)}</span></span>
                                    [/#if]
                                </p>
                        </div>
                        [#else ]
                            <div class="old_price">
                                [#if productShowRate??]
                                    <p class="price">${coinConvert(gp.groupPrice, productShowRate, true, false)}</p>
                                [#else ]
                                    <p class="price">${currency(gp.groupPrice, true, false, true)}</p>
                                    <p>${currency(p.marketPrice, true, false, true)}</p>
                                [/#if]
                            </div>
                        [/#if]
                    </div>
                    [/#if]
                [/#list]
                [/#if]
                </div>
            </div>
        </div>
              <!--商品列表-->
              <van-list
                      class="hotList"
                      v-model="loading"
                      :finished="finished"
                      finished-text="没有更多了"
                      @load="onLoad"
              >
                  <!--瀑布流 分成两组数据-->
                  <div class="flexbox justify-content-space-between">
                      <ul>
                          <li @click="goDetail('${base}'+item.path)" class="flexbox flex-column align-items-c justify-content-c"
                              v-for="(item,index) in productList0"
                              v-if="item!=null"
                              :key="'brand_list_'+index">
                              <div class="imgBox flexbox align-items-c justify-content-c">
                                  <img :src="item.attributeValue8"/>
                                  <span  class="no_stock" style="display: none" :id="'p-stock_'+item.id">区域无货</span>
                              </div>
                              <div class="des">
                                  <p v-cloak>{{item.name}}</p>
                                  <p class="tabList clearfix" v-if="item.attributeValue11&&item.attributeValue11.length>0">
                                      <span class="tab" v-for="(ite,idx) in item.attributeValue11" :key = "'attributeValue11'+idx+index">{{ite}}</span>
                                  </p>
                                  <p class="flexbox justify-content-space-between align-items-c" v-cloak>
                                           [#if productShowRate??]
                                                        <span  >
                                                                    <i class="iconfontAli icon-ali-jifen"></i><span>{{item.price}}
                                                       </span>
                                           [#else]
                                                      <span>
                                                    ￥<span>{{item.price}}</span>
                                                    </span>
                                               [#if !(purchaseMemberFlag??&&purchaseMemberFlag)]
	                                                <span :data-id="item.id"  @click.stop="addToCart($event, item)"  class="add_cart icon_cart"></span>
                                               [/#if]
                                           [/#if]
                                  </p>
                              </div>
                          </li>
                      </ul>
                      <ul >
                          <li @click="goDetail('${base}'+item.path)"  class="flexbox flex-column align-items-c justify-content-c"
                              v-for="(item,index) in productList1"
                              v-if="item!=null"
                              :key="'brand_list_'+index">

                              <div class="imgBox flexbox align-items-c justify-content-c">
                                  <img :src="item.attributeValue8"/>
                                  <span   class="no_stock" style="display: none" :id="'p-stock_'+item.id">区域无货</span>
                              </div>
                              <div class="des">
                                  <p v-cloak>{{item.name}}</p>
                                  <p class="flexbox justify-content-space-between align-items-c" v-cloak>
                                               [#if productShowRate??]
                                                    <span  >
                                                                <i class="iconfontAli icon-ali-jifen"></i><span>{{item.price}}
                                                   </span>
                                               [#else]
                                                      <span>
                                                    ￥<span>{{item.price}}</span>
                                                    </span>
                                                   [#if !(purchaseMemberFlag??&&purchaseMemberFlag)]
	                                                <span :data-id="item.id"     @click.stop="addToCart($event, item)" class="add_cart icon_cart"></span>
                                                   [/#if]
                                               [/#if]
                                  </p>
                              </div>
                          </li>
                      </ul>
                  </div>

              </van-list>
            [#assign current = "mall" /]
            [#include "./wechat/include/footer.ftl" /]
   </div>
    <script>

    </script>
    <script>
        localStorage.setItem('versionType','${versionType}');
        var companyId="${company.id}";
        var bannerList=[[@ad_list id = 715   /]];

        function goGroup() {
            window.location.href ="${base}/act/list/${(act.id)!}.jhtml"
        }
        function  handleTabScroll(){
            var itemleft = $(".shop_nav_section_ul").scrollLeft(); //大盒子滚动左边的距离
            var itemWith = $(".shop_nav_section_ul li").width();
            var sitemWith = $(".scroll-bar-lists .item").width();
            var maxLeft = 0.16*$(".scroll-bar-lists .item").length - 0.64; //小盒子左侧最大的left值 0.16为item设置的rem值。0.64为bar设置的值
            var rate = sitemWith/0.16;
            var sleft = (sitemWith*itemleft/itemWith)/rate + 0.16; //计算小盒子左侧应该滚动的距离 并加一个小单位修正
            //边界值控制
            if(sleft <= 0.16){
                sleft = 0;
            }
            if(sleft > maxLeft){
                sleft = maxLeft;
            }
            var sleftrem = sleft + "rem"
            $(".bar").animate({left:sleftrem},0);
        }
        function jumpSearch () {
            vm.clearsessionStorage();
            if($('#input_search').val()&&$('#input_search').val().trim()){
                $("#searchValue").val($('#input_search').val());
                $('#input_search').val('');
                $("#productSearchForm").submit();
            }else {
                if(vm.sreachWordtList.length===0){
                    return
                }
                var searchWord = vm.sreachWordtList[vm.currentIndex];
                $('#input_search').val('');
                $("#searchValue").val(searchWord);
                $("#productSearchForm").submit();
            }
            setTimeout(function () {
                vm.showRotation();
            })
        }
        var swiperWidth;
        var maxTranslate;
        var maxWidth;
        var ProductRecordsArr = new Array();
        var dataFlag=true //请求数据
        var mySwiper
        let vm = new Vue({
            el: "#self_support_new_index_page",
            data() {
                return {
                    loading: false,
                    finished: false,
                    totalPage:0,
                    currentPage:1,
                    isFirst:true,//是否是第一次加载
                    productList: [],
                    productList0:[
                        [#list page.content as product]
                        [#if product_index%2==0]
                        {
                            id: ${product.id},
                            attributeValue8: "${product.attributeValue8}",
                            name: "${product.name}",
                            price: ${product.price},
                            path: "${product.path}"
                        }[#if product_index+1<page.content?size], [/#if]
                        [/#if]
                        [/#list]
                    ],//手动把数据分成两组
                    productList1:[
                        [#list page.content as product]
                        [#if product_index%2==1]
                        {
                            id: ${product.id},
                            attributeValue8: "${product.attributeValue8}",
                            name: "${product.name}",
                            price: ${product.price},
                            path: "${product.path}"
                        }[#if product_index+1<page.content?size], [/#if]
                        [/#if]
                        [/#list]
                    ],//手动把数据分成两组
                    hideAnnouncement:true,//隐藏公告
                    currentIndex:0, //顶部搜索内容当前显示位置
                    tag_scroll_top:0,
                    listCurPageSS:0,
                    sreachWordtList:[]
                }
            },
            created:function () {
                this.inint();
                this.sreachWordtList = querySearchList(9);
                recordPageFrom("/product/tMallShop.jhtml?sysModuleId=2483");
            },
            methods:{
                jump(url){
                    this.clearsessionStorage();
                    window.location.href ="${base}"+ url;
                },
                clearsessionStorage(){
                    sessionStorage.removeItem("listUrl");
                    sessionStorage.removeItem("listCurPageSS");
                    sessionStorage.removeItem("tag_scroll_top");
                },
                goBack(){
                    this.clearsessionStorage();
                  history.go(-1);
                },
                inint(){
                    let listUrl = window.location.href;
                    if(sessionStorage.getItem("listUrl") === listUrl){
                        this.tag_scroll_top =parseFloat(sessionStorage.getItem("tag_scroll_top")) ;
                        this.listCurPageSS = parseFloat(sessionStorage.getItem("listCurPageSS"));
                    }
                },
                //加入购物车
                addToCart(event, item){
                    event.stopPropagation();
                    var offset = $("#enterShoppingCart").offset();
                    var productId = item.id;
                    var flyer = $('<img class="u-flyer" src="${base}/resources/wechat/img/icon_cart.png">');
                    flyer.fly({
                        start: {
                            left: event.clientX,
                            top: event.clientY
                        },
                        end: {
                            left: offset.left,
                            top: offset.top,
                            width: 0,
                            height: 0
                        },
                        onEnd: function() {
                            $.ajax({
                                url: "${base}/cart/add.jhtml",
                                type: "POST",
                                data: {
                                    id: productId,
                                    quantity: 1,
                                    areaId: ""
                                },
                                dataType: "json",
                                cache: false,
                                success: function(message) {
                                    addSelectProduct(productId);
                                    refreshCartConut();
                                    collection("AddToCart", null, null, productId, null, null, null);
                                }
                            });
                        }
                    });
                },
                //点击品牌跳转
                goDetail(data) {
                   let listUrl = window.location.href;
                   sessionStorage.setItem("listUrl",listUrl);
                   sessionStorage.setItem("listCurPageSS",this.currentPage);
                    sessionStorage.setItem("tag_scroll_top", $(window).scrollTop());
                    window.location.href = data;
                },
                onLoad() {
                    debugger
                    this.loading = true;
                    var that = this;
                    var pageSize = 10;
                    if(this.listCurPageSS>1&&this.currentPage===1){

                        pageSize=(this.listCurPageSS-1)*10;
                    }
                    if(this.currentPage===1&&pageSize===10){
                        this.currentPage++
                    }
                    // 异步更新数据
                    $.ajax({
                        url: "${base}/product/tmallProductList.jhtml",
                        type: "POST",
                        data: {pageNumber:this.currentPage,pageSize:pageSize},
                        success: function (data) {
                            var arr0 =[];
                            var arr1=[];
                            
                            // if(that.currentPage===1){
                            //     data.content.splice(0, 10)
                            // }
                            
                            if(data.content&&data.content.length>0){
                                if( that.isFirst ){
                                    // that.productList = [];
                                    // that.productList0=[];
                                    // that.productList1=[];
                                    data.content.splice(0, 10)
                                    that.isFirst = false;
                                }    
                                data.content.forEach((item,index) => {
                                    if(index%2===1){
                                        arr1.push(item);
                                    }else {
                                        arr0.push(item);
                                    }
                                });
                                that.productList0 =that.productList0.concat(arr0);
                                that.productList1 =that.productList1.concat(arr1);
                              //  that.productList =  that.productList.concat(data.content);
                                that.listStock(data.content);
                            }
                            if(that.currentPage===1&&that.tag_scroll_top>0){
                                that.$nextTick(()=>{

                                        $(window).scrollTop(that.tag_scroll_top);


                                })

                            }
                            that.totalPage =Math.ceil(data.total/10) ;
                            // 数据全部加载完成
                            if(that.currentPage >= that.totalPage){
                                that.finished = true;
                            }else{
                                that.currentPage = that.currentPage + pageSize/10;
                            }
                            // 加载状态结束
                            that.loading = false;
                        },
                        error: function (xhr, type) {
                            //加载数据失败提示
                            that.error = true;
                            if( that.isFirst ){
                                that.productList = [];
                                that.isFirst = false;
                            }
                        }
                    });
                },
                searchBtn(e){
                    var ev=e||window.event;
                    if(ev.keyCode==13){
                        ev.preventDefault(); //禁止默认事件（默认是换行）
                        jumpSearch();
                    }

                },
                // 搜索轮播切换的时候记录当前的值
                onChangeSearch(index){
                    this.currentIndex = index;
                },
                showRotation(){
                    $(".searchRotation").show();
                    $("#btn_search").hide();
                    this.$refs.searchRotationSwipe.autoplay='6000';
                    $('#input_search').val('');
                },
                goSearch(){
                    if(this.sreachWordtList.length===0){
                        window.location.href="${base}/hotSearchWord/searchPage.jhtml?hotkeywordtype=1&supplierId=${(supplier.id)!}";
                    }
                },
                // 点击搜索轮播
                onClickSearch(){
                    // this.$refs.searchRotationSwipe.autoplay=false;
                    $(".searchRotation").hide();
                    $('#input_search').focus();
                    var searchWord = this.sreachWordtList[this.currentIndex];
                    // $("#input_search").val(searchWord);
                    // $('#searchValue').val(searchWord);
                    window.location.href="${base}/hotSearchWord/searchPage.jhtml?hotkeyword="+searchWord+"&hotkeywordtype=1&supplierId=${(supplier.id)!}";
                },
                goAnnouncement(){ //点击通知跳转到通知公告
                    if(!this.hideAnnouncement){//通知栏显示的时候跳转（点击“×”后会隐藏，防止点“×”后依旧跳转）
                        location.href="${base}/announcement/index.jhtml?showModule=1";
                    }
                },

                //查询是否有货
                listStock(data){
                    var ids = new Array();
                    data.map(item=>{
                        ids.push(item.id)
                    })
                    $.ajax({
                        url: '${base}/product/listStock.jhtml',
                        type: "POST",
                        data: {ids: ids.toString(), areaId: getCookieAreaId()},
                        dataType: "json",
                        cache: false,
                        success: function(data) {
                            $.each(data, function(index, obj) {
                                if(obj){
                                    $("#p-stock_" + index).show();
                                }
                                else{
                                    $("#p-stock_" + index).hide();
                                }

                            });
                        }
                    });
                },
                //隐藏公告栏
                hideAnn(){
                    let that = this;
                    $.ajax({
                        url: '${base}/announcement/hide.jhtml',
                        type: 'POST',
                        cache: false,
                        async:false,
                        success: function(data){
                            if(data.code == "200"){//显示公告
                                that.hideAnnouncement = true;//显示公告栏
                            } else {
                                console.log("隐藏公告栏失败")
                            }
                        },
                        error: function (xhr, textstatus, thrown){
                            console.log("隐藏公告栏失败")
                        }
                    });
                }
            }

        })
        $(function () {
            setIsFixed();
            refreshCartConut();
            localStorage.setItem("cateListPage",'/product/tMallShop.jhtml');
            $(window).scroll(function () {
                setIsFixed();
            });
            $(".icon_search").on("mousedown",function(e){//搜索
                var ev = e||window.event;
                ev.preventDefault();
                ev.stopPropagation();
                jumpSearch();
            });
            var mySwiper1=new Swiper('#group_buying_list', {
                freeMode: true,
                freeModeMomentumRatio: 0.5,
                slidesPerView: 'auto',
            });

            var num_slider2 = null;
            var pic_loop = true;
            if($(".banner_section .swiper-container .swiper-slide").length==1){
                num_slider2 = false;
                pic_loop = false;
                $(".swiper-pagination").hide();
            }else{
                num_slider2 = {
                    delay: 3000,
                    stopOnLastSlide: false,
                    disableOnInteraction: false,
                }
            }
            //banner背景轮播
            var swiper2 = new Swiper('.banner_section .swiper-container', {
                autoplay: num_slider2,
                effect: 'slide',
                loop: pic_loop,
                on: {
                    slideChange: function () {
                    },
                },
                pagination: {
                    el: '.swiper-pagination',
                    type: 'bullets',
                },
            })
            $.each($(".productUl li"),function(i,item){
                if($(item).hasClass("active")){
                    $(item).closest("ul").slideDown();
                    $(item).find("ul").slideDown();
                }
            });
            //选择地址时判断是否缺货
            // var productIds = $("input[name='listProductId']");
            // var ids = new Array();
            // $(productIds).each(function(i){
            //     ids.push($(this).val());
            // });


        });

        function setIsFixed() {
            var layerTop = $(".logo_and_search").height();
            var bodyScrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
            if (bodyScrollTop >= layerTop) {
                if( !$(".logo_and_search").hasClass("fixedtop")){
                    $(".logo_and_search").addClass("fixedtop");
                }
            } else {
                $(".logo_and_search").removeClass("fixedtop");
            }
        }

        function removeInput(){
            dataFlag=false
            listStock();
            $("#totalPages").remove();
        }
        //选择地址时判断是否缺货
        function listStock(){
            var productAreaId = $("#areaId").val();
            var treePath = $("#treePath").val();
            var productIds = $("input[name='listProductId']");
            var ids = new Array();
            //获取最新10个校验库存
            $(productIds).each(function(i){
                ids.push($(this).val());
            });

            var temp = ids.filter(function(v){ return ProductRecordsArr.indexOf(v) == -1 })
            ProductRecordsArr = ids

            // addPageCookies();

            $.ajax({
                url: '${base}/product/listStock.jhtml',
                type: "POST",
                data: {ids: temp.toString(), areaId: productAreaId},
                dataType: "json",
                cache: false,
                success: function(data) {
                    $.each(data, function(index, obj) {
                        if(obj){
                            $("#p-stock_" + index).show().parents("li").find(".icon_cart").hide();
                        }
                        else{
                            $("#p-stock_" + index).hide().parents("li").find(".icon_cart").show();
                            [#if purchaseMemberFlag??&&purchaseMemberFlag]
                                $("#p-stock_" + index).parents("li").find(".icon_cart").hide();
                             [/#if]
                        }

                    });
                }
            });
        }








        //顶部轮播滚动事件
        function mySwiperTran(index) {
         const  slide = mySwiper.slides[index]
         const   slideLeft = slide.offsetLeft
         const   slideWidth = slide.clientWidth
         const   slideCenter = slideLeft + slideWidth / 2
            // 被点击slide的中心点
            if (slideCenter < swiperWidth / 2) {
                mySwiper.setTranslate('0')
            } else if (slideCenter > maxWidth) {
                mySwiper.setTranslate(maxTranslate)
            } else {
             const   nowTlanslate =slideCenter - swiperWidth / 2
                mySwiper.setTranslate(-nowTlanslate)
            }
            $("#product_classification_nav  .swiper-slide").removeClass('active')
            $("#product_classification_nav .swiper-slide").eq(index).addClass('active')

        }

		//检查模块权限
		checkModule();
    </script>
</body>

</html>