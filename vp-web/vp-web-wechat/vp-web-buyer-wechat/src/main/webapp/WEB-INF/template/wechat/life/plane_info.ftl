<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>确认订单</title>
		<meta name="apple-mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="black" />
		<meta http-equiv="Expires" content="0" />
		<meta http-equiv="Cache-Control" content="no-cache" />
		<meta http-equiv="Pragma" content="no-cache" />
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/plugins/font-awesome-4.7.0/css/font-awesome.min.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/card.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
		<script type="text/javascript">
			
			var $orderRefreshForm;
			var $memo;
			var $memoMsg;
			var booking= $.parseJSON("${bookingResult.replace("\'", "\\'")!"{}"}".replace(/'/g, '"'));
			function videoTest(obj){
				 var mobileValue = $("#mobile").val();
				 $("#midMobile").val(mobileValue);
			}
			var flag;
			var retryCont=0;

		    function Timer() {
		        window.clearInterval(flag)
		        var countdown = 30;
		        $('.layui-m-layerbtn').children().eq(0).html('否(' + countdown + 's)')
		        flag = setInterval(function() {
		            countdown--;
		            $('.layui-m-layerbtn').children().eq(0).html('否(' + countdown + 's)')
		            countdown == 0 ? window.clearInterval(flag) : false;
		        }, 1000)
		    }
		   function getNum(Str,isFilter) {
		　　		　//用来判断是否把连续的0去掉
		        isFilter = isFilter || false;
		        if (typeof Str === "string") {
		            // var arr = Str.match(/(0\d{2,})|([1-9]\d+)/g);
		            //"/[1-9]\d{1,}/g",表示匹配1到9,一位数以上的数字(不包括一位数).
		            //"/\d{2,}/g",  表示匹配至少二个数字至多无穷位数字
		            var arr = Str.match( isFilter ? /[1-9]\d{1,}/g : /\d{2,}/g);
		            console.log(arr);
		            if(!arr){
						return arr;
					}
		            return arr.map(function (item) {
		                //转换为整数，
		                //但是提取出来的数字，如果是连续的多个0会被改为一个0，如000---->0，
		                //或者0开头的连续非零数字，比如015，会被改为15，这是一个坑
		                // return parseInt(item);
		                //字符串，连续的多个0也会存在，不会被去掉
		                return item;
		            });
		        } else {
		            return [];
		        }
		    }
			$().ready(function() {
                <!--百事通隐藏积分支付选项-->
                var memberCompanyId="${member.companyId.id}";
                console.log("公司Id="+memberCompanyId);
                if(memberCompanyId!=null && memberCompanyId=="2003"){
                    console.log("百事通隐藏积分支付选项");
                    // $("#favourable_section_flag").hide();
                    $(".PepsiHide").hide();
                }
                <!--结束-->


				$orderRefreshForm = $("#orderRefreshForm");
				$memo = $("#memo");
				$memoMsg = $("#memoMsg");
				var $submit = $("#submit");
				var $orderForm = $("#orderForm");
				var isOverseas = "${isOverseas}"; //海外商品标识
				var $receiverIdCard = $("#receiverIdCard");
				var $idcardNoOrder = $("#idcardNoOrder");
				var $saveIdcardNo = $("#saveIdcardNo");
				var $receiverId = $("#receiverId");

				 function submitOrder() {
				    /* var message={type:"warn",content:"下单异常",data:{type:"payValidate",code:1007,message:"订单价格由1092变为1170.00"}}
					handle(message);
					return false;  */
					var url = "createVirtual.jhtml";
					$.ajax({
						url: url,
						type: "POST",
						data: $orderForm.serialize(),
						dataType: "json",
						cache: false,
						beforeSend: function() {
							$submit.prop("disabled", true);
							$submit.val("提交中");
						},
						success: function(message) {
							handle(message)
						},
						complete: function() {
							$submit.prop("disabled", false);
						}
					});
				} 
				var  handle=function(message){

					if(message.type == "success") {
						//location.href = "payment.jhtml?sn=" + message.content + "&type=0";
						location.href = "${base}/paysRedirect/payment.jhtml?sn=" + message.content + "&type=0";
					}else  if(message.type == "warn"||message.type == "error") {
						$submit.val("立即付款")
						var msg=""
						if(message.type == "warn"){
							msg=message.content
						}else{
							if(message.data&&message.data.type=="createOrder"){
								if(message.data.code==1004){
									msg="航空公司反馈下单失败";
								}else if(message.data.code==1001||message.data.code==1404
									||message.data.code==404){
									msg=message.data.message.replace("。","");
								}
							}else if(message.data&&message.data.type=="payValidate"){
								if(message.data.code==1025){
									msg="航空公司反馈下单失败";
								}else if(message.data.code==1030){
									msg='当前价位机票已售罄'
								}else if(message.data.code==1007){
									var arr =getNum(message.data.message);
									if(arr&&arr.length>1){
										/* if(retryCont==0&&arr[0]&&arr[1]&&arr[0]>arr[1]){
											retryCont=1;
											 retry();
											 return false;
										} */
										msg = "您的机票</p><p>价格由<span class='OLD'>"+arr[0]+"元</span>变为<span class='NEW'>"+arr[1]+"元</span>";
									}else{
										msg = "您的机票</p><p>"+message.data.message+"";
									}
							     
								}
							}
						}
						if(message.data&&message.data.type){
						 var trHtml = '<div class="order_goods_has_changed"> <div class="header"><p>很抱歉，' + msg + '，请重新搜索并选择其它价位</p></div><div class="list"></div> </div>';
					        layer.open({
					        	title: "提示",
					            btn: ["确定"],
					            content: trHtml,
					            success: function() {
					               /*  Timer(); */
					            },
					            yes: function(index, layero) {
					            	layer.closeAll();
					            	var flightInfo=booking.result.flightInfo[0]
					            	if(message.data.code==1004
					            			||message.data.code==1025
					            			||message.data.code==1030){
						            	var extInfo= booking.result.extInfo
						            	var vendorKey=flightInfo.flightNum+flightInfo.dptDate+flightInfo.dpt+flightInfo.arr+extInfo.tag+extInfo.wrapperId+extInfo.policyId
						            	var plane_vendor_stockout=getCookie("plane_vendor_stockout_cookie");
						    			if(plane_vendor_stockout){
						    				plane_vendor_stockout=$.parseJSON( plane_vendor_stockout);
						    			}else{
						    				plane_vendor_stockout={}
						    			}
						    			plane_vendor_stockout[vendorKey]=1
						            	addCookie("plane_vendor_stockout_cookie", JSON.stringify(plane_vendor_stockout), {path: "${base}/"});
					            	}
					            	var hrefStr = "${base}/plane/searchFlight.jhtml?dptCity="+flightInfo.dptCity+"&dpt="+flightInfo.dpt+"&arrCity="+flightInfo.arrCity+"&arr="+flightInfo.arr+"&date="+flightInfo.dptDate;
					            	[#if tripSnapshot?? ]
					            		hrefStr = hrefStr + "&applyId=${tripSnapshot.applyId}";
									[/#if]	
									[#if agentMemberId??]
										hrefStr = hrefStr + "&agentMemberId=${agentMemberId}";
									[/#if]
									[#if isPublic??]
										hrefStr = hrefStr + "&isPublic=${isPublic}";
									[/#if]
					    			location.href = hrefStr;
					            }
					        });
						}else{
							layer.open({
								content: msg,
								skin: 'msg',
								time: 2 //2秒后自动关闭
							});
						}
						
					} 
				
				}
				var retryMsg=function (){
					layer.open({
						content: "重新下单失败！",
						skin: 'msg',
						time: 2 //2秒后自动关闭
					});
					$submit.val("立即付款");
					$submit.prop("disabled", false);
				}
				/**
				 * 检查是否为购买的供应商
				 */
				var checkVendor=function (vendor){
					if(booking&&booking.result&&vendor){
						var extInfo= booking.result.extInfo;
						if(extInfo&&
								extInfo.tag==vendor.prtag&&
								extInfo.cabin==vendor.cabin&&
								extInfo.policyId==vendor.policyId&&
								extInfo.policyType==vendor.policyType&&
								extInfo.clientId==vendor.domain&&
								extInfo.wrapperId==vendor.wrapperId
								){
							return true;
						}
					}
					return false;
				}
				// 下单重试
				var retry = function(){
						$submit.prop("disabled", true);
						$submit.val("提交中");
						var dpt=booking.result.flightInfo[0].dpt;
						var arr=booking.result.flightInfo[0].arr;
						var date=booking.result.flightInfo[0].dptDate;
						var flightNum=booking.result.flightInfo[0].flightNum;
						if(flightNum != null && dpt != null && arr != null && date != null){
							// 报价搜索请求				
							$.ajax({
								url: "${base}/plane/searchPrice.jhtml",
								type: "POST",
								data: {
									dpt : dpt,
									arr : arr,
									date : date,
									flightNum : flightNum					
								},
								dataType: "json",
								cache: false,
								success: function(data) {
									if(data != null){
										console.log(data);						
										// 公用参数
										var carrier = data.carrier;
										var code = data.code;
										var depCode = data.depCode;
										var arrCode = data.arrCode;
										var bookingDate = data.date;
										var btime = data.btime;
										var selectVendor=null;
										$.each(data.vendors, function(index, vendor){		
											if(checkVendor(vendor)){
												selectVendor=vendor;
												return false;
											}
										})
										if(!selectVendor){
											retryMsg();
											return false;
										}
										var vendorStr = JSON.stringify(selectVendor);
										$.ajax({
											url: '${base}/plane/booking.jhtml',
											type: 'GET',
											data: {
												carrier : carrier,
												code : code,
												depCode : depCode,
												arrCode : arrCode,
												date : bookingDate,
												btime : btime,
												vendorStr : vendorStr
											},
											dataType: 'json',
											async: false,
											success: function(data) {
												console.log(data);
												if(data != null){
													var bookingResultStr = JSON.stringify(data);
													$("#orderForm #bookingResult").val(bookingResultStr.replace(/"/g, "'"));
													var orderJson=$("#orderForm #orderJson").val().replace(/'/g, '"');
													if(orderJson){
														var orderObj=$.parseJSON(orderJson);
														orderObj.arf=data.result.priceInfo.arf;
														orderObj.price=data.result.extInfo.barePrice;
														orderObj.tof=data.result.priceInfo.tof;
														$.each(orderObj.passengers, function(i, passenger){
															passenger.price = data.result.extInfo.barePrice;
															passenger.arf = data.result.priceInfo.arf;
															passenger.tof = data.result.priceInfo.tof;
														});
														$("#orderForm #orderJson").val( JSON.stringify(orderObj).replace(/"/g, "'"));
													}
													$submit.trigger("click");
												}else{
													retryMsg();
												}					
											}
										});
									}else{
										retryMsg();
									}
								}
							});
						}else{
							retryMsg();
						}
				}
				//输入密码事件
				$("#password").keyup(function() {
					var l_pwd = this.value.length;
					if(l_pwd >= 1 && l_pwd <= 6 && event.keyCode != 8) {
						var _input = document.getElementById("number" + l_pwd);
						_input.value = this.value.charAt(l_pwd - 1);
						if(l_pwd == 6) { //输入完成动作
							var _pwd = this.value;
							$("#paymentPop").hide();
							this.blur();
							var device = openDevice();
							if(device == "android") {
								vpshop_android.callHideSoftInput();
							}

							var $checkedIds = $("#rechargeRecords input[name='coinIds']:enabled:checked");
							$.ajax({
								url: "check_paypassword.jhtml",
								type: "POST",
								data: {
									coinIds: $("#coinIds").val(),
									paypassword: _pwd,
									whiteBarIds: $("#whiteBarIds").val()
								},
								dataType: "json",
								cache: false,
								success: function(message) {
									$(".password_section input").val("");
									if(message.type == "success") {
										submitOrder();
									} else {
										layer.open({
											content: message.content,
											skin: 'msg',
											time: 2 //2秒后自动关闭
										});
									}
								}
							});

						}
					} else if(event.keyCode == 8) { //退格键删除
						var _input = document.getElementById("number" + (l_pwd + 1));
						_input.value = '';
					}
				})




				var $payPasswordSetBtn = $("#payPasswordSetBtn");


                //保存身份证号
				$saveIdcardNo.click(function() {
					var receiverId = $receiverId.val();
					var idcardNo = $receiverIdCard.val();
					if(!idCardNoUtil.checkIdCardNo(idcardNo)) {
						layer.open({
							content: '请正确填写身份证号！',
							skin: 'msg',
							time: 2 //2秒后自动关闭
						});
						return false;
					}
					$.ajax({
						url: "saveReviceIdcard.jhtml",
						type: "POST",
						data: {
							receiverId: receiverId,
							idcardNo: idcardNo
						},
						dataType: "json",
						cache: false,
						success: function(msg) {
							if(msg == true) {
								layer.open({
									content: '身份证号码保存成功!',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
							} else {
								layer.open({
									content: '身份证号码保存失败',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
							}
						}
					});

				});

				// 订单提交
				$submit.click(function() {
					// 如果是视频卡，则要判断手机号
					 if($("#type").val() ==3){
						 var mobile = /^(((13[0-9]{1})|14[57]{1}|(15[0-9]{1})|17[378]{1}|(18[0-9]{1}))+\d{8})$/;
						 var mobileValue = $("#mobile").val();
						 if(mobileValue.length ==11 && mobile.test(mobileValue)){

						 }else{
								layer.open({
									content: '请输入正确的手机号',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
								return false;
						 }
					 }

					if(isOverseas == "true") {
						var receiverIdCard = $receiverIdCard.val();

						if(receiverIdCard == "" || receiverIdCard == null) {
							layer.open({
								content: '存在海外商品请正确填写身份证号！',
								skin: 'msg',
								time: 2 //2秒后自动关闭
							});
							$("html,body").animate({
								scrollTop: 0
							}, 300);
							return false;
						} else {
							if(!idCardNoUtil.checkIdCardNo(receiverIdCard)) {
								layer.open({
									content: '存在海外商品请正确填写身份证号！',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
								$("html,body").animate({
									scrollTop: 0
								}, 300);
								return false;
							}
							$idcardNoOrder.val(receiverIdCard);
						}
					}

					var rechargeRecordAmount = "${rechargeRecordAmount}"; //勾选了积分支付
					var whiteBarYAmount = "${whiteBarYAmount}"; //勾选了白条支付
					[#if member.companyId.isFreePay ]
						submitOrder();
					[#else]
					
					var cashPayFlag="${member.companyId.cashPayFlag}";
                   if(cashPayFlag!=null && cashPayFlag!="" && cashPayFlag==0){ //不允许使用现金支付
					   if($("#_amountPayable").val() > 0) {
                            layer.open({
                                content: '积分余额不足！',
                                skin: 'msg',
                                time: 2 //2秒后自动关闭
                            });
                            return false;
                        }
				   }
	                   
					if((rechargeRecordAmount > 0||whiteBarYAmount > 0)&&retryCont==0) { //可用积分大于0，弹出密码框
						$("#paymentPop").show().find("#password").focus();
					/* } else if(whiteBarYAmount > 0) {
						$("#paymentPop").show().find("#password").focus(); */
					} else {
						submitOrder();
					}
					[/#if]
				});
			});



			//选择收货地址页面跳转
			function selectAddress() {
				$memoMsg.val($memo.val());
				$("#orderRefreshForm").attr("action", "selectAddress.jhtml");
				$("#orderRefreshForm").submit();
			}

			//选择积分页面跳转
			function selectCoin() {
				var agentMemberId = '${agentMemberId}';
				if(agentMemberId && agentMemberId != ""){
					var url = "groupSelectCoin.jhtml?agentMemberId="+agentMemberId;
					$("#orderRefreshForm").attr("action", url);
					$("#orderRefreshForm").submit();
				}else{
					var url = "groupSelectCoin.jhtml";
					$("#orderRefreshForm").attr("action", url);
					$("#orderRefreshForm").submit();
				}

			}

			//选择积分页面跳转
			function selectWhiteBar() {
				var agentMemberId = '${agentMemberId}';
				if(agentMemberId && agentMemberId != ""){
					var url = "groupSelectWhiteBar.jhtml?agentMemberId="+agentMemberId;
					$("#orderRefreshForm").attr("action", url);
					$("#orderRefreshForm").submit();
				}else{
					var url = "groupSelectWhiteBar.jhtml";
					$("#orderRefreshForm").attr("action", url);
					$("#orderRefreshForm").submit();
				}

			}
			
			// 验证手机号
			jQuery.validator.addMethod("isTel", function(value,element) {   
			    var length = value.length;   
			    var mobile = /^(((13[0-9]{1})|14[57]{1}|(15[0-9]{1})|17[378]{1}|(18[0-9]{1}))+\d{8})$/;
			    if(this.optional(element) || (length==11 && mobile.test(value))){
			    	$("#midMobile").val(value);
			    }	    
			    return this.optional(element) || (length==11 && mobile.test(value));   
			   }, "请正确填写您的联系方式"); 
			
			$("#orderForm").validate({  
			    rules : {  
			        mobile : {  
			        	isTel:"#mobile",
			            required : true,  
			            minlength : 11 
						
			        }
			    },  
			    messages : {  
			    	mobile : {  
			            required : "请输入手机号",  
			            minlength : "确认手机不能小于11个字符",  
			            isMobile : "请正确填写您的手机号码"  
			        }
			    },  
				errorPlacement: function(error, element) {
					error.appendTo(element.parent());
				}				
			}); 
		</script>
		
		<style>
			.myOrderPage{
				background: transparent;
				margin-top: 0;
			}
			
			.fly_order_info_section header{
				margin-top: 0;
				border:none;
			}
		
		</style>
	</head>

	<body id="myOrderPage">
		<div class="myOrderPage simple common-order-info-page">
			<div class="public_top_header bg_theme">
                <span class="return_back" onclick="javascript:history.back();"></span>
				确认订单
				[#if com_person_hide_menu?? && com_person_hide_menu == '1']
					<!-- 隐藏右上角菜单目录 -->
				[#else ]
					[#include "./wechat/include/head_nav.ftl" /]
				[/#if]
			</div>

			<form id="orderRefreshForm" action="" method="post">
				<input type="hidden" name="agentMemberId" value="${agentMemberId}"/>
				<input type="hidden" name="isPublic" value="${isPublic}"/>
				[#list cartItems as cartItem]
				<input type="hidden" name="ids" value="${cartItem.id}" /> [/#list]
				<input type="hidden" name="coinIds" value="${coinIds}" id="coinIds" />
				<input type="hidden" name="whiteBarIds" value="${whiteBarIds}" id="whiteBarIds" />
				<input type="hidden" id="receiverId" name="receiverId" [#if receiver??] value="${receiver.id}" [/#if] />
				<input type="hidden" id="productIdRefresh" name="productId" value="${productId}" />
				<input type="hidden" name="couponCodeId" value="${couponCodeId}" />
				<input type="hidden" name="quantity" value="${quantity}" />
				<input type="hidden" name="pId" value="${productId}" />
				<input type="hidden" name="groupId" value="${groupId}" />
				<input type="hidden" name="toatlAmount" value="[#if order??]${order.toatlAmount}[#else]000[/#if]" />
				<input type="hidden" name="coinAmount" value="${order.coinAmount}" />
				<input type="hidden" name="amountPaid" value="${order.amountPaid}" />
				<input type="hidden" id="orderJson" name="orderJson" value="${orderJson}" />
				<input type="hidden" id="midMobile" name="mobile" value="${mobile}" />
				<input type="hidden" id="gasType" name="gasType" value="${gasType}" />
				<input type="hidden" id="gasCard" name="gasCard" value="${gasCard}" />
				<input type="hidden" id="userName" name="userName" value="${userName}" />
				<input type="hidden" id="phone" name="phone" value="${phone}" />
				<input type="hidden" id="" name="type" value="${type}" />
				<input type="hidden" id="bookingResult" name="bookingResult" value="${bookingResult}" />
				<input type="hidden" id="" name="planeTicketStr" value="${orderJson}" />
				<input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}" />
				
				<input type="hidden" name="coinTypeIdsEcho" value="${coinTypeIdsEcho}" />
				<input type="hidden" name="coinTypeIds" value="${coinTypeIds}" />
				<!-- 下面参数为了计算金额 -->
				<!-- 下面参数为了计算金额 -->
				<input type="hidden" id="price" name="price" value="${order.price}" />
				<input type="hidden" id="freight" name="freight" value="${order.freight}" />
				<input type="hidden" id="couponDiscount" name="couponDiscount" value="${order.couponDiscount}" />
				<input type="hidden" id="promotionDiscount" name="promotionDiscount" value="${order.promotionDiscount}" />
						
				[#if tripSnapshot?? ]
					<input type="hidden" name="applyId" value="${tripSnapshot.applyId}"/>
					<input type="hidden" name="tripType" value="${tripSnapshot.tripType}"/>
					<input type="hidden" name="serviceInfo" value="${tripSnapshot.serviceInfo}"/>
					<input type="hidden" name="miniPriceFlight" value="${tripSnapshot.miniPriceFlight}"/>
					<input type="hidden" name="tripReasonCode" value="${tripSnapshot.tripReasonCode}"/>
					<input type="hidden" name="tripRuleBroken" value="${tripSnapshot.tripRuleBroken}"/>
					<input type="hidden" name="tripReason" value="${tripSnapshot.tripReason}"/>
					<input type="hidden" name="tripMemo" value="${tripSnapshot.tripMemo}"/>
					<input type="hidden" name="isBreak" value="${tripSnapshot.isBreak}"/>
				[/#if]
			</form>

	
			<div class="fly_order_info_section">
				<div class="page_header_bg fly">
				[#if planeTicket.changeInfo??]
					<header>
					<div class="line1">
						<span >${planeTicket.changeInfo.startDate} ${planeTicket.changeInfo.showDptWeek}</span>
					</div>
					<div class="line2">
						<div class="from">
							<p>${planeTicket.changeInfo.startTime}</p>
							<p>${planeTicket.changeInfo.startPlace}${planeTicket.changeInfo.dptTerminal}</p>
						</div>
	
						<div class="arrow">
							  <p >${planeTicket.changeInfo.flightTimes}</p>
			                    [#if planeTicket.changeInfo.stopFlightInfo.stopType == 2]
			                   	 <p>
			                   	 [#list planeTicket.changeInfo.stopFlightInfo.stopCityInfoList as stopCity]
			                   	[#if stopCity_index==0]
			                   	 ${stopCity.stopCityName}
			                   	 [#break]
			                   	 [/#if]
			                   	 [/#list]
			                   	 </p>
			                    [/#if]
						</div>
	
						<div class="to">
							 <p>${planeTicket.changeInfo.endTime}</p><span class="plusflag" >[#if planeTicket.changeInfo.isAcrossDay]+1[/#if]</span>
                    		 <p>${planeTicket.changeInfo.endPlace}${planeTicket.changeInfo.arrTerminal}</p>
						</div>
					</div>
					<div class="line3">
						<span>${planeTicket.changeInfo.flight}${planeTicket.changeInfo.flightNo}</span>
               			 <span>${planeTicket.changeInfo.flightType}</span>
					</div>
					[#if planeTicket.changeInfo.actFlightName??]
						<div class="line4">
							<i class="fa fa-fighter-jet"></i>实际乘坐:${planeTicket.changeInfo.actFlightName}
						</div>
					[/#if]
				</header>
				[#else]
				<header>
					<div class="line1">
						<span id="showDptWeek"></span>
					</div>
					<div class="line2">
						<div class="from">
							<p>${planeTicket.dptTime}</p>
							<p>${planeTicket.dptAirport}${planeTicket.dptTerminal}</p>
						</div>
	
						<div class="arrow">
							<p>${planeTicket.flightTimes}</p>
							[#if planeTicket.stopCityName??&&planeTicket.stopCityName?length>0]	<p>经停${planeTicket.stopCityName}</p>[/#if]		
							
						</div>
	
						<div class="to">
							<p>${planeTicket.arrTime}</p><span  class="plusflag" id="overDate"></span>
							<p>${planeTicket.arrAirport}${planeTicket.arrTerminal}</p>
						</div>
					</div>
					<div class="line3">
						<span>${planeTicket.carrierName}${planeTicket.flightNum}</span>
						<span>${planeTicket.flightTypeFullName}</span>
					</div>
					[#if planeTicket.actFlightNum??]
						<div class="line4">
							<i class="fa fa-fighter-jet"></i>实际乘坐:${planeTicket.actFlightNum}
						</div>
					[/#if]
				</header>
				[/#if]
				</div>
				[#assign passengerNames = ""]
				<div class="preson_info orderPersoninfo card-rd margin-side">
			
				
					<div class="item plane_info_page">
						<label>乘机人</label>
							[#if planeTicket.changeInfo??]
								 
								[#list planeTicket.changeInfo.passengers?split(";") as passenger]
						           [#assign passengerArray=passenger?split(",")]
						           [#if passengerArray??&&passengerArray?size>1]
						           [#assign num=num+1]
						           [#assign passengerNames = passengerNames + passengerArray[0] + " "]
						            <div class="values">
										<span>${passengerArray[0]}</span>
										<span>[#list planeTicket.passengers as passenger][#if passenger.cardNo==passengerArray[1]]身份证 [#break][/#if][/#list]${passengerArray[1]}</span>
									</div>
						            [/#if]
								[/#list]
							[#else]
								[#list planeTicket.passengers as passenger]
									[#assign passengerNames = passengerNames + passenger.name + " "]
									<div class="values">
										<span>${passenger.name}</span>
										<span>[#if passenger.cardType='PP']护照[#else]身份证[/#if]${passenger.cardNo}</span>
									</div>
								[/#list]
							[/#if]
						
					</div>
					
					[#assign showInsurances = null]
					[#if planeTicket.changeInfo??]<!-- 改签的订单 -->
						[#if planeTicket.changeInfo.insurances != null]
							[#assign showInsurances = planeTicket.changeInfo.insurances]
						[/#if]
					[#else]
						[#if planeTicket.insuranceAmount>0]<!-- 犀牛保险订单 -->
							[#assign showInsurances = planeTicket.insurances]
		                [/#if]
					[/#if]
                	
                	[#if showInsurances != null && showInsurances?size > 0]
					<div class="item">
						<label>保险</label>
						[#list showInsurances as insurance]
	                	<div class="values">
							<span>${insurance.insuranceName!insurance.insuranceCode}</span>
							<span passengerNames="${passengerNames }">(${passengerNames })</span>
						</div>
									
	                	[/#list]
					</div>
					[/#if]
					
					<div class="item">
						<label>联系人</label>
						<div class="values">
							<span>${planeTicket.contactName}</span>
							<span>${planeTicket.contactMob}</span>
						</div>
					</div>
				</div>
			</div>
			
 			
 				<form id="orderForm" action="createVirtual.jhtml" method="post">
					<input type="hidden" name="agentMemberId" value="${agentMemberId}"/>
				<input type="hidden" name="isPublic" value="${isPublic}"/>
					<input type="hidden" name="idcardNoOrder" id="idcardNoOrder" value="" /> [#list cartItems as cartItem]
					<input type="hidden" name="cartItemids" value="${cartItem.id}" /> [/#list]
					<input type="hidden" name="ids" value="${coinIds}" />
					<input type="hidden" name="whiteBarIds" value="${whiteBarIds}" />
					<input type="hidden" name="cartToken" value="${cartToken}" />
					<input type="hidden" name="receiverId" [#if receiver??] value="${receiver.id}" [/#if]/>
					<input type="hidden" name="paymentMethodId" maxlength="200" value="1" />
					<!--支付方式-->
					<input type="hidden" name="shippingMethodId" maxlength="200" value="1" />
					<!--支付方式-->
					<input type="hidden" name="couponCodeId" value="${couponCodeId}" />
					<input type="hidden" name="groupId" value="${groupId}" id="groupId" />
					<input type="hidden" id="productIdOrder" name="productId" value="${productId}" />
					<input type="hidden" id="quantity" name="quantity" value="${quantity}" />
					<input type="hidden" id="orderJson" name="orderJson" value="${orderJson}" />
					<input type="hidden" id="type" name="type" value="${type}" />
					<input type="hidden" id="bookingResult" name="bookingResult" value="${bookingResult}" />
					<input type="hidden" id="" name="planeTicketStr" value="${orderJson}" />
					<input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}" />
					
					[#if tripSnapshot?? ]
						<input type="hidden" name="applyId" value="${tripSnapshot.applyId}"/>
					<input type="hidden" name="tripType" value="${tripSnapshot.tripType}"/>
					<input type="hidden" name="serviceInfo" value="${tripSnapshot.serviceInfo}"/>
					<input type="hidden" name="miniPriceFlight" value="${tripSnapshot.miniPriceFlight}"/>
					<input type="hidden" name="tripReasonCode" value="${tripSnapshot.tripReasonCode}"/>
					<input type="hidden" name="tripRuleBroken" value="${tripSnapshot.tripRuleBroken}"/>
					<input type="hidden" name="tripReason" value="${tripSnapshot.tripReason}"/>
					<input type="hidden" name="tripMemo" value="${tripSnapshot.tripMemo}"/>
					<input type="hidden" name="isBreak" value="${tripSnapshot.isBreak}"/>
					[/#if]
					<input type="hidden" name="agentMemberId" value="${agentMemberId}"/>
 						
				</form>
			</section> 

			[#if companyPaymentUrl?? && companyPaymentUrl != ""]
			<section class="amount_section card-rd margin-side">
				<p class="item flexbox align-items-c justify-content-space-between">
					<label>数量</label>
					<span>共${order.quantity}件</span>
				</p>
				<p class="item flexbox align-items-c justify-content-space-between">
					<label>商品金额</label>
					[#if serviceCharge!=null]
                         <span>${currency(order.price-serviceCharge, true, false)}</span>
					[#else]
					     <span>${currency(order.price, true, false)}</span>
					[/#if]

				</p>
				[#if serviceCharge!=null]
				<p class="item flexbox align-items-c justify-content-space-between">
					<label>服务费</label>
					<span>${currency(serviceCharge, true, false)}</span>
				</p>
				[/#if]
				[#--<p [#if member.companyId?? && member.companyId.cashPayFlag?? && member.companyId.cashPayFlag == 0] style="display: none;" [/#if]>--]
					[#--实付款--]
					[#--<span>-${currency(order.amount, true, false)}</span>--]
				[#--</p>--]
				<input type="hidden" id="_amountPayable" value="${order.amountPayable + order.fee }" />
			</section>
			[#else]


			[#if com_person_third_pay?? && com_person_third_pay == '1']
                <!-- 有第三方支付 -->
			[#else ]
                [#--<section class="favourable_section" id="favourable_section_flag">--]
				[#--[#if member.companyId.sourceFlag == null || member.companyId.sourceFlag==""]--]
                    [#--<div class="lines_Section_all fli_link_line" onclick="selectCoin()">--]
                        [#--<a href="javascript:void(0)"  class="fli_link_line">--]
                            [#--<label>积分支付</label>--]
                            [#--<div class="right_text red">--]
								[#--[#if order.coinAmount > 0]--]
								   [#---${coinConvert(order.coinAmount, coinShowRate!1)}--]
								[#--[#else]--]
									[#--[#list coins as coin] [#if coin.coinTypeId.isCredit]--]
								[#--<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list]--]
									[#--[#if thirdCoin??]--]
									[#--<span class="like_btn" thirdCoinAmount="${thirdCoinAmount }">${thirdCoin.coinName?substring(0,1)}${thirdCoin.amount}</span>--]
									[#--[/#if]--]
								[#--[/#if]--]
							[#--</div>--]
                        [#--</a>--]
                    [#--</div>--]

                    [#--<div class="lines_Section_all" [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>--]
                        [#--<a href="javascript:void(0)" onclick="selectWhiteBar()" class="fli_link_line">--]
                            [#--白条支付 [#list whiteBarCoins as coin] [#if coin.coinTypeId.isCredit && coin.enabledFlag && coin.isFreeze]--]
                            [#--<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list] [#if order.whiteBarAmount gte 0]--]
                            [#--<div class="right_text red">-${coinConvert(order.whiteBarAmount, coinShowRate!1)}</div>--]
						[#--[#else]--]
                            [#--<div class="right_text gray">未使用</div>--]
						[#--[/#if]--]
                        [#--</a>--]
                    [#--</div>--]
				[#--[/#if]--]
                [#--</section>--]
			[/#if]





			<section class="amount_section card-rd margin-side">
				<p class="item flexbox align-items-c justify-content-space-between">
					<label>数量</label>
					<span class="right-text">共${order.quantity}件</span>
				</p>
				[#if productShowRate??]
					<p class="item flexbox align-items-c justify-content-space-between">
                        <label>商品金额</label>
						[#if serviceCharge!=null]
							 <span class="right-text">${coinConvert(order.price-serviceCharge, productShowRate, false, false)}</span>
						[#else]
							 <span class="right-text">${coinConvert(order.price, productShowRate, false, false)}</span>
						[/#if]
					</p>
					[#if serviceCharge!=null]
						<p class="item flexbox align-items-c justify-content-space-between">
                            <label>服务费</label>
							<span class="right-text">${coinConvert(serviceCharge, productShowRate, false, false)}</span>
						</p>
					[/#if]
				<!--百事通：隐藏积分支付和白条抵扣选项-->
				[#if !(com_person_third_pay?? && com_person_third_pay == '1')]
					<!-- 有第三方支付 -->
					[#if member.companyId.sourceFlag == null || member.companyId.sourceFlag==""]
							<div class="lines_Section_all fli_link_line PepsiHide" onclick="selectCoin()">
								[#--<a href="javascript:void(0)"  class="fli_link_line">--]
                                    <label>积分支付</label>
									<div class="right_text red">
										[#if order.coinAmount > 0]
											-${coinConvert(order.coinAmount, coinShowRate!1)}
										[#else]
											[#list coins as coin]
												[#if coin.coinTypeId.isCredit]
												<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list]
											[#if thirdCoin??]
												<span class="like_btn" thirdCoinAmount="${thirdCoinAmount }">${thirdCoin.coinName?substring(0,1)}${thirdCoin.amount}</span>
											[/#if]
										[/#if]
									</div>
								[#--</a>--]
							</div>

							<div class="lines_Section_all fli_link_line PepsiHide" onclick="selectWhiteBar()" [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>
								[#--<a href="javascript:void(0)"  class="fli_link_line">--]
                                    <label> 白条支付 </label>
									<div class="right_text red">
										[#if order.whiteBarAmount > 0]
											-${coinConvert(order.whiteBarAmount, coinShowRate!1)}
										[#else]
											[#list whiteBarCoins as coin] [#if coin.coinTypeId.isCredit && coin.enabledFlag && coin.isFreeze]
											<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list]
										[/#if]
									</div>
								[#--</a>--]
							</div>
					[/#if]
				[/#if]


				<p class="item flexbox align-items-c justify-content-space-between">
					<label>优惠券抵扣</label>
					<span  class="right-text">-${coinConvert(order.couponDiscount, productShowRate, false, false)}</span>
				</p>
				[#else]
				<p class="item flexbox align-items-c justify-content-space-between">
					<label>商品金额</label>
					[#if serviceCharge!=null]
                         <span  class="right-text">${currency(order.price-serviceCharge, true, false)}</span>
					[#else]
					     <span  class="right-text">${currency(order.price, true, false)}</span>
					[/#if]
				</p>
				[#if serviceCharge!=null]
				<p class="item flexbox align-items-c justify-content-space-between">
					<label>服务费</label>
					<span  class="right-text">${currency(serviceCharge, true, false)}</span>
				</p>
				[/#if]
					[#if member.companyId.sourceFlag == null || member.companyId.sourceFlag==""]
							<div class="lines_Section_all fli_link_line PepsiHide" onclick="selectCoin()">
                                [#--<a href="javascript:void(0)" onclick="selectCoin()" class="fli_link_line">--]
                                    <label> 积分支付 </label>
									<div class="right_text red">
										[#if order.coinAmount > 0]
											-${coinConvert(order.coinAmount, coinShowRate!1)}
										[#else]
											[#list coins as coin]
												[#if coin.coinTypeId.isCredit]
												<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if]
											[/#list]
											[#if thirdCoin??]
											<span class="like_btn" thirdCoinAmount="${thirdCoinAmount }">${thirdCoin.coinName?substring(0,1)}${thirdCoin.amount}</span>
											[/#if]
										[/#if]
									</div>
                                [#--</a>--]
                            </div>
							[#if order.whiteBarAmount > 0 || whiteBarCoins??&&whiteBarCoins?size>0]
								<div class="lines_Section_all fli_link_line PepsiHide" onclick="selectWhiteBar()" [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>
									<!--<a href="javascript:void(0)" onclick="selectWhiteBar()" class="fli_link_line">-->
										<label>白条支付</label>
									   <div class="right_text red">
											[#if order.whiteBarAmount > 0]
												-${coinConvert(order.whiteBarAmount, coinShowRate!1)}
											[#else ]
												[#list whiteBarCoins as coin] [#if coin.coinTypeId.isCredit && coin.enabledFlag && coin.isFreeze]
											   <span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list]
											[/#if]
										</div>
									[#--</a>--]
								</div>
							[/#if]
					[/#if]
				<p class="item flexbox align-items-c justify-content-space-between">
					<label>优惠券抵扣</label>
					<span  class="right-text">-${currency(order.couponDiscount, true, false)}</span>
				</p>
				[/#if]
				[#--<p class="item flexbox align-items-c justify-content-space-between" [#if member.companyId?? && member.companyId.cashPayFlag?? && member.companyId.cashPayFlag == 0] style="display: none;" [/#if]>--]
					[#--<label>实付款</label>--]
					[#--<span  class="right-text">-${currency(order.amount, true, false)}</span>--]
				[#--</p>--]
				<input type="hidden" id="_amountPayable" value="${order.amountPayable}" />
			</section>
			[/#if]
            <div class="bottom info-footer flexbox align-items-c justify-content-space-between">
                <div class="total">
						[#if productShowRate??]
							[#assign floatValue =coinConvert(order.amountPayable,productShowRate)?number]
						[#else]
							[#assign floatValue = order.amountPayable?number]
						[/#if]
                [#--[#assign floatValue = order.amountPayable?number]--]
                [#assign integerPart = floatValue?floor]
                [#assign decimalPart = (floatValue * 100)?round % 100 / 100]
                    <span class="money-total" id="money-total">
						[#if productShowRate??]
							<i class="iconfontAli icon-ali-jifen"></i>
						[#else]
					  		￥
						[/#if]
					 <span>${integerPart}</span>${decimalPart?string(".00")}</span>
                    <p>实付款</p>
                </div>
                <input type="button" name="" id="submit" value="立即付款" class="btn_submit_long" />
            </div>
			[#--<div class="bottom">--]
				[#--<input type="button" name="" id="submit" value="立即付款" class="btn_submit_long" />--]
			[#--</div>--]
		</div>

		[#include "./wechat/include/set_payPasswords.ftl" /]

		<script>
			[#include "./wechat/include/categoryCheck.ftl" /]
			// 星期展示
			var dptDate = "${planeTicket.dptDate}";
			var arrDate = "${planeTicket.arrDate}";
			var a = new Array("日", "一", "二", "三", "四", "五", "六");  
			var dptWeek = new Date(dptDate).getDay();  
			var dptStr = "星期"+ a[dptWeek]; 
			$("#showDptWeek").text(dptDate + " " + dptStr);
			if(dptDate != arrDate){
				$("#overDate").text("+1");
			}
			
			var maxstrlen = 30;
			/* checkWord($("#memo")[0]);
			//检查文本
			function checkWord(c) {
				len = maxstrlen;
				var str = c.value;
				myLen = getStrleng(str);
				var wck = document.getElementById("lengthLeft");
				if(myLen > len * 2) {
					c.value = str.substring(0, i - 1);
				}
				wck.innerHTML = Math.floor((len * 2 - myLen) / 2) > 0 ? Math.floor((len * 2 - myLen) / 2) : 0;
			} */
			//计算长度
			function getStrleng(str) {
				myLen = 0;
				i = 0;
				for(;
					(i < str.length) && (myLen <= maxstrlen * 2); i++) {
					if(str.charCodeAt(i) > 0 && str.charCodeAt(i) < 128)
						myLen++;
					else
						myLen += 2;
				}
				return myLen;
			}
		</script>

	</body>

</html>