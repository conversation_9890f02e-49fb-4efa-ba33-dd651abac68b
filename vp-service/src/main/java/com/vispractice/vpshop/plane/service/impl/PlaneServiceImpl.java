package com.vispractice.vpshop.plane.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.jms.Destination;
import javax.persistence.NoResultException;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vispractice.vpshop.CommonAttributes;
import com.vispractice.vpshop.Filter;
import com.vispractice.vpshop.Message;
import com.vispractice.vpshop.Page;
import com.vispractice.vpshop.Pageable;
import com.vispractice.vpshop.api.exception.ApiException;
import com.vispractice.vpshop.api.plane.PlaneApi;
import com.vispractice.vpshop.api.plane.PlaneConfig;
import com.vispractice.vpshop.api.plane.bean.ApplyChangeRequest;
import com.vispractice.vpshop.api.plane.bean.ApplyRefundRequest;
import com.vispractice.vpshop.api.plane.bean.BookingPriceInfo;
import com.vispractice.vpshop.api.plane.bean.ChangeInfo;
import com.vispractice.vpshop.api.plane.bean.ChangePayRequest;
import com.vispractice.vpshop.api.plane.bean.Detail;
import com.vispractice.vpshop.api.plane.bean.PackageInfo;
import com.vispractice.vpshop.api.plane.bean.Passenger;
import com.vispractice.vpshop.api.plane.bean.PlaneTicket;
import com.vispractice.vpshop.api.plane.bean.TgqReason;
import com.vispractice.vpshop.api.plane.bean.TgqRequest;
import com.vispractice.vpshop.api.plane.bean.Vendor;
import com.vispractice.vpshop.api.plane.request.BookingRequest;
import com.vispractice.vpshop.api.plane.request.CreateOrderRequest;
import com.vispractice.vpshop.api.plane.request.LuggageAllowanceRequest;
import com.vispractice.vpshop.api.plane.request.PayRequest;
import com.vispractice.vpshop.api.plane.request.SearchFlightRequest;
import com.vispractice.vpshop.api.plane.request.SearchQuoteRequest;
import com.vispractice.vpshop.api.plane.response.ApplyChangeResponse;
import com.vispractice.vpshop.api.plane.response.ApplyRefundResponse;
import com.vispractice.vpshop.api.plane.response.BookingResult;
import com.vispractice.vpshop.api.plane.response.ChangePayrResponse;
import com.vispractice.vpshop.api.plane.response.ChangeSearchResponse;
import com.vispractice.vpshop.api.plane.response.CreateOrderResponse;
import com.vispractice.vpshop.api.plane.response.LuggageAllowanceResponse;
import com.vispractice.vpshop.api.plane.response.OrderDetailResponse;
import com.vispractice.vpshop.api.plane.response.PayResponse;
import com.vispractice.vpshop.api.plane.response.PayValidateResponse;
import com.vispractice.vpshop.api.plane.response.RefundSearchResponse;
import com.vispractice.vpshop.api.plane.response.RefundSearchResponseResult;
import com.vispractice.vpshop.api.plane.response.SearchFlightResponse;
import com.vispractice.vpshop.api.plane.response.SearchFlightResponse.FlightInfo;
import com.vispractice.vpshop.api.plane.response.SearchQuoteResponse;
import com.vispractice.vpshop.api.plane.response.SearchQuoteResponse.Result;
import com.vispractice.vpshop.api.plane.utils.JacksonUtil;
import com.vispractice.vpshop.api.tempus.plane.TempusConstants;
import com.vispractice.vpshop.dao.CoinLogDao;
import com.vispractice.vpshop.dao.JedisDao;
import com.vispractice.vpshop.dao.OrderDao;
import com.vispractice.vpshop.dao.OrderItemDao;
import com.vispractice.vpshop.dao.PaymentDao;
import com.vispractice.vpshop.dao.SnDao;
import com.vispractice.vpshop.dao.payment.PaymentRecordsHandler;
import com.vispractice.vpshop.entity.Cart;
import com.vispractice.vpshop.entity.CartItem;
import com.vispractice.vpshop.entity.Company;
import com.vispractice.vpshop.entity.Member;
import com.vispractice.vpshop.entity.Order;
import com.vispractice.vpshop.entity.Order.OrderStatus;
import com.vispractice.vpshop.entity.Order.PaymentStatus;
import com.vispractice.vpshop.entity.OrderItem;
import com.vispractice.vpshop.entity.Payment;
import com.vispractice.vpshop.entity.PaymentMethod;
import com.vispractice.vpshop.entity.Product;
import com.vispractice.vpshop.entity.Receiver;
import com.vispractice.vpshop.entity.RechargeRecord;
import com.vispractice.vpshop.entity.ShippingMethod;
import com.vispractice.vpshop.entity.Sn;
import com.vispractice.vpshop.entity.Supplier;
import com.vispractice.vpshop.entity.SysCode;
import com.vispractice.vpshop.entity.TripSnapshot;
import com.vispractice.vpshop.insurance.service.InsuranceService;
import com.vispractice.vpshop.jd.json.FastJsonUtils;
import com.vispractice.vpshop.plane.service.PlaneFacadeService;
import com.vispractice.vpshop.plane.service.PlaneService;
import com.vispractice.vpshop.service.CartService;
import com.vispractice.vpshop.service.CoinAmountService;
import com.vispractice.vpshop.service.CoinLogService;
import com.vispractice.vpshop.service.CoinTypeService;
import com.vispractice.vpshop.service.MemberService;
import com.vispractice.vpshop.service.MessageService;
import com.vispractice.vpshop.service.OrderItemService;
import com.vispractice.vpshop.service.OrderService;
import com.vispractice.vpshop.service.PaymentMethodService;
import com.vispractice.vpshop.service.ProductService;
import com.vispractice.vpshop.service.RechargeRecordService;
import com.vispractice.vpshop.service.ShippingMethodService;
import com.vispractice.vpshop.service.SmsService;
import com.vispractice.vpshop.service.SupplierService;
import com.vispractice.vpshop.service.SysCodeService;
import com.vispractice.vpshop.service.TmplmsgService;
import com.vispractice.vpshop.trip.service.TripSnapshotService;
import com.vispractice.vpshop.util.CommUtil;
import com.vispractice.vpshop.util.StringUtil;
import com.vispractice.vpshop.vo.CreateOrderMsg;
import com.vispractice.vpshop.vo.PlaneInsuranceVo;

/**
 * 类 编 号：UI_PU010401_PlaneServiceImpl
 * 类 名 称：PlaneServiceImpl.java
 * 内容摘要：
 * 完成日期：2017年12月22日 上午10:34:54
 * 编码作者: 胡辉
 */
@Transactional
@Service("planeServiceImpl")
public class PlaneServiceImpl implements PlaneService{
	private static final Logger LOGGER = LoggerFactory.getLogger(PlaneServiceImpl.class);

	/**
	 * 机票价格规则 加
	 */
	private static final String PLANE_ADD = "add";
	/**
	 * 机票价格规则 乘
	 */
	private static final String PLANE_MULTIPLY = "mul";
	/**
	 * 机票价格规则key名称
	 */
	private static final String PLANE_PRICE_DISCIPLINE_KEY_NAME = "plane_price_discipline_key";
	/**
	 * 机票排除航空公司
	 */
	private static final String PLANE_EXCLUDE_CARRIER_KEY_NAME = "plane_exclude_carrier_key";
	/** 分隔符 */
	private static final String SEPARATOR = ",";
	/**
	 * 获取价格区间表达式
	 */
	private static final String PRICE_DISCIPLINE_RGEX = "\\[(.*?)\\]";
	/**
	 * 获取加价格表达式
	 */
	private static final String PRICE_ADD_RGEX = "add\\((.*?)\\)";
	/**
	 * 获取乘比例表达式
	 */
	private static final String PRICE_MUL_RGEX = "mul\\((.*?)\\)";
	
	private static final String PLANE_REFUND_CACHE = "plane_refund_cache_";

	@Resource(name = "planeApi")
	private PlaneApi planeApi;
	@Resource(name = "orderDaoImpl")
	private OrderDao orderDao;
	@Resource(name = "snDaoImpl")
	private SnDao snDao;
	@Resource
	private CoinLogDao coinLogDao;
	@Resource
	private SysCodeService sysCodeService;
	@Resource
	private SmsService smsService;
	@Resource(name = "memberServiceImpl")
	private MemberService memberService;
	@Resource(name = "supplierServiceImpl")
	private SupplierService supplierService;
	@Resource(name = "productServiceImpl")
	private ProductService productService;
	@Resource(name = "cartServiceImpl")
	private CartService cartService;
	@Resource
	private OrderItemDao orderItemDao;
	@Resource(name = "rechargeRecordServiceImpl")
	private RechargeRecordService rechargeRecordService;
	@Resource(name = "coinTypeServiceImpl")
	private CoinTypeService coinTypeService;
	@Resource(name = "paymentMethodServiceImpl")
	private PaymentMethodService paymentMethodService;
	@Resource(name = "shippingMethodServiceImpl")
	private ShippingMethodService shippingMethodService;
	@Resource(name = "orderServiceImpl")
	private OrderService orderService;
	@Resource(name = "tmplmsgServiceImpl")
	private TmplmsgService tmplmsgService;
	@Resource
	private CoinAmountService coinAmountService;
	@Autowired
	private OrderItemService orderItemService;
	@Autowired
	private PlaneFacadeService planeFacadeService;
	@Autowired
	private PaymentDao paymentDao;
	@Autowired
	@Qualifier("queueDestination")
	private Destination destination;
	@Resource(name = "messageServiceImpl")
	private MessageService messageService;
	@Value("${qunar.change.callbackUrl}")
	private String callbackUrl;
	@Value("${qunar.change.pay.callbackUrl}")
	private String payCallbackUrl;

	@Resource
	private PaymentRecordsHandler paymentRecordsHandler;
	@Resource
	private CoinLogService coinLogService;

	@Resource
	private InsuranceService insuranceService;

	@Resource
	private TripSnapshotService tripSnapshotService;
	
	@Resource(name = "jedisDao")
	private JedisDao jedisDao;

	/**
	 * 方法:航班搜索
	 *
	 * autor :胡辉
	 *
	 * @param  dpt        起飞机场三字码
	 * @param  arr        到达机场三字码
	 * @param  date       起飞日期          eg : 2015-12-21
	 *
	 * @return
	 */
	public List<FlightInfo> searchFlight(String dpt, String arr, String date){
		// 请求对象封装
		SearchFlightRequest request = new SearchFlightRequest();
		// 航班信息集合
		List<FlightInfo> flightInfos = new ArrayList<FlightInfo>();
		try {
			// 设置请求参数
			request.setArr(arr);
			request.setDpt(dpt);
			request.setDate(date);
			request.setEx_track(PlaneConfig.EX_TRACK);
			// 调用接口，获得返回对象
			SearchFlightResponse searchFlightResponse = planeApi.searchFlightPage(request);
			if(searchFlightResponse != null){
				com.vispractice.vpshop.api.plane.response.SearchFlightResponse.Result result = searchFlightResponse.getResult();
				if(result != null){
					flightInfos = result.getFlightInfos();
					Company company=memberService.getCurrent().getCompanyId();
					Supplier supplier=supplierService.findByProperties(Filter.eq("isOweOrder", 10),Filter.eq("enabledFlag", true));
					Iterator<FlightInfo> it=flightInfos.iterator();
					while(it.hasNext()){
						FlightInfo flightInfo =  it.next();
						//获取排除航空公司配置
						SysCode sysCode=sysCodeService.findbyCode(SysCode.SYS_GROUP, PLANE_EXCLUDE_CARRIER_KEY_NAME);
						//删除配置的航班
						if(sysCode!=null
								&& StringUtils.isNotBlank(sysCode.getValue())
								&&sysCode.getValue().indexOf(SEPARATOR+flightInfo.getCarrier()+SEPARATOR)!=-1){
							it.remove();
						}else{
							flightInfo.setBarePrice(calculatePrice(new BigDecimal(flightInfo.getBarePrice()),company.getSupplierPriceRuleBySupplierId(supplier.getId())).intValue());
						}


					}

				}
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			LOGGER.error("查询航班异常：", e);
			e.printStackTrace();
		}
		return flightInfos;
	}

	/**
	 * 方法:报价搜索
	 *
	 * autor :胡辉
	 *
	 * @param  dpt        起飞机场三字码
	 * @param  arr        到达机场三字码
	 * @param  date       起飞日期          eg : 2015-12-21
	 * @param  flightNum  航班号
	 *
	 * @return
	 */
	public Result searchPrice(String dpt, String arr, String date, String flightNum) {
		// 请求对象封装
		SearchQuoteRequest request = new SearchQuoteRequest();
		// 设置请求参数
		request.setArr(arr);
		request.setDpt(dpt);
		request.setDate(date);
		request.setFlightNum(flightNum);
		request.setEx_track(PlaneConfig.EX_TRACK);
		// 调用接口，获得返回对象
		SearchQuoteResponse searchQuoteResponse = planeApi.searchQuote(request);
		if(searchQuoteResponse != null){
			Result result = searchQuoteResponse.getResult();
			try{
				if(result!=null){
					List<Vendor> vendors=result.getVendors();
					Company company=memberService.getCurrent().getCompanyId();
					Supplier supplier=supplierService.findByProperties(Filter.eq("isOweOrder", 10),Filter.eq("enabledFlag", true));
					for(Vendor vendor:vendors){
						vendor.setCost(vendor.getBarePrice());
						vendor.setBarePrice(calculatePrice(new BigDecimal(vendor.getBarePrice()),company.getSupplierPriceRuleBySupplierId(supplier.getId())).intValue());
					}
				}
			}catch (Exception e){
				LOGGER.error("报价搜索异常：", e);
				e.printStackTrace();
			}
			return result;
		}
		return null;
	}

	/**
	 * 方法:预约bk
	 *
	 * autor :胡辉
	 *
	 * @param
	 *
	 * @return
	 */
	public String booking(BookingRequest bookingRequest){
		// 调用接口，获得返回对象
		String response = planeApi.booking(bookingRequest);
		// 预约接口返回数据，封装对象
		BookingResult bookingResult = JacksonUtil.decode(response, BookingResult.class);
		if(bookingResult != null){
			if(PlaneConfig.SUCCESS_CODE == bookingResult.getCode() && "BOOKING_SUCCESS".equals(bookingResult.getResult().getBookingStatus())){
				return response;
			}
		}
		return null;
	}

	/**
	 * 机票下单
	 *
	 * @param order 订单
	 * @param planeTicket 机票对象
	 * @param bookingResult 预约返回对象
	 *
	 * @return
	 */
	public CreateOrderMsg orderPayForPlane(Order order, String planeTicket, String bookingResult){
		CreateOrderMsg resultmsg = new CreateOrderMsg();
		// return new CreateOrderMsg(null, true, "无机票订单下单成功");
		// Product product = order.getOrderItems().get(0).getProduct();	
		LOGGER.info("进入生成订单调接口的方法");
		//记录下单到支付的错误信息
		Map<String,Object> errMsg=new HashMap<String,Object>();
		// 生单请求封装
		CreateOrderRequest createOrderRequest = new CreateOrderRequest();
		// 设置联系人信息
		PlaneTicket planeTicketObject = JSON.parseObject(planeTicket, PlaneTicket.class);
		//检查乘客是否在同样的航班同样的时间已有机票订单
		Message msg=checkRepetitionFlight(planeTicketObject);
		if(msg.getType().equals(Message.Type.error)){
			errMsg.put("code", 1001);
			errMsg.put("type","createOrder");
			errMsg.put("message",msg.getContent());
			return new CreateOrderMsg(null, false, JSON.toJSONString(errMsg));
		}
		planeTicketObject.setFlightInfo(null);
		planeTicketObject.setFlightInfoOthe(null);
		createOrderRequest.setContact(planeTicketObject.getContactName());
		createOrderRequest.setContactMob(planeTicketObject.getContactMob());
		// bookingResult此时生单接口时终于用上了
		LOGGER.info("planeServiceImpl中的bookingResult" + bookingResult);
		createOrderRequest.setBookingResult(bookingResult);
		createOrderRequest.setAddress("");
		createOrderRequest.setNeedXcd(false);

		/*if(errMsg!=null){
			errMsg.put("code", 1004);
			errMsg.put("type","createOrder");
			errMsg.put("code", 1030);
			errMsg.put("type","payValidate");
			errMsg.put("code", 1007);
			errMsg.put("type","payValidate");
			errMsg.put("message", "价格由2300变更为2200");
			return new CreateOrderMsg(null, false, JSON.toJSONString(errMsg));
			resultmsg = new CreateOrderMsg(null, true, "机票订单下单成功并且已经支付");
			return resultmsg;
		}*/
		// 调用生单接口
		String response = planeApi.createOrder(createOrderRequest, planeTicketObject.getPassengers());
		CreateOrderResponse createOrderResponse = JacksonUtil.decode(response, CreateOrderResponse.class);
		if(createOrderResponse != null){
			errMsg.put("code", createOrderResponse.getCode());
			errMsg.put("type","createOrder");
			errMsg.put("message", createOrderResponse.getMessage());
			if(PlaneConfig.SUCCESS_CODE == createOrderResponse.getCode() && PlaneConfig.ORDER_STATUS_SUCCESS_CODE == createOrderResponse.getResult().getStatus()){
				// 保存去哪儿订单号以及待支付价格
				// 支付接口需要传递
				int orderId = createOrderResponse.getResult().getId();
				// 订单查询接口需要用到
				String orderNo = createOrderResponse.getResult().getOrderNo();
				// 待支付总价
				int noPayAmount = createOrderResponse.getResult().getNoPayAmount();
				//福利plus收取手续费
				int count=(planeTicketObject.getPassengers().size()-planeTicketObject.getChilSum());
				//默认区间服务费
				BigDecimal fee=new BigDecimal((planeTicketObject.getFee())*count+(planeTicketObject.getChildFee()*planeTicketObject.getChilSum()));
				BigDecimal sericeFee = BigDecimal.ZERO;//企业加价服务费
				if(planeTicketObject.getServiceCharge()!=null) {
					sericeFee=sericeFee.add(planeTicketObject.getServiceCharge());
				}
				if(planeTicketObject.getChildServiceCharge()!=null) {
					sericeFee=sericeFee.add(planeTicketObject.getChildServiceCharge());
				}
				fee.add(sericeFee);
				//最终支付总价，需与收取价格进行对比，支付前校验会拿此价格校验
				//如果我们收取的机票金额比去哪儿机票金额高则通过，反之提示价格变动

				LOGGER.info("福利plus收取机票价格：{}  去哪儿机票收取价格：{} 福利PLUS收取手续费{}", order.getToatlAmount(), noPayAmount, fee);
				//如果手续费小于零，或者支付价格小于零 
				if(fee.compareTo(BigDecimal.ZERO)==-1
						||order.getToatlAmount().compareTo(BigDecimal.ZERO)==-1){
					LOGGER.error("参数非法，非法操作！手续费fee={} 订单金额：{}", fee, order.getToatlAmount());
					return new CreateOrderMsg(null, false, null);
				}
				//总价减去福利平台收取手续费对比需要支付的金额
				if(order.getToatlAmount().subtract(fee).compareTo(new BigDecimal(noPayAmount))==-1){
					errMsg.put("code", 1007);
					errMsg.put("type","payValidate");
					errMsg.put("message", "价格由"+order.getToatlAmount().setScale(0, BigDecimal.ROUND_HALF_UP)+"变更为"+new BigDecimal(noPayAmount).add(fee));
					return new CreateOrderMsg(null, false, JSON.toJSONString(errMsg));
				}
				//订单成本
				BigDecimal jdPrice = new BigDecimal(noPayAmount);
				order.setPhone(planeTicketObject.getContactMob());
				order.setConsignee(planeTicketObject.getContactName());
				order.setJdOrderId(orderNo);                 //订单详情
				order.setJdPrice(jdPrice);
				// 设置乘机人状态(1:出票中)
				String passengerNames="";
				List<Passenger> passengers = planeTicketObject.getPassengers();
				for(Passenger passenger : passengers){
					if(!"".equals(passengerNames)){
						passengerNames+=",";
					}
					passengerNames+=passenger.getName();
					passenger.setPassengerStatus(1);
				}
				//设置购买数量
				planeTicketObject.setQty(passengers.size());
				planeTicketObject.setQunerOrderId(String.valueOf(orderId)); //支付
				OrderItem oi = getPlaneItem(order);//order.getOrderItems().get(0);
				oi.setVirtualProductOrderInfo(JSONObject.toJSONString(planeTicketObject));
				oi.setFullName(oi.getFullName()+"("+passengerNames+")");
				oi.setName(oi.getFullName());

				//订单成本
				//BigDecimal jdPrice = oi.getPrice().subtract(fee);//原有逻辑，商品加个减去手续费
//				try{
//					int totalPrice = planeTicketObject.getTotalPrice();//原有逻辑，商品加个减去手续费
//					if(totalPrice > 0 && sericeFee.compareTo(BigDecimal.ZERO)==0){//航司返回的成本价
//						jdPrice = BigDecimal.valueOf(totalPrice).subtract(fee);//使用航司返回的成本价
//					}
//					if(StringUtils.isNotBlank(planeTicketObject.getInsuranceOrderId())){//犀牛保险
//						jdPrice = jdPrice.subtract(planeTicketObject.getInsuranceAmount()).setScale(0,BigDecimal.ROUND_HALF_UP);
//					}
//				} catch(Exception e){
//					LOGGER.error("企业加价计算异常:", e);
//				}
				//设置成本价
				oi.setJdPrice(jdPrice.divide(BigDecimal.valueOf(oi.getQuantity()),2, BigDecimal.ROUND_HALF_UP));

				if(order.getPaymentStatus().equals(PaymentStatus.partialPayment)||order.getPaymentStatus().equals(PaymentStatus.unpaid)){
					LOGGER.info("{}机票订单下单成功部分支付，待支付现金：{}", order.getSn(), order.getAmountPayable());
					return new CreateOrderMsg(order, true, "机票订单下单成功部分支付");
				}
				//return pay(planeTicketObject, order);
				return new CreateOrderMsg(order, true, "机票订单下单成功");
			}
		}

		// 如果没有下单成功，返回下单失败
		resultmsg = new CreateOrderMsg(null, false, JSON.toJSONString(errMsg),3);
		return resultmsg;
	}
	/**
	 * 支付
	 * @param planeTicketObject 下单信息
	 * @param order 订单
	 * @return
	 */
	public CreateOrderMsg pay(PlaneTicket planeTicketObject,Order order){
		//记录下单到支付的错误信息
		Map<String,Object> errMsg=new HashMap<String,Object>();
		// 调订单支付接口
		PayRequest payRequest = new PayRequest();
		// 设置支付接口的请求参数
		payRequest.setClientSite(planeTicketObject.getClientSite());
		payRequest.setOrderId(planeTicketObject.getQunerOrderId());
		PayValidateResponse payValidateResponse = planeApi.pay(payRequest);
		LOGGER.info("机票订单付款结果：{}", payValidateResponse);
		if(payValidateResponse==null){
			errMsg.put("code", -1);
			errMsg.put("type","payValidate");
			errMsg.put("message", "支付校验接口调用失败");
			order.setSupplierMemo(errMsg.get("message")+"");
			return new CreateOrderMsg(null, false, JSON.toJSONString(errMsg));
		}
		if(PlaneConfig.SUCCESS_CODE != payValidateResponse.getCode()){
			errMsg.put("code", payValidateResponse.getCode());
			errMsg.put("message", payValidateResponse.getMessage());
			errMsg.put("type","payValidate");
			order.setSupplierMemo(errMsg.get("message")+"");
			return new CreateOrderMsg(null, false, JSON.toJSONString(errMsg));
		}

		String payResponse=payValidateResponse.getPayResponse();
		if(payResponse != null){
			PayResponse payResponseObject = JacksonUtil.decode(payResponse, PayResponse.class);
			errMsg.put("code", payResponseObject.getCode());
			errMsg.put("type","pay");
			errMsg.put("message", payResponseObject.getMessage());
			// 如果支付状态为成功
			if(PlaneConfig.SUCCESS_CODE == payResponseObject.getCode() && "SUCCESS".equals(payResponseObject.getResult().getResults().get(0).getPayStatus())){
				LOGGER.info("机票订单付款成功");
				order.setOrderStatus(OrderStatus.confirmed);
				order.setExpire(null);//取消失效时间
				planeTicketObject.setStatus(1);
				// 所有信息保存更新到虚拟字段中
				OrderItem oi=getPlaneItem(order);//order.getOrderItems().get(0);
				oi.setVirtualProductOrderInfo(JSONObject.toJSONString(planeTicketObject));
				return new CreateOrderMsg(null, true, "机票订单下单成功并且已经支付");
			}
		}else{
			//支付返回为空则可认为请求支付接口返回异常或超时
			throw new RuntimeException("去哪儿机票订单："+order.getSn()+"返回支付信息为空，需要人工检查是否支付成功");
		}
		order.setSupplierMemo(errMsg.get("message")+"");
		return  new CreateOrderMsg(null, false, JSON.toJSONString(errMsg),3);
	}
	/**
	 * 方法:查看订单列表
	 *
	 * autor :胡辉
	 *
	 * @return
	 */
	public Page<PlaneTicket> queryList(Pageable pageable){
		List<Filter> filters = pageable.getFilters();
		if(filters == null){
			filters=new ArrayList<Filter>();
		}
		Long supplierId = CommonAttributes.VIRTUAL_SUPPLIER_PLANE_TICKET;/*123l*/;
		//腾邦机票供应商
		Supplier supplier = supplierService.findByProperties(Filter.eq("isOweOrder", 16),Filter.eq("enabledFlag", true));
		List<Long> supplierIds=new ArrayList<Long>();
		supplierIds.add(supplierId);
		supplierIds.add(supplier.getId());
		filters.add(Filter.in("product.supplierId", supplierIds));//订单项关联的供应商
		//filters.add(Filter.ne("product.jdSku", PlaneConfig.SKU_CHANGE_PLANE_TICKET));
		filters.add(Filter.eq("order.enabledFlag", true));
		filters.add(Filter.eq("order.isSplit", false));

		//		filters.add(Filter.ne("order.orderStatus", OrderStatus.unconfirmed));
		filters.add(Filter.isNotNull("virtualProductOrderInfo"));
		//filters.add(Filter.)


		// 获取该用户下的所有有关机票的订单数据


		Page<OrderItem> orderItems =orderItemService.findPage(pageable);
		// 判断是否有订单
		if(orderItems != null){
			List<PlaneTicket> planeTicketList = new ArrayList<PlaneTicket>();
			// 遍历orderItem
			for(OrderItem orderItem : orderItems.getContent()){
				// 判断orderItem里面是否有虚拟字段数据，以及状态不是待确认的订单（这一步其实应该放在dao层去过滤）
				Order order=orderItem.getOrder();
				// 转换成对象
				PlaneTicket planeTicket = FastJsonUtils.toBean(orderItem.getVirtualProductOrderInfo(), PlaneTicket.class);

				BigDecimal totalAmount = order.getToatlAmount();//订单支付金额
				try{
					if(StringUtils.isNotBlank(planeTicket.getInsuranceOrderSn()) && order.getOrderItems().size() == 1){//有犀牛保险信息并且已经拆单
						totalAmount = totalAmount.add(planeTicket.getInsuranceAmount().multiply(BigDecimal.valueOf(planeTicket.getQty())));//订单金额加上保险金额到前台显示
					}
				} catch(Exception e){
					LOGGER.error("保险金额获取异常：", e);
				}
				planeTicket.setExpire(order.getExpire());
				planeTicket.setToatlAmount(totalAmount);
				planeTicket.setCompanyFee(order.getFee());//企业收取的手续费
				if(planeTicket.getChangeInfo()!=null&&planeTicket.getChangeInfo().getStatus()==0
						&&planeTicket.getChangeInfo().getFeeOrderNo()!=null){
					Order feeOrder=orderService.findBySn(planeTicket.getChangeInfo().getFeeOrderNo());
					if(!feeOrder.isExpired()&&feeOrder.getOrderStatus()==OrderStatus.unpaid){
						planeTicket.getChangeInfo().setIsAllowPay(true);
					}
				}
				planeTicket.setPlaneOrderId(order.getId());
				planeTicket.setTotalRebate(order.getTotalRebate());//返利信息
				planeTicket.setFreezingId(order.getFreezingId());//订单返利id

				TripSnapshot tripSnapshot = order.getTripSnapshot();//超标信息
				if(tripSnapshot != null && StringUtils.isNotBlank(tripSnapshot.getReason()) && tripSnapshot.getIsBreak() != null && tripSnapshot.getIsBreak()){
					planeTicket.setTripSnapshot("yes");
				}
				if(order.getOrderStatus() == OrderStatus.cancelled){
					planeTicket.setRefund(true);
					// 如果是取消状态，且是退款的单，做显示
					//if(planeTicket.isRefund() == true){
					planeTicket.setOrderId(order.getSn());
					planeTicket.setCreateOrderDate(order.getCreateDate());
					// 计算总价
					int totalPrice = planeTicket.getArf() + planeTicket.getTof();
					totalPrice = totalPrice + planeTicket.getPrice();
					planeTicket.setTotalPrice(totalPrice);
					//orderItem.setVirtualProductOrderInfo(JSONObject.toJSONString(planeTicket));
					// 新增展
					planeTicketList.add(planeTicket);
					//}
				}else{
					planeTicket.setOrderId(order.getSn());
					planeTicket.setCreateOrderDate(order.getCreateDate());
					// 计算总价
					int totalPrice = planeTicket.getArf() + planeTicket.getTof();
					totalPrice = totalPrice + planeTicket.getPrice();
					planeTicket.setTotalPrice(totalPrice);
					//orderItem.setVirtualProductOrderInfo(JSONObject.toJSONString(planeTicket));
					planeTicketList.add(planeTicket);
				}


			}
			Page<PlaneTicket> planeTicketPage = new Page<PlaneTicket>(planeTicketList,orderItems.getTotal(), pageable);
			return  planeTicketPage;
		}
		return null;
	}

	/**
	 * 方法:查看订单详情
	 *
	 * autor :胡辉
	 *
	 * @param     orderId     订单号
	 *
	 * @return
	 */
	public PlaneTicket getOrderDetail(String orderId){
		if(orderId != null){
			// 获取指定订单
			Order order = orderService.findBySn(orderId);
			if(order !=null){
				// 拿到订单项
				OrderItem orderItem = getPlaneItem(order);//order.getOrderItems().get(0);
				if(orderItem !=null && orderItem.getVirtualProductOrderInfo() != null){
					// 解析成机票对象
					PlaneTicket	planeTicket = FastJsonUtils.toBean(orderItem.getVirtualProductOrderInfo(), PlaneTicket.class);

					BigDecimal totalAmount = order.getToatlAmount();//订单支付金额
					BigDecimal coinAmount = order.getCoinAmount();//积分支付
					BigDecimal whiteBarAmount = order.getWhiteBarAmount();//白条支付
					try{
						if(planeTicket.getSuccessChangeInfo() != null){//有改签记录
							if(StringUtils.isNotBlank(planeTicket.getSuccessChangeInfo().getInsuranceOrderSn())){
								totalAmount = totalAmount.add(BigDecimal.valueOf(Double.valueOf(planeTicket.getSuccessChangeInfo().getInsurancePrice())));//订单金额加上保险金额到前台显示
								Order insuranceOrder = orderService.findBySn(planeTicket.getSuccessChangeInfo().getInsuranceOrderSn());
								if(insuranceOrder != null){//保险订单数据
									coinAmount = coinAmount.add(insuranceOrder.getCoinAmount());
									whiteBarAmount = whiteBarAmount.add(insuranceOrder.getWhiteBarAmount());
								}
							}
						} else if(StringUtils.isNotBlank(planeTicket.getInsuranceOrderSn()) && order.getOrderItems().size() == 1){//有犀牛保险信息并且已经拆单
							totalAmount = totalAmount.add(planeTicket.getInsuranceAmount().multiply(BigDecimal.valueOf(planeTicket.getQty())));//订单金额加上保险金额到前台显示
							Order insuranceOrder = orderService.findBySn(planeTicket.getInsuranceOrderSn());
							if(insuranceOrder != null){//保险订单数据
								coinAmount = coinAmount.add(insuranceOrder.getCoinAmount());
								whiteBarAmount = whiteBarAmount.add(insuranceOrder.getWhiteBarAmount());
							}
						}
					} catch(Exception e){
						LOGGER.error("保险金额获取异常：", e);
					}

					planeTicket.setOrderId(order.getSn());//订单号
					planeTicket.setToatlAmount(totalAmount);//订单总金额
					planeTicket.setCreateOrderDate(order.getCreateDate());//订单创建时间爱你
					planeTicket.setCoinAmount(coinAmount);//订单支付金额
					planeTicket.setWhiteBarAmount(order.getWhiteBarAmount());//白条支付金额
					if(planeTicket.getAgentLastTicketTime()==null||"".equals(planeTicket.getAgentLastTicketTime())){//如果机票代理商最晚出票时限为空则设置机票代理商最晚出票时限
						if(!TempusConstants.TEMPUS_TAG.equals(planeTicket.getTag())){
							PlaneTicket	planeTicketNow = setAgentLastTicketTime(orderItem);
							if(planeTicketNow != null && StringUtils.isNotBlank(planeTicketNow.getAgentLastTicketTime())){//最晚出票时间不为空
								LOGGER.info("获取到最晚出票时间:{}", planeTicketNow.getAgentLastTicketTime());
								planeTicket.setAgentLastTicketTime(planeTicketNow.getAgentLastTicketTime());//回填最晚出票时间
							}
						}
					}
					if(order.isExpired()||order.getOrderStatus()==OrderStatus.cancelled){
						planeTicket.setRefund(true);
					}
					return planeTicket;
				}
			}
		}
		return null;
	}
	/**
	 * 设置机票代理商最晚出票时限
	 *
	 * @Title: setAgentLastTicketTime
	 * @param orderItem
	 */
	public PlaneTicket setAgentLastTicketTime(OrderItem orderItem){
		try {
			if(orderItem !=null && orderItem.getVirtualProductOrderInfo() != null){
				// 解析成机票对象
				PlaneTicket	planeTicket = FastJsonUtils.toBean(orderItem.getVirtualProductOrderInfo(), PlaneTicket.class);
				if(planeTicket!=null&&planeTicket.getAgentLastTicketTime()==null||"".equals(planeTicket.getAgentLastTicketTime())){
					// 去查订单详情接口	
					String response = planeApi.getOrderDetail(orderItem.getOrder().getJdOrderId());
					OrderDetailResponse orderDetailResponse = JacksonUtil.decode(response, OrderDetailResponse.class);
					planeTicket.setAgentLastTicketTime(orderDetailResponse.getResult().getDetail().getAgentLastTicketTime());
					orderItem.setVirtualProductOrderInfo(JSONObject.toJSONString(planeTicket));
					orderItemService.update(orderItem);
				}
				return planeTicket;
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			LOGGER.error("设置机票代理商最晚出票时限异常：", e);
			e.printStackTrace();
		}
		return null;
	}
	/**
	 *
	 * 预订成功设置机票代理商最晚出票时限，并发送短信通知
	 * @Title: setAgentLastTicketTimeAndSendSms
	 * @param orderItem
	 */
	public void setAgentLastTicketTimeAndSendSms(OrderItem orderItem){
		try {
			PlaneTicket	planeTicket =setAgentLastTicketTime(orderItem);
			if( planeTicket.getAgentLastTicketTime()!=null){
				String fightText=" "+planeTicket.getDptTime()+" ("+planeTicket.getDptCity()+")"+planeTicket.getDptAirport()+CommUtil.null2String(planeTicket.getDptTerminal())
						+" 飞 ("+planeTicket.getArrCity()+")"+planeTicket.getArrAirport()+CommUtil.null2String(planeTicket.getArrTerminal());
				smsService.sendPlaneTicketBookSuccessSms(orderItem.getOrder().getCompanyId(), planeTicket.getContactMob(), orderItem.getOrder().getSn(), planeTicket.getCarrierName()+planeTicket.getFlightNum(), planeTicket.getDptDate(), fightText, planeTicket.getAgentLastTicketTime());
			}
		} catch (Exception e) {
			LOGGER.error("预订成功设置机票代理商最晚出票时限，并发送短信通知异常：", e);
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	/**
	 * 方法:获取行李额信息
	 *
	 * autor :胡辉
	 *
	 * @param     luggageAllowanceRequest    行李额请求参数封装
	 *
	 * @return
	 */
	public String getLuggageAllowance(LuggageAllowanceRequest luggageAllowanceRequest){
		// 判断行李额请求是否为空
		if(luggageAllowanceRequest != null){
			// 调用接口
			String response = planeApi.getLuggageAllowance(luggageAllowanceRequest);
			// 获取接口返回信息
			LuggageAllowanceResponse luggageAllowance = JacksonUtil.decode(response, LuggageAllowanceResponse.class);
			if(luggageAllowance != null){
				// 判断状态码是否为成功
				if(PlaneConfig.SUCCESS_CODE == luggageAllowance.getCode()){
					LOGGER.info("行李额信息 ==" + luggageAllowance.getResult().getSpecialRules().get(0));
					return luggageAllowance.getResult().getSpecialRules().get(0);
				}
			}
		}

		return null;
	}

	/**
	 * 方法:是否可退款
	 *
	 * autor :胡辉
	 *
	 * @param     orderId  订单号
	 *
	 * @return
	 */
	public boolean canRefund(String orderId){
		if(orderId !=null){
			// 查询平台order
			Order order = orderDao.findBySn(orderId);
			if(order !=null){
				String planeCacheKey = PLANE_REFUND_CACHE+order.getJdOrderId();
				String response = jedisDao.STRINGS.get(planeCacheKey);
				if(StringUtil.isEmpty(response)) {
					LOGGER.info("退款查询缓存为空");
					// 获取接口返回信息
				    response = planeApi.refundSearch(order.getJdOrderId());
				}
				if(!StringUtil.isEmpty(response)){
					jedisDao.STRINGS.setEx(planeCacheKey, 1800,response);
					// 封装接口对象
					RefundSearchResponse refundSearchResponse = JacksonUtil.decode(response, RefundSearchResponse.class);
					// 返回是否可退款，返回true，表示可退款
					if(PlaneConfig.SUCCESS_CODE == refundSearchResponse.getCode() && refundSearchResponse.getResult().get(0).getRefundSearchResult().isCanRefund() == true){
						return true;
					}
				}

			}
		}
		return false;
	}

	/**
	 * 方法:退款查询
	 *
	 * autor :胡辉
	 *
	 * @param     orderId  订单号
	 *
	 * @return
	 */
	public PlaneTicket refundSearch(String orderId){
		if(orderId !=null){
			// 查询平台order
			Order order = orderDao.findBySn(orderId);
			LOGGER.info("平台order= " + order.toString());
			if(order !=null){
				String planeCacheKey = PLANE_REFUND_CACHE+order.getJdOrderId();
				String response = jedisDao.STRINGS.get(planeCacheKey);
				if(StringUtil.isEmpty(response)) {
					LOGGER.info("退款查询缓存为空");
					// 获取接口返回信息
					response = planeApi.refundSearch(order.getJdOrderId());
				}
				LOGGER.info("退款查询数据为:{}",response);
				if(response !=null){
					jedisDao.STRINGS.setEx(planeCacheKey, 1800,response);
					// 封装接口返回信息
					RefundSearchResponse refundSearchResponse = JacksonUtil.decode(response, RefundSearchResponse.class);
					if(PlaneConfig.SUCCESS_CODE == refundSearchResponse.getCode() && refundSearchResponse.getResult().get(0).getRefundSearchResult().isCanRefund() == true){
						// 获取机票实体对象字符串
						OrderItem oi = getPlaneItem(order);//获取机票订单项
						String planeTicketStr = null;//order.getOrderItems().get(0).getVirtualProductOrderInfo();
						if(oi != null){
							planeTicketStr = oi.getVirtualProductOrderInfo();//机票信息
						}
						// 是否为空
						if(planeTicketStr !=null){
							// 封装机票实体对象
							PlaneTicket	planeTicket = FastJsonUtils.toBean(planeTicketStr, PlaneTicket.class);
							// 设置订单号
							planeTicket.setOrderId(order.getSn());
							planeTicket.setPlaneOrderId(order.getId());
							planeTicket.setTgqText(refundSearchResponse.getResult().get(0).getRefundSearchResult().getRefundRuleInfo().getTgqText());
							planeTicket.setSignText(refundSearchResponse.getResult().get(0).getRefundSearchResult().getRefundRuleInfo().getSignText());
							planeTicket.setTgqReasons(refundSearchResponse.getResult().get(0).getRefundSearchResult().getTgqReasons());

							planeTicket.setRefundFeeJson(JSON.toJSONString(getRefundFeeMap(refundSearchResponse)));

							BigDecimal refundFee=new BigDecimal(refundSearchResponse.getResult().get(0).getRefundSearchResult().getTgqReasons().get(0).getRefundPassengerPriceInfoList().get(0).getRefundFeeInfo().getRefundFee());
							planeTicket.setRefundFee(refundFee);
							List<Passenger> passengers = planeTicket.getPassengers();
							// 遍历乘机人
							/*for(int i=0 ; i<passengers.size(); i++){
								// 乘客id，供退款申请使用
								long id = refundSearchResponse.getResult().get(i).getId();
								LOGGER.info("乘机人id=" + id);					
								passengers.get(i).setPassengerId(id);
							}*/
							for(Passenger passenger : passengers){
								result:for(RefundSearchResponseResult result :refundSearchResponse.getResult()){
									//									LOGGER.info("result乘机人{}_id={},{}", result.getName(), result.getId(), result.getCardNum());
									//									LOGGER.info("passenger乘机人{}_id={},{}", passenger.getName(), passenger.getPassengerId(), passenger.getCardNo());

									if(passenger.getName().equals(result.getName())&&verifyCardNo(passenger.getCardNo(),result.getCardNum(),(PlaneConfig.PASSPORT_CARD_TYPE.equals(passenger.getCardType())))){
										//										LOGGER.info("设置乘机人{}_id={}", result.getName(), result.getId());
										passenger.setPassengerId(result.getId());
										break result;
									}
								}
							}
							return planeTicket;
						}
					}
				}

			}
		}
		return null;
	}
	/**
	 * 回去退票费
	 * @param refundSearchResponse
	 * @return
	 */
	private Map<Long, Map<Integer, Integer>> getRefundFeeMap(RefundSearchResponse refundSearchResponse){
		Map<Long, Map<Integer, Integer>> refundFeeMap=new HashMap<Long, Map<Integer, Integer>>();
		for(RefundSearchResponseResult res:refundSearchResponse.getResult()) {
			Map map= new HashMap();
			refundFeeMap.put(res.getId(), map);
			for(TgqReason tgqReason:res.getRefundSearchResult().getTgqReasons()) {
				map.put(tgqReason.getCode(), tgqReason.getRefundPassengerPriceInfoList().get(0).getRefundFeeInfo().getRefundFee());
			}
		}
		return refundFeeMap;
	}
	/**
	 * 校验证件
	 * @param cardNo 本地证件
	 * @param cardNum  去哪儿证件
	 * @param isPP 是否为护照
	 * @return
	 */
	public boolean verifyCardNo(String cardNo,String cardNum,Boolean isPP) {
		/*
		 * boolean isPP=false;
		 * if("#".equals(cardNum.substring(cardNum.length()-1,cardNum.length()))) {
		 * isPP=true; }
		 */
		cardNum=cardNum.replace("#", "");
		return
				((
						//NI:身份证,PP:护照,ID:其他	
						!isPP&&
								cardNo.substring(0,4).equals(cardNum.substring(0,4))&&
								//避免用户输入时候用小写x，这里大小写忽略
								cardNo.substring(cardNo.length()-3,cardNo.length()).equalsIgnoreCase(cardNum.substring(cardNum.length()-3,cardNum.length()))
				)||(
						//NI:身份证,PP:护照,ID:其他
						isPP&&
								cardNo.substring(0,2).equals(cardNum.substring(0,2))&&
								//避免用户输入时候用小写x，这里大小写忽略
								cardNo.substring(cardNo.length()-2,cardNo.length()).equalsIgnoreCase(cardNum.substring(cardNum.length()-2,cardNum.length()))

				));

	}
	/**
	 * 方法:退款申请
	 *
	 * autor :胡辉
	 *
	 * @param     orderId  订单号
	 * @param     ids      乘机人ids
	 * @param	  refundCause  退款原因
	 *
	 * @return
	 */
	@Transactional
	public boolean applyRefund(String orderId, Long[] ids, String[] refundNames,String refundCauseId, String refundCause, BigDecimal refundF1ee){
		try {
			if(orderId !=null && ids !=null && ids.length>0){
				// 获取要申请退款的订单
				Order order = orderDao.findBySn(orderId);
				// 获取虚拟字符串信息
				String virtualInfo = order.getOrderItems().get(0).getVirtualProductOrderInfo();
				OrderItem oi = getPlaneItem(order);//获取机票订单项
				if(oi != null){
					virtualInfo = oi.getVirtualProductOrderInfo();//机票信息
				}
				if(order !=null && virtualInfo != null && !StringUtils.isEmpty(virtualInfo)){
					// 获取去哪儿订单号
					ApplyRefundRequest applyRefundRequest = new ApplyRefundRequest();
					String orderNo = order.getJdOrderId();
					applyRefundRequest.setRefundCauseId(refundCauseId);
					// 设置要查询的第三方订单号
					applyRefundRequest.setOrderNo(orderNo);
					StringBuffer sb = new StringBuffer();
					// 获取乘机人ids
					for(int i=0; i<ids.length; i++){
						sb.append(ids[i].toString());
						sb.append(",");
					}
					applyRefundRequest.setPassengerIds(sb.toString());
					// 获取退款原因
					applyRefundRequest.setRefundCause(refundCause);
					//退款信息
					RefundSearchResponse refundSearchResponse=getRefundSearchResponse(orderNo);
					//获取每张票需要退款的金额
					//List<TgqReason> tgqReasons=refundSearchResponse.getResult().get(0).getRefundSearchResult().getTgqReasons();
					//BigDecimal refundFee =null;
					Map<Long, Map<Integer, Integer>> refundFeeMap=getRefundFeeMap(refundSearchResponse);
					/*
					 * for(TgqReason tgqReason:tgqReasons){
					 * if(tgqReason.getCode()==Integer.parseInt(refundCauseId)){
					 * LOGGER.info("tgqReason code:"+tgqReason.getCode()+"refundCauseId："+
					 * refundCauseId); refundFee=new
					 * BigDecimal(tgqReason.getRefundPassengerPriceInfoList().get(0).
					 * getRefundFeeInfo().getRefundFee()); break; } }
					 */
					/*
					 * if(refundFee==null){ LOGGER.info("获取refundFee:"+refundFee); return false; }
					 */
					PlaneTicket	planeTicket = FastJsonUtils.toBean(virtualInfo, PlaneTicket.class);
					//退款总金额
					//BigDecimal refundFeeTotal = BigDecimal.ZERO;

					//LOGGER.info("每张票需要收取手续费："+refundFee+"乘客人数："+ ids.length);
					//退款总金额
					//refundFeeTotal = refundFeeTotal.add(refundFee.multiply(new BigDecimal( ids.length)));


					// 获取接口返回信息
					String response = planeApi.applyRefund(applyRefundRequest);
					if(response !=null){
						// 封装接口返回信息
						ApplyRefundResponse applyRefundRefundResponse = JacksonUtil.decode(response, ApplyRefundResponse.class);
						if(applyRefundRefundResponse !=null && applyRefundRefundResponse.getResult() !=null &&applyRefundRefundResponse.getResult().get(0).getRefundApplyResult().isSuccess() == true){
							// 设置该乘机人的状态为退款待确认
							//退款总金额
							BigDecimal refundFeeTotal = BigDecimal.ZERO;
							List<Passenger> passengers = planeTicket.getPassengers();
							// 遍历需要退款的乘机人
							for(int i =0; i<refundNames.length ; i++){
								for(int j=0; j<passengers.size(); j++){
									String[] array=refundNames[i].split(":");
									Long id=Long.parseLong(array[0]);
									String refundName=array[1];
									if(refundName.equals(passengers.get(j).getName())){
										// 设置乘机人状态为退款待确认
										passengers.get(j).setPassengerStatus(3);
										passengers.get(j).setRefundApplyDate(com.vispractice.vpshop.util.CommUtil.formatLongDate(new Date()));
										LOGGER.info("乘机人状态设置为退款待确认");
										BigDecimal refundFee=new BigDecimal( refundFeeMap.get(id).get(Integer.parseInt(refundCauseId)));
										//自愿退票
										if(applyRefundRequest.getRefundCauseId().equals(PlaneConfig.REFUND_CAUSE_ID)
												||applyRefundRequest.getRefundCauseId().equals(PlaneConfig.REFUND_CAUSE_ID_17)){
											if(passengers.get(j).getAgeType()==1) {//儿童加价
												refundFee=refundFee.add(new BigDecimal(planeTicket.getChildFee()));
												//退款手续费需要加上福利平台收取的手续费
												if(planeTicket.getChildServiceCharge() != null){//企业服务费不为空
													refundFee = refundFee.add(planeTicket.getChildServiceCharge());
												}
											}else {//成人收取服务费
												refundFee=refundFee.add(new BigDecimal(planeTicket.getFee()));
												//退款手续费需要加上福利平台收取的手续费
												if(planeTicket.getServiceCharge() != null){//企业服务费不为空
													refundFee = refundFee.add(planeTicket.getServiceCharge());
												}

											}
										}
										//退款总金额
										refundFeeTotal = refundFeeTotal.add(refundFee);
										passengers.get(j).setRefundFee(refundFee);
									}
								}
							}
							if(passengers.size() == ids.length){
								// 全部退，则为全部退款待确认
								planeTicket.setStatus(4);
							}else{
								// 部分出票，部分退票
								planeTicket.setStatus(3);
							}
							planeTicket.setRefundFee(refundFeeTotal);
							LOGGER.info("进行退款待确认以后的planeTicket== {}", JSONObject.toJSONString(planeTicket));
							order.getOrderItems().get(0).setVirtualProductOrderInfo(JSONObject.toJSONString(planeTicket));
							if(oi != null){
								oi.setVirtualProductOrderInfo(JSONObject.toJSONString(planeTicket));//机票信息
							}

							// 更新订单（更新了乘机人状态和退款价格）
							orderService.update(order);
							return true;
						}
					}
				}
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return false;
	}


	/**
	 * 方法:退款处理方法
	 *
	 * autor :胡辉
	 *
	 *
	 * @return
	 */
	@Transactional
	public Order refunProcessing(String orderNo){
		// 去查订单详情接口	
		String response = planeApi.getOrderDetail(orderNo);
		if(response != null){
			OrderDetailResponse orderDetailResponse = JacksonUtil.decode(response, OrderDetailResponse.class);
			if(orderDetailResponse != null){
				Detail detail = orderDetailResponse.getResult().getDetail();
				if(detail !=null){
					// 获取父订单号
					String parentOrderNo = detail.getParentOrderNo();
					// 获取兄弟节点订单号
					String siblingOrderNo = detail.getSiblingOrderNo();
					// 兄弟节点状态如果是出票成功，那说明要拆单了
					String siblingOrderStatus = detail.getSiblingOrderStatus();
					String planeOkStr = "出票完成";
					String planeChangeStr = "改签完成";
					LOGGER.info("兄弟节点状态siblingOrderStatus=={}", siblingOrderStatus);
					LOGGER.info("比的结果{}", planeOkStr.equals(siblingOrderStatus));
					Order order =null;
					try {
						order=orderDao.findByJdOrderId(orderNo);
					} catch (NoResultException e) {
						// TODO Auto-generated catch block
						LOGGER.info("{}订单不存在	-----", orderNo);
					}
					if(order==null&&parentOrderNo != null && !StringUtils.isEmpty(parentOrderNo) && siblingOrderNo != null && !StringUtils.isEmpty(siblingOrderNo) &&( planeOkStr.equals(siblingOrderStatus)||planeChangeStr.equals(siblingOrderStatus))){
						LOGGER.info("拆单，取消原来订单，退款	-----");
						Order parentOrder = orderService.findByJdOrderId(parentOrderNo);
						// 父单在已完成的情况下才能调用，防止多次回调
						if(parentOrder !=null && parentOrder.getOrderStatus() == OrderStatus.completed){
							String virtualInfo = parentOrder.getOrderItems().get(0).getVirtualProductOrderInfo();
							OrderItem oi = getPlaneItem(parentOrder);//获取机票订单项
							if(oi != null){
								virtualInfo = oi.getVirtualProductOrderInfo();//机票信息
							}
							PlaneTicket planeTicket =  FastJsonUtils.toBean(virtualInfo, PlaneTicket.class);
							BigDecimal refundFee = planeTicket.getRefundFee();
							if(refundFee==null){
								throw new RuntimeException("退款手续费为空，此订单为线下申请退款订单");
							}
							// 先取消原来的订单
							orderService.cancelOrderRefunds(parentOrder, parentOrder.getMember(),false);
							parentOrder.setEnabledFlag(false);
							//获取退款之后的员工发放积分明细
							Map<String,List<RechargeRecord>> rechargeRecords=rechargeRecordService.getRefundRechargeRecord(parentOrder);
							// 要退票的乘机人信息，拿进去做判断，从而拆单，拆乘机人
							List<Passenger> refundPassengers = orderDetailResponse.getResult().getPassengers();
							//拆单,生成两个新单
							Order refundOrder=createRefundOrder(rechargeRecords,parentOrder, orderNo, siblingOrderNo, refundPassengers);
							// 退款金额相关的操作（可能会生单，扣除退款金额的钱， 还会取消上一步生成的要退票的单，因为这个单要做载体展示数据，所以上一步必须要生成）
							return returnFeeProcess(rechargeRecords,refundOrder, refundFee);
						}
					}else{// 不拆单，直接取消原来订单，退款		
						LOGGER.info("不拆单，直接取消原来订单，退款	-----");

						// 订单在已完成的情况下才能调用，防止多次回调
						if(order != null && order.getOrderStatus() == OrderStatus.completed){
							String virtualInfo = order.getOrderItems().get(0).getVirtualProductOrderInfo();
							OrderItem oi = getPlaneItem(order);//获取机票订单项
							if(oi != null){
								virtualInfo = oi.getVirtualProductOrderInfo();//机票信息
							}
							PlaneTicket planeTicket =  FastJsonUtils.toBean(virtualInfo, PlaneTicket.class);
							LOGGER.info("不拆单，直接取消原来订单，退款	planeTicket-----{}", planeTicket);
							/** 订单状态  1.出票中 2.出票成功 3.部分出票，部分退款待确认（会显示出票成功，但是里面的单会有退款待确认单，不能点击退票）  4.全部退款待确认  5.退票成功*/
							if(planeTicket.getStatus() != 4){
								LOGGER.error("机票状态不为4（全部退款待确认）不能进行退款    planeTicket.getStatus()：  {}", planeTicket.getStatus());
								return null;
							}
							BigDecimal refundFee = planeTicket.getRefundFee();
							if(refundFee==null){
								throw new RuntimeException("退款手续费为空，此订单为线下申请退款订单");
							}
							// 全部退款完成
							planeTicket.setStatus(5);
							List<Passenger> passengers = planeTicket.getPassengers();
							for(Passenger passenger : passengers){
								passenger.setPassengerStatus(4);
							}
							planeTicket.setRefund(true);
							order.getOrderItems().get(0).setVirtualProductOrderInfo(JSONObject.toJSONString(planeTicket));
							if(oi != null){
								oi.setVirtualProductOrderInfo(JSONObject.toJSONString(planeTicket));
							}

							orderService.update(order);
							// 先取消原来的订单
							orderService.cancelOrderRefunds(order, order.getMember(),false);

							//获取退款之后的员工发放积分明细
							Map<String,List<RechargeRecord>> rechargeRecords=rechargeRecordService.getRefundRechargeRecord(order);

							//取消保险订单信息
							String oldInsuranceOrderSn = planeTicket.getInsuranceOrderSn();//原来保险单号
							if(planeTicket.getSuccessChangeInfo() != null){//如果已经有改签过的话，则用改签的信息
								oldInsuranceOrderSn = planeTicket.getSuccessChangeInfo().getInsuranceOrderSn();
							}
							cancelInsuranceOrder(order, null, null, oldInsuranceOrderSn);

							// 退款金额相关的操作（可能会生单，扣除退款金额的钱）
							return returnFeeProcess(rechargeRecords,order, refundFee);
						}
					}
				}
			}
		}
		return null;
	}


	/**
	 * 方法:退款金额处理
	 *
	 * autor :胡辉
	 *
	 *
	 * @return
	 */
	public Order returnFeeProcess(Map<String,List<RechargeRecord>> rechargeRecords,Order parentOrder, BigDecimal refundFee ){
		BigDecimal coinAmount=parentOrder.getCoinAmount().add(parentOrder.getWhiteBarAmount());
		//退款现金金额=已支付金额-积分白条支付金额
		BigDecimal refundCashAmount=parentOrder.getAmountPaid().subtract(coinAmount);
		Order order =null;
		// 如果退款金额大于0
		if(refundFee.compareTo(BigDecimal.ZERO) == 1){
			// 调用生单接口公用参数
			OrderItem parentOi=getPlaneItem(parentOrder);//parentOrder.getOrderItems().get(0);
			Long productId  =parentOi.getProduct().getId();
			// 生成一个单（专门用于退款，扣除金额）

			BigDecimal payCashAmount=BigDecimal.ZERO;
			BigDecimal payCoinAmount=refundFee;

			if(coinAmount.compareTo(refundFee)==-1){
				payCashAmount=refundFee.subtract(coinAmount);
				payCoinAmount=coinAmount;
			}
			//退款现金金额=已支付金额-积分白条支付金额-需收现金手续费金额
			refundCashAmount=parentOrder.getAmountPaid().subtract(coinAmount).subtract(payCashAmount);

			if(parentOrder.getFee() != null){//手续费不退
				refundCashAmount = refundCashAmount.subtract(parentOrder.getFee());
			}

			LOGGER.info("{}已支付金额:{}---积分白条支付金额:{}需收现金手续费金额{}需退现金：{}", parentOrder.getSn(), parentOrder.getAmountPaid(), coinAmount, payCashAmount, refundCashAmount);
			//计算成本 手续费总金额-父单的收取的服务费
			BigDecimal jdPrice = refundFee.subtract(parentOi.getPrice().subtract(parentOi.getJdPrice()));
			CreateOrderMsg msg =createOrder(rechargeRecords.get("integral"),rechargeRecords.get("whiteBar"),parentOrder.getMember() , productId, null, payCoinAmount,payCashAmount, null, null,parentOrder,jdPrice);
			order = msg.getOrder();
			order.setParentId(parentOrder);
			OrderItem oi=getPlaneItem(order);//order.getOrderItems().get(0);

			oi.setFullName(parentOi.getFullName().replace(oi.getName(), parentOi.getProduct().getName()+"-退改签"));
			oi.setName(oi.getFullName());
			order.setOrderStatus(OrderStatus.completed);
			orderService.update(order);

			if(order.getCompanyId() != null && !StringUtils.isEmpty(order.getCompanyId().getCoinConfKey())){//第三方积分配置
				//第三方积分回退
				coinAmountService.thirdCoinRefunds(parentOrder.getCompanyId(), parentOrder.getMember(), parentOrder, null, null);
			}


		}
		//退现金
		if(refundCashAmount.compareTo(BigDecimal.ZERO)==1){
			Member member=parentOrder.getMember();
			orderService.refundsWeixin(parentOrder, refundCashAmount, null, null,member.getId() , member.getUsername());
		}
		return order;
	}
	/**
	 * 方法:退款生单操作，以及变更状态
	 *
	 * autor :胡辉
	 *
	 *
	 * @return
	 */
	public Order createRefundOrder(Map<String,List<RechargeRecord>> rechargeRecords,Order parentOrder, String orderNo, String siblingOrderNo, List<Passenger> refundPassengers){
		String virtualInfo = getPlaneItem(parentOrder).getVirtualProductOrderInfo();// parentOrder.getOrderItems().get(0).getVirtualProductOrderInfo();
		OrderItem oi = getPlaneItem(parentOrder);//获取机票订单项
		if(oi != null){
			virtualInfo = oi.getVirtualProductOrderInfo();//机票信息
		}
		if(virtualInfo != null && !StringUtils.isEmpty(virtualInfo)){
			PlaneTicket	planeTicket = FastJsonUtils.toBean(virtualInfo, PlaneTicket.class);
			// 机票总价
			// 计算总价
			int adultPrice = planeTicket.getArf() + planeTicket.getTof()+planeTicket.getPrice();
			int childPrice = planeTicket.getChildtof() + planeTicket.getChildPrice();
			List<Passenger> passengers = planeTicket.getPassengers();
			// 调用生单接口公用参数
			Member member = parentOrder.getMember();
			Long productId  = productService.findByJdSku(PlaneConfig.SKU_PLANE_TICKET).getId();
			// 生成要退款的订单
			PlaneTicket refundPlaneTicket = (PlaneTicket)planeTicket.clone();
			List<Passenger> refundNewPassengers = new ArrayList<Passenger>();
			String passengerNames="";

			BigDecimal refundPayAmount =  BigDecimal.ZERO;

			List<String> cartIds = new ArrayList<String>();
			for(int i =0; i<passengers.size(); i++){
				for(int j=0; j<refundPassengers.size(); j++){
					if(passengers.get(i).getName().equals(refundPassengers.get(j).getName())&&
							passengers.get(i).getCardNo().substring(0,4).equals(refundPassengers.get(j).getCardNum().substring(0,4))&&
							passengers.get(i).getCardNo().substring(passengers.get(i).getCardNo().length()-3,passengers.get(i).getCardNo().length()).equals(refundPassengers.get(j).getCardNum().substring(refundPassengers.get(j).getCardNum().length()-3,refundPassengers.get(j).getCardNum().length()))){
						// 设置乘机人状态为退款完成状态
						passengers.get(i).setPassengerStatus(4);
						refundNewPassengers.add(passengers.get(i));
						if(!"".equals(passengerNames)){
							passengerNames+=",";
						}
						passengerNames+=passengers.get(i).getName();
						if(passengers.get(i).getAgeType()==1){//儿童
							refundPayAmount=refundPayAmount.add(new BigDecimal(childPrice));
							if(planeTicket.getChildServiceCharge() != null){//企业服务费不为空,需要扣除服务费信息
								refundPayAmount.add(planeTicket.getChildServiceCharge());
							}
						}else {
							refundPayAmount=refundPayAmount.add(new BigDecimal(adultPrice));
							if(planeTicket.getServiceCharge() != null){//企业服务费不为空,需要扣除服务费信息
								refundPayAmount.add(planeTicket.getServiceCharge());
							}

						}
						cartIds.add(passengers.get(i).getCardNo());//待退款的乘机人证件号--用于后面航意险保单退保用
					}
				}
			}
			refundPlaneTicket.setPassengers(refundNewPassengers);
			// 全部退款完成
			refundPlaneTicket.setStatus(5);
			refundPlaneTicket.setRefund(true);
			refundPlaneTicket.setQty(refundNewPassengers.size());
			LOGGER.info("refundPlaneTicket =={}", JSONObject.toJSONString(refundPlaneTicket));
			LOGGER.info("orderNo =={}",  orderNo);


			//获取退款之后的员工发放积分明细
			//父订单积分加白条支付金额
			BigDecimal coinAmount=parentOrder.getCoinAmount().add(parentOrder.getWhiteBarAmount());
			//需支付现金
			BigDecimal payCashAmount=BigDecimal.ZERO;
			//需支付积分
			BigDecimal payCoinAmount=refundPayAmount;
			//父订单支付积分白条小于需支付积分
			if(coinAmount.compareTo(payCoinAmount)==-1){
				payCashAmount=payCoinAmount.subtract(coinAmount);
				payCoinAmount=coinAmount;
				coinAmount=BigDecimal.ZERO;
			}else{
				coinAmount=coinAmount.subtract(payCoinAmount);
			}
			//计算成本
			BigDecimal jdPrice = getOrderCost(refundPlaneTicket,refundNewPassengers,refundPayAmount);
			CreateOrderMsg refundOrderMsg = createOrder(rechargeRecords.get("integral"),rechargeRecords.get("whiteBar"),member , productId, JSONObject.toJSONString(refundPlaneTicket), payCoinAmount,payCashAmount, orderNo, null,parentOrder,jdPrice);
			Order refundOrder = refundOrderMsg.getOrder();
			OrderItem refundOi = getPlaneItem(refundOrder);//refundOrder.getOrderItems().get(0);
			refundOi.setFullName(refundOi.getFullName()+"("+passengerNames+")");
			refundOi.setName(refundOi.getFullName());
			// 取消退票乘机人的单	
			LOGGER.info("退票单开始发送");
			createSuccessOperate(refundOrder);
			refundOrder.setParentId(parentOrder);
			orderService.update(refundOrder);
			/*try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}*/
			//orderService.cancelOrderRefunds(refundOrder, refundOrder.getMember(),false);


			// 生成未退款的单	
			PlaneTicket noRefundPlaneTicket = (PlaneTicket)planeTicket.clone();
			List<Passenger> noRefundPassengers = new ArrayList<Passenger>();
			int count = 0;
			passengerNames="";
			BigDecimal noRefundPayAmount = new BigDecimal(0);
			for(int i =0; i<passengers.size(); i++){
				for(int j=0; j<refundPassengers.size(); j++){
					if(passengers.get(i).getName().equals(refundPassengers.get(j).getName())&&
							passengers.get(i).getCardNo().substring(0,4).equals(refundPassengers.get(j).getCardNum().substring(0,4))&&
							passengers.get(i).getCardNo().substring(passengers.get(i).getCardNo().length()-3,passengers.get(i).getCardNo().length()).equals(refundPassengers.get(j).getCardNum().substring(refundPassengers.get(j).getCardNum().length()-3,refundPassengers.get(j).getCardNum().length()))){
						count ++;
					}
				}
				// 遍历完都没有找到合适的，那说明不在退款乘机人里面
				if(count == i){
					if(!"".equals(passengerNames)){
						passengerNames+=",";
					}
					passengerNames+=passengers.get(i).getName();
					passengers.get(i).setPassengerStatus(2);
					noRefundPassengers.add(passengers.get(i));
					if(passengers.get(i).getAgeType()==0){
						noRefundPayAmount=noRefundPayAmount.add(new BigDecimal(adultPrice));
					}else {
						noRefundPayAmount=noRefundPayAmount.add(new BigDecimal(childPrice));
					}
					count ++;
				}
			}
			noRefundPlaneTicket.setPassengers(noRefundPassengers);
			// 状态为出票完成状态
			noRefundPlaneTicket.setStatus(2);
			noRefundPlaneTicket.setQty(noRefundPassengers.size());
			LOGGER.info("noRefundPassengers =={}", JSONObject.toJSONString(noRefundPlaneTicket));
			LOGGER.info("siblingOrderNo =={}", siblingOrderNo);

			//需支付现金
			payCashAmount=BigDecimal.ZERO;
			//需支付积分
			payCoinAmount=noRefundPayAmount;
			//父订单支付积分白条小于需支付积分
			if(coinAmount.compareTo(payCoinAmount)==-1){
				payCashAmount=payCoinAmount.subtract(coinAmount);
				payCoinAmount=coinAmount;
			}
			//计算成本
			BigDecimal noRefundCost = getOrderCost(noRefundPlaneTicket,noRefundPassengers,noRefundPayAmount);
			CreateOrderMsg noRefundOrderMsg = createOrder(rechargeRecords.get("integral"),rechargeRecords.get("whiteBar"),member , productId, JSONObject.toJSONString(noRefundPlaneTicket), payCoinAmount,payCashAmount, siblingOrderNo, null,parentOrder,noRefundCost);
			// 设为出票成功
			Order noRefundOrder =  noRefundOrderMsg.getOrder();
			LOGGER.info("出票单开始发送");
			oi=getPlaneItem(noRefundOrder);//noRefundOrder.getOrderItems().get(0);

			oi.setFullName(oi.getFullName()+"("+passengerNames+")");
			oi.setName(oi.getFullName());
			noRefundOrder.setParentId(parentOrder);
			createSuccessOperate(noRefundOrder);
			noRefundOrder.setOrderStatus(OrderStatus.completed);
			orderService.update(noRefundOrder);
			orderService.cancelOrderRefunds(refundOrder, refundOrder.getMember(),false);

			//取消保险订单信息
			String oldInsuranceOrderSn = planeTicket.getInsuranceOrderSn();//原来保险单号
			if(planeTicket.getSuccessChangeInfo() != null){//如果已经有改签过的话，则用改签的信息
				oldInsuranceOrderSn = planeTicket.getSuccessChangeInfo().getInsuranceOrderSn();
			}
			cancelInsuranceOrder(parentOrder, cartIds, noRefundOrder, oldInsuranceOrderSn);

			return refundOrder;
		}
		return null;
	}
	/**
	 * 创建订单
	 *
	 * */
	@Transactional
	public CreateOrderMsg createOrder(List<RechargeRecord> rechargeRecordsY,List<RechargeRecord> whiteBarRecordsY,
			Member member , Long productId,String orderJson,BigDecimal payCoinAmount,BigDecimal payCashAmount, 
			String jdOrderId,Integer quantity,Order parentOrder,BigDecimal jdPrice){
		Product product=productService.find(Long.valueOf(productId));
		if(product==null){
			LOGGER.error("商品为空");
			throw new RuntimeException("商品为空");
		}
		if(quantity==null){
			quantity=1;
		}
		LOGGER.info("{}拆分创建子订单--------支付积分：{}支付现金：{}成本：{}", parentOrder.getSn(), payCoinAmount, payCashAmount, jdPrice);
		Cart cart = getCart(member ,product, quantity, payCashAmount.add(payCoinAmount), orderJson);


		PaymentMethod paymentMethod = paymentMethodService.find(1L);
		if (paymentMethod == null) {
			//return new CreateOrderMsg(null, false, "paymentMethod is null");
			LOGGER.error("支付方式为空");
			throw new RuntimeException("支付方式为空");

		}
		ShippingMethod shippingMethod = shippingMethodService.find(1L);
		if (shippingMethod == null) {
			//return new CreateOrderMsg(null, false, "shippingMethod is null");
			LOGGER.error("配送方式为空");
			throw new RuntimeException("配送方式为空");
		}
		Receiver receiver =new Receiver();
		receiver.setConsignee(member.getName());

		CreateOrderMsg createOrderMsg  = orderService.create(cart,receiver, paymentMethod, shippingMethod, null, false, "", false, null, null,rechargeRecordsY,whiteBarRecordsY);

		//更新第三方订单号
		Order order = createOrderMsg.getOrder();
		if(order != null){
			order.setJdPrice(jdPrice);
			OrderItem oi = getPlaneItem(order);//order.getOrderItems().get(0);

			oi.setJdPrice(jdPrice);
			if(oi.getResellerCost() != null && oi.getResellerCost().compareTo(oi.getJdPrice()) < 0){
				//分销成本比实际成本低，使用平台成本作为分销成本
				oi.setResellerCost(oi.getJdPrice());
				oi.setProfit(BigDecimal.ZERO);
			}
			//BigDecimal amountPayable=order.getToatlAmount().subtract(order.getAmountPaid());//应付金额
			if(payCashAmount.compareTo(BigDecimal.ZERO)==1){

				//支付积分不足开始支付现金
				Boolean flag=cashPay(order, payCashAmount, parentOrder);
				if(!flag){
					throw new RuntimeException("现金支付失败-------------");
				}
				order.setOrderStatus(OrderStatus.confirmed);
				order.setAmountPaid(order.getAmountPaid().add(payCashAmount));
				//order.setToatlAmount(order.getToatlAmount().add(payCashAmount));
			}
			//创建人
			if(parentOrder.getCreateUser()!=null){
				order.setCreateUser(parentOrder.getCreateUser());
			}
			//出差申请单
			if(parentOrder.getApplyId()!=null){
				order.setApplyId(parentOrder.getApplyId());
			}
			order.setConsignee(parentOrder.getConsignee());
			order.setPhone(parentOrder.getPhone());
			order.setJdOrderId(jdOrderId);
			order.setExpire(null);//取消失效时间
			orderService.update(order);
		}else{
			LOGGER.error("配送方式为空");
			throw new RuntimeException("生成订单失败-------------");
		}
		return createOrderMsg;
	}
	/**
	 * 退现金
	 * @param refundOrder 退款订单
	 * @param order 手续费订单
	 * @return
	 */
	/*	private boolean cashRefund(Order refundOrder,Order order){
		LOGGER.error("取消订单开始退扣除手续费的现金-------------------");
		//如果已支付金额大于积分加白条支付金额说明有现金支付
		if(refundOrder.getAmountPaid().compareTo(order.getCoinAmount().add(order.getWhiteBarAmount()))==1){
			//获取手续订单现金支付记录
			List<Payment> payments = findPaymentByOrderItemId(order.getOrderItems().get(0).getId());
			if(CollectionUtils.isEmpty(payments)){
				LOGGER.error("现金支付记录不存在---------------不需要退现金");
				return true;
			}
			BigDecimal amount=BigDecimal.ZERO;
			for(Payment payment:payments){
				amount=amount.add(payment.getAmount());
			}
			//获取refundOrder支付记录
			List<Payment> refundPayments = findPaymentByOrderItemId(refundOrder.getOrderItems().get(0).getId());
			if(CollectionUtils.isEmpty(refundPayments)){
				LOGGER.error("现金支付记录不存在---------------数据有问题？");
				return false;
			}
			BigDecimal refundAmount=BigDecimal.ZERO;
			for(Payment payment:refundPayments){
				refundAmount=refundAmount.add(payment.getAmount());
			}
			Member member=refundOrder.getMember();
			orderService.refundsWeixin(refundOrder, refundAmount.subtract(amount), null, null,member.getId() , member.getUsername());
			LOGGER.info(order.getSn()+"需要退款现金："+refundAmount.subtract(amount)+"退款成功");
		}
		return true;
	}*/
	/**
	 * 现金支付
	 * @param order 支付订单
	 * @param amountPayable 待支付金额
	 * @param parentOrder 父订单
	 */
	private boolean cashPay(Order order,BigDecimal amountPayable,Order parentOrder){
		LOGGER.error("机票改签或取消拆单拆现金支付记录开始-------------------");
		if(amountPayable==null||amountPayable.compareTo(BigDecimal.ZERO)==0){
			LOGGER.error("需现金支付金额不存在或为零 amountPayable：：："+amountPayable);
			return true;
		}
		if(amountPayable.compareTo(BigDecimal.ZERO)==-1){
			LOGGER.error("需现金支付金额小鱼零 amountPayable：：：{}", amountPayable);
			return false;
		}

		//获取父订单现金支付记录
		OrderItem oi = getPlaneItem(parentOrder);//parentOrder.getOrderItems().get(0);//获取机票订单项
		List<Payment> payments = findPaymentByOrderItemId(oi.getId());
		if(CollectionUtils.isEmpty(payments)){
			LOGGER.error("现金支付记录不存在---------------");
			return false;
		}
		List<Payment> newPayments=new ArrayList<Payment>();
		OrderItem thisOi = getPlaneItem(order);//本单的订单项
		for(Payment payment:payments){
			Payment newPayment=new Payment();
			BeanUtils.copyProperties(payment,newPayment, new String[] { "id", "createDate", "modifyDate", "memo", "order","orderItemId"});
			newPayment.setOrder(order);
			newPayment.setExpire(null);
			newPayment.setSn(snDao.generate(Sn.Type.payment));
			newPayment.setOrderItemId(thisOi.getId());
			newPayment.setMemo("订单项Id"+newPayment.getOrderItemId()+"拆分付款金额");
			if(payment.getAmount().compareTo(amountPayable)!=-1){
				LOGGER.error("现金支付记录:{}金额：{}大于 需要支付现金：{}", payment.getId(), payment.getAmount(), amountPayable);
				newPayment.setAmount(amountPayable);
				amountPayable=BigDecimal.ZERO;
			}else{
				LOGGER.error("现金支付记录:{}金额：{}小于 需要支付现金：{}", payment.getId(), payment.getAmount(), amountPayable);
				newPayment.setAmount(payment.getAmount());
				amountPayable=amountPayable.subtract(payment.getAmount());
			}
			newPayments.add(newPayment);
			if(amountPayable.compareTo(BigDecimal.ZERO)==0){
				break;
			}
		}
		if(amountPayable.compareTo(BigDecimal.ZERO)==0){
			paymentDao.saveEntitys(newPayments);
			return true;
		}
		LOGGER.error("现金支付记录金额不足，还需支付现金："+amountPayable);
		return false;
	}

	/**
	 * 查询需退款的订单项现金扣款记录
	 * 方法:
	 * 实现流程:
	 *    1.
	 * autor :sunjianwen
	 * @param orderItemId
	 * @return
	 */
	private List<Payment> findPaymentByOrderItemId(Long orderItemId){
		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("orderItemId", orderItemId));
		filters.add(Filter.eq("type", Payment.Type.orderItemSplit));
		//filters.add(Filter.eq("method", Payment.Method.online));
		filters.add(Filter.eq("status", Payment.Status.success));
		List<Payment> payments = paymentDao.findList(null, null, filters, null);
		return payments;
	}
	/**
	 * 支付成功拆分积分支付记录
	 * @param order
	 */
	public void createSuccessOperate(Order order) {
		// TODO Auto-generated method stub
		Assert.notNull(order);
		if(order.getCoinAmount().compareTo(new BigDecimal(0))>0 ||
				order.getWhiteBarAmount().compareTo(new BigDecimal(0))>0){
			paymentRecordsHandler.paymentRecordsCoin(order.getId());
			/*try {
				PaymentMsgBean paymentMsgBean = new PaymentMsgBean("a",order.getId());
				LOGGER.info("机票发送消息队列");
				producerService.sendMessage(destination, paymentMsgBean);//发送消息
			} catch (Exception e) {
				e.printStackTrace();
			}*/
		}
	}
	/**
	 * 拆分积分支付记录
	 * 方法:
	 * 实现流程:
	 *    1.
	 * autor :sunjianwen
	 * @param
	 */
	/*public void paymentRecordsCoin(Long orderId){
		LOGGER.info("开始处理orderId=" + orderId + "的订单");
		if(orderId == null){
			LOGGER.info("orderId为NULL");
			return;
		}
		Order order = orderDao.find(orderId);
		if(order == null){
			LOGGER.info("orderId=" + orderId + "订单,不存在");
			return;
		}
		BigDecimal coinAmount = order.getCoinAmount();//该订单应付的总积分
		boolean flag1=true;
		if(coinAmount == null || coinAmount.compareTo(new BigDecimal(0)) <= 0){
			flag1=false;
			LOGGER.info("orderId=" + orderId + "订单,没有支付积分");
		}
		boolean flag2=true;
		BigDecimal whiteBarAmount = order.getWhiteBarAmount();//该订单支付的白条
		if(whiteBarAmount==null || whiteBarAmount.compareTo(new BigDecimal(0)) <= 0){
			flag2=false;
			LOGGER.info("orderId=" + orderId + "订单,没有支付白条");
		}

		if(flag1==false && flag2==false){
			return;
		}

		//遍历订单项
		List<CoinLog> coinLogsTemp = new ArrayList<CoinLog>();

		List<Filter> filters = new ArrayList<Filter>();
		filters.add(Filter.eq("order", order));
		List<OrderItem> orderItems = orderItemService.findList(null, null, filters, null);

		List<CoinLog> coinLogs = coinLogDao.findConsumeByOrder(order);
		//没有订单项
		if(orderItems == null || orderItems.size() <= 0){
			LOGGER.info("orderId=" + orderId + "订单,没有订单项");
			return;
		}
		//没有积分支付记录
		if(coinLogs == null || coinLogs.size() <= 0){
			LOGGER.info("orderId=" + orderId + "订单,没有积分支付记录");
			return;
		}
		BigDecimal debitOi = orderItems.get(0).getSubtotal().subtract(orderItems.get(0).getCouponAmount());//本次订单项的拆分剩余额度(遍历第一次是第一个订单项的)
		BigDecimal debitCl = coinLogs.get(0).getDebit();//本次积分支付记录的拆分剩余额度(遍历第一次是第一条积分支付记录的)
		BigDecimal debitFr = order.getFreight();//邮费
		int indexOi = 0;//订单项索引
		int indexCl = 0;//积分记录索引
		//遍历订单项或者有邮费(还要拆分邮费)
		while((indexOi < orderItems.size() || (debitFr != null && debitFr.compareTo(new BigDecimal(0)) > 0)) && indexCl < coinLogs.size()){
			OrderItem orderItem = null;
			CoinLog coinLog = coinLogs.get(indexCl);
			BigDecimal debitNow = null;//本次循环的消费记录

			if(coinLog.getType() == CoinLog.Type.orderItemSplit){
				//订单已拆分，不需要拆分了
				LOGGER.info("orderId=" + orderId + "订单已拆分");
				return;
			}

			//先拆分订单项
			if(indexOi < orderItems.size()){
				orderItem = orderItems.get(indexOi);
				LOGGER.info("orderId=" + orderId + "订单,拆分订单项id="+orderItem.getId());
				//比较订单项和积分支付记录的额度大小
				if(debitOi.compareTo(debitCl) < 0){
					//本条积分支付记录拆分之后还有剩余额度，给下一个订单项用
					debitNow = debitOi;
					indexOi++;
					debitCl = debitCl.subtract(debitOi);//本条积分支付记录剩余额度
					//防止超出索引
					if(indexOi < orderItems.size()){
						debitOi =  orderItems.get(indexOi).getSubtotal().subtract(orderItems.get(indexOi).getCouponAmount());//本次的订单项额度已全部支付完，debitOi中记录下个订单项的额度
					}
				}else if(debitOi.compareTo(debitCl) > 0){
					//本条积分支付记录额度不够用,用下一条来继续付
					debitNow = debitCl;
					indexCl++;
					debitOi = debitOi.subtract(debitCl);//本次的订单项剩余未支付额度
					//防止超出索引
					if(indexCl < coinLogs.size()){
						debitCl = coinLogs.get(indexCl).getDebit();//本条积分支付记录额度已全部消费完，debitCl中记录下一条积分支付记录的额度
					}
				}else if(debitOi.compareTo(debitCl) == 0){
					//本条积分支付记录额度正好支付本次订单项
					debitNow = debitCl;
					indexCl++;
					indexOi++;
					//防止超出索引
					if(indexOi < orderItems.size()){
						debitOi =  orderItems.get(indexOi).getSubtotal().subtract(orderItems.get(indexOi).getCouponAmount());//本次的订单项额度已全部支付完，debitOi中记录下个订单项的额度
					}
					//防止超出索引
					if(indexCl < coinLogs.size()){
						debitCl = coinLogs.get(indexCl).getDebit();//本条积分支付记录额度已全部消费完，debitCl中记录下一条积分支付记录的额度
					}
				}
			}else{
				//后拆分邮费
				//比较邮费和积分支付记录的额度大小
				if(debitFr.compareTo(debitCl) < 0){
					LOGGER.info("orderId=" + orderId + "订单,拆分邮费额度:"+debitFr);
					//本条积分支付记录拆分之后还有剩余额度，给下一个订单项用
					debitNow = debitFr;
					debitFr =  new BigDecimal(0);//邮费全部支付完，剩余需支付变成0
					debitCl = debitCl.subtract(debitFr);//本条积分支付记录剩余额度
				}else{
					//本条积分支付记录额度不够用,用下一条来继续付
					debitNow = debitCl;
					indexCl++;
					debitFr = debitFr.subtract(debitCl);//本次的订单项剩余未支付额度
					//防止超出索引
					if(indexCl < coinLogs.size()){
						debitCl = coinLogs.get(indexCl).getDebit();//本条积分支付记录额度已全部消费完，debitCl中记录下一条积分支付记录的额度
					}
				}
				LOGGER.info("orderId=" + orderId + "订单,拆分邮费额度:"+debitNow);
			}

			CoinLog coinLogTemp = new CoinLog();   //保存积分支付拆分记录
			coinLogTemp.setOrders(order);
			coinLogTemp.setTradeSn(order.getSn());
			coinLogTemp.setOrderItemId(orderItem == null ? 0L : orderItem.getId());
			coinLogTemp.setBalance(coinLog.getBalance());    //用户余额
			coinLogTemp.setCoinTypeId(coinLog.getCoinTypeId());
			coinLogTemp.setCompanyId(coinLog.getCompanyId());
			coinLogTemp.setCreateUser(coinLog.getCreateUser());
			coinLogTemp.setDebit(debitNow);
			coinLogTemp.setMember(coinLog.getMember());
			if(orderItem == null){
				coinLogTemp.setMemo("订单"+order.getSn()+"邮费拆分付款积分");
			}else{
				//已付金额
				orderItem.setAmountPaid(debitNow.add(orderItem.getAmountPaid() == null ? new BigDecimal(0) : orderItem.getAmountPaid()));
				coinLogTemp.setMemo("订单项Id"+orderItem.getId()+"拆分付款积分");
			}
			coinLogTemp.setOperator(coinLog.getOperator());
			coinLogTemp.setRechargeRecord(coinLog.getRechargeRecord());
			coinLogTemp.setType(CoinLog.Type.orderItemSplit);
			coinLogsTemp.add(coinLogTemp);
		}

		for(OrderItem orderItem: orderItems){
			//解锁，可以对订单进行取消和退货退款操作
			orderItem.setIsPaymentLog(true);
		}

		coinLogDao.saveEntitys(coinLogsTemp);
		orderItemDao.updateEntitys(orderItems);
	}*/
	public Cart getCart(Member member ,Product product,Integer quantity,BigDecimal price ,String  virtualProductOrderInfo){
		// TODO Auto-generated method stub
		Assert.notNull(product);
		Assert.notNull(price);
		Assert.notNull(quantity);
		Cart cart=new Cart();
		cart.setMember(member);
		cart.setKey(Cart.KEY_VIRTUAL_PRODUCT_NAME);
		CartItem item=new CartItem();
		item.setCart(cart);
		item.setProduct(product);
		item.setQuantity(quantity);
		item.setTempPrice(price);
		item.setVirtualProductOrderInfo(virtualProductOrderInfo);
		cart.getCartItems().add(item);
		return cart;
	}

	/**
	 * 返回退款信息
	 *
	 * @Title: getRefundSearchResponse
	 * @param planeOrderId 第三方订单id
	 * @return
	 */
	private RefundSearchResponse getRefundSearchResponse(String planeOrderId){
		RefundSearchResponse refundSearchResponse=null;
		try {
			if(planeOrderId==null){
				return refundSearchResponse;
			}
			String planeCacheKey = PLANE_REFUND_CACHE+planeOrderId;
			String response = jedisDao.STRINGS.get(planeCacheKey);
			if(StringUtil.isEmpty(response)) {
				LOGGER.info("退款查询缓存为空");
				// 获取接口返回信息
				response = planeApi.refundSearch(planeOrderId);
			}
			if(response==null||"".equals(response)){
				return refundSearchResponse;
			}
			jedisDao.STRINGS.setEx(planeCacheKey, 1800,response);
			refundSearchResponse = JacksonUtil.decode(response, RefundSearchResponse.class);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		return refundSearchResponse;
	}
	@Override
	public String selectTGQ(TgqRequest req) {
		// TODO Auto-generated method stub
		return planeApi.selectTGQ(req);
	}
	@Override
	public boolean tpUpload(String orderNo,String passengerIds ,MultipartFile multipartFile) {
		// TODO Auto-generated method stub
		return planeApi.tpUpload(orderNo, passengerIds, multipartFile);
	}
	 /**
     * 改签上传附件接口
     * @param orderNo订单号
     * @param passengerIds乘机人Id，多个乘机人以逗号分隔，改签查询接口返回，所在节点层级：result->id
     * @param multipartFile 必须为图片，小于5M，图片类型没有限制
     * @return
     */
    public boolean gqUpload(String orderNo,String passengerIds ,MultipartFile multipartFile) {
    	return planeApi.gqUpload(orderNo, passengerIds, multipartFile);
    }
	/**
	 * 改签查询
	 * @param orderNo 订单号
	 * @param changeDate 改签日期，例如：“2017-12-17”
	 * @return
	 */
	public ChangeSearchResponse changeSearch(String orderNo,String changeDate) {
		return planeApi.changeSearch(orderNo, changeDate);
	}
	/**
	 * 改签申请
	 * @param info 改签信息
	 * @return
	 */
	@Transactional
	public Message  applyChange(ChangeInfo info){
		return applyChange(info, null);
	}

	/**
	 *
	 * 方法: 改签申请<br/>
	 * 实现流程  : <br/>
	 * @param info	改签信息
	 * @param paymentLockKey	用户支付的redis异步锁
	 * @return
	 * 编码作者 : 吴锭超
	 * 完成日期 : 2020-04-15 12:01:53
	 */
	@Transactional
	public Message applyChange(ChangeInfo info, String paymentLockKey){
		if(info==null){
			return Message.error("参数异常");
		}
		Order parentOrder=orderService.findBySn(info.getOrderNo());
		if(parentOrder==null){
			return Message.error("订单不存在");
		}
		//改签费用异常
		if(new BigDecimal(info.getAllFee()).compareTo(BigDecimal.ZERO)==-1){
			LOGGER.error("订单：{}改签费用:{}异常",parentOrder.getSn(),info.getAllFee());
			LOGGER.error("订单：{}改签参数：{}",parentOrder.getSn(),JSON.toJSONString(info));
			return Message.error("由于航司原因，改签费用待核算，请联系平台客服人员");
		}
		ApplyChangeRequest req=new ApplyChangeRequest();
		req.setApplyRemarks("不想飞了" );
		req.setCabinCode(info.getCabinCode());
		req.setCallbackUrl(callbackUrl);
		req.setChangeCauseId(info.getCode()+"");
		req.setEndTime(info.getEndTime());
		req.setFlightNo(info.getFlightNo());
		req.setOrderNo(parentOrder.getJdOrderId());
		req.setPassengerIds(info.getPassengerIds());
		req.setStartDate(info.getStartDate());
		req.setStartTime(info.getStartTime());
		req.setUniqKey(info.getUniqKey());
		req.setGqFee(info.getGqFee());
		req.setUpgradeFee(info.getUpgradeFee());

		ApplyChangeResponse res=planeApi.applyChange(req);
		if(res!=null&&res.getCode()==PlaneConfig.SUCCESS_CODE){
			//新的订单号
			if(res.getResult().get(0).getChangeApplyResult().isSuccess()){
				String qunaerOrderNo=res.getResult().get(0).getChangeApplyResult().getOrderNo();
				if(qunaerOrderNo!=null&&!"".equals(qunaerOrderNo)){
					OrderItem oi = getPlaneItem(parentOrder);//parentOrder.getOrderItems().get(0);//获取机票订单项
					String virtualInfo=oi.getVirtualProductOrderInfo();
					PlaneTicket planeTicket = JSON.parseObject(virtualInfo, PlaneTicket.class);
					//获取父订单全部乘客
					info.setGqId(res.getResult().get(0).getChangeApplyResult().getGqId());
					info.setQunaerOrderNo(qunaerOrderNo);
					planeTicket.setChangeInfo(info);
					PlaneInsuranceVo insuranceChange = null;
					if(!qunaerOrderNo.equals(parentOrder.getJdOrderId())){//拆单
						planeTicket.setNeedSplit(true);
						try {
							//拆分订单
							insuranceChange = planeFacadeService.changeSplitOrder(qunaerOrderNo, planeTicket, paymentLockKey);
						} catch (Exception e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
					oi.setVirtualProductOrderInfo(JSONObject.toJSONString(planeTicket));
					orderService.update(parentOrder);
					/*Message msg=
					msg.getData().put("parentOrderId", parentOrder.getId());
					//msg.getData().put("changeQunaerOrderNo", qunaerOrderNo);
					 * 
					 */
					Message result = Message.success("");
					result.getData().put("insuranceChange", insuranceChange);
					return result;
				}
			}else{
				String reason=res.getResult().get(0).getChangeApplyResult().getReason();
				if(reason!=null&&!"".equals(reason)){
					return Message.error(reason);
				}
			}
		}
		return Message.error("操作失败");
	}


	/**
	 * 改签支付
	 * @param info 改签信息
	 * @return
	 */
	public ChangePayrResponse changePay(ChangeInfo info){
		ChangePayRequest req=new ChangePayRequest();
		req.setCallbackUrl(payCallbackUrl);
		req.setGqId(info.getGqId());
		req.setOrderNo(info.getQunaerOrderNo());
		req.setPassengerIds(info.getPassengerIds());
		req.setTotalAmount(info.getAllFee());
		return planeApi.changePay(req);
	}
	/**
	 *
	 * 修改改签状态
	 * @param orderNo 去哪儿订单号
	 * @param status 状态-1申请失败0申请1支付2改签完成 
	 */
	@Transactional
	public void changeUpdateStatus(String orderNo,int status){

		LOGGER.info("开始修改订单orderNo= {}改签状态为{}", orderNo, status);
		Order order=null;
		if(orderService.exists(Filter.eq("jdOrderId", orderNo))){
			order = orderService.findByJdOrderId(orderNo);
		}else{
			LOGGER.info("订单号orderNo={}不存在--------之前未拆单 开始拆单", orderNo);
			changeSplitOrder(orderNo,null);
			try {
				//重新查询
				order=orderService.findByJdOrderId(orderNo);
			} catch (NoResultException e) {
				// TODO Auto-generated catch block
				LOGGER.info("订单查询为空");
			}
		}
		if(order==null||!order.getEnabledFlag()){//订单号为空说明未拆单
			LOGGER.info("{}订单不存在 不需要拆单---------", orderNo);
			return;
		}
		OrderItem oi=getPlaneItem(order);//order.getOrderItems().get(0);
		String virtualInfo = oi.getVirtualProductOrderInfo();
		LOGGER.info("订单id={}机票订单下单信息---------{}", order.getId(), virtualInfo);
		PlaneTicket planeTicket = JSON.parseObject(virtualInfo, PlaneTicket.class);
		if(status==1){
			LOGGER.info("{}改签申请成功 调起改签支付---------", orderNo);
			if(planeTicket.getChangeInfo()==null){
				LOGGER.info("{}不存在改签申请", orderNo);
				return;
			}
			if(planeTicket.getChangeInfo().getStatus()!=0){
				LOGGER.info("{}修改状态不正确---------现在状态{}", orderNo, planeTicket.getChangeInfo().getStatus());
				return;
			}
			LOGGER.info("改签费用====={}, 改签保险费用==={}", Double.parseDouble(planeTicket.getChangeInfo().getAllFee()), planeTicket.getChangeInfo().getInsurancePrice());
			if(Double.parseDouble(planeTicket.getChangeInfo().getAllFee())>0){//改签费用大于零调起支付
				Order feeOrder = orderService.findBySn(planeTicket.getChangeInfo().getFeeOrderNo());
				if(feeOrder!=null&&feeOrder.getPaymentStatus()==PaymentStatus.paid){//并且已经支付
					LOGGER.info("{}改签支付---------", orderNo);
					ChangePayrResponse res=changePay(planeTicket.getChangeInfo());
					if(res!=null){
						if(res.getCode()==PlaneConfig.SUCCESS_CODE&&res.getResult().getCode()==0){
							LOGGER.info("{}改签支付申请---成功------", orderNo);
							planeTicket.getChangeInfo().setStatus(1);
							//设置改签支付单已完成
							feeOrder.setOrderStatus(OrderStatus.completed);

							try{
								OrderItem feeItem = feeOrder.getOrderItems().get(0);
								if(feeItem != null && feeItem.getResellerCost() != null){
									feeItem.setResellerCost(feeItem.getPrice());//设置价格为售价
								}
								orderItemService.update(feeItem);
							} catch(Exception e){

							}

							orderService.update(feeOrder);

						}else{

							LOGGER.info("{}改签申请失败---------退钱", orderNo);

							Order parentOrder=feeOrder.getParentId();
							//防止从父单退钱
							feeOrder.setParentId(null);
							feeOrder.setSupplierMemo("改签失败取消订单");
							orderService.cancelOrderRefunds(feeOrder, feeOrder.getMember());
							feeOrder.setParentId(parentOrder);

							if(feeOrder.getCompanyId() != null && !StringUtils.isEmpty(feeOrder.getCompanyId().getCoinConfKey())){//第三方积分配置
								//第三方积分回退
								coinAmountService.thirdCoinRefunds(feeOrder.getCompanyId(), feeOrder.getMember(), feeOrder, null, null);
							}

							LOGGER.info("{}改签申请失败---------退钱完成", orderNo);
							LOGGER.info("{} 失败--------改签支付申请---失败------", orderNo);
							planeTicket.getChangeInfo().setStatus(-1);
							planeTicket.getSuccessChangeInfos().add(planeTicket.getChangeInfo());
							planeTicket.setChangeInfo(null);
						}
					}
				}else{

					LOGGER.info("{}支付订单---------", (feeOrder!=null?feeOrder.getSn():"feeOrder is null"));
				}
			}


		}else if(status==2){
			LOGGER.info("{}改签完成---------", orderNo);
			if(planeTicket.getChangeInfo()==null){
				LOGGER.info("{}不存在改签申请------------", orderNo, status);
				return;
			}
			if(planeTicket.getChangeInfo().getStatus()!=0&&planeTicket.getChangeInfo().getStatus()!=1){
				LOGGER.info("{}修改状态不正确---------现在状态{}", orderNo, planeTicket.getChangeInfo().getStatus());
				return;
			}

			planeTicket.getChangeInfo().setStatus(2);

			try {//获取订单详情---取改签后的票号回填成功  票号
				LOGGER.info("{}改签成功后获取乘客新票号", orderNo);
				String response = planeApi.getOrderDetail(orderNo);//获取订单详情
				if(response != null){//有返回信息
					OrderDetailResponse orderDetailResponse = JacksonUtil.decode(response, OrderDetailResponse.class);
					if(orderDetailResponse != null && orderDetailResponse.getResult() != null){//订单详情返回信息
						Map<String, String> ticketNos = new HashMap<>();//改签成功后的新票号
						for (Passenger passenger : orderDetailResponse.getResult().getPassengers()){//改签的乘客
							if (passenger != null) {//乘客不为空
								String cardNo = passenger.getCardNum().substring(0, 4) + passenger.getCardNum().substring(passenger.getCardNum().length() - 3);
								ticketNos.put(passenger.getName() + cardNo, passenger.getTicketNo());
							}
						}
						for (Passenger passenger : planeTicket.getPassengers()) {
							if (passenger != null) {//乘客不为空
								String cardNo = passenger.getCardNo().substring(0, 4) + passenger.getCardNo().substring(passenger.getCardNo().length() - 3);
								String newTicketNo = ticketNos.get(passenger.getName() + cardNo);
								if (newTicketNo != null) {//接口返回了新的票号
									LOGGER.info("乘客票号：{}改签后，新票号：{}", passenger.getTicketNo(), newTicketNo);
									passenger.setTicketNo(newTicketNo);
								}
							}
						}
					}
				}
			} catch (Exception e) {
				LOGGER.error("{}改签成功后获取订单详情回填新票号异常:", orderNo, e);
			}


			//1.取消原来的保险订单
			String oldInsuranceOrderSn = planeTicket.getInsuranceOrderSn();//原来保险单号
			if(planeTicket.getSuccessChangeInfo() != null){//如果已经有改签过的话，则用改签的信息
				oldInsuranceOrderSn = planeTicket.getSuccessChangeInfo().getInsuranceOrderSn();
			}
			cancelInsuranceOrder(order, null, null, oldInsuranceOrderSn);
			//2.承保新保单
			Order feeOrder = orderService.findBySn(planeTicket.getChangeInfo().getFeeOrderNo());
			Map<String, Order> orders = insuranceService.splitOrder(feeOrder, planeTicket.getChangeInfo());//拆单
			if(orders != null && orders.get("insuranceOrder") != null){//有保险订单
				Order insuranceOrder = orders.get("insuranceOrder");//保险订单
				Message message = insuranceService.confirmOrder(insuranceOrder.getSn(), insuranceOrder.getJdOrderId());//保险确保
				if(message.getType() != Message.Type.success){//确认保单失败
					LOGGER.info("订单{}投保订单{}确认失败", insuranceOrder.getSn(), insuranceOrder.getJdOrderId());
				} else {
					LOGGER.info("订单{}投保订单{}确认成功", insuranceOrder.getSn(), insuranceOrder.getJdOrderId());
				}

				Order changeOrder = orders.get("planeOrder");//手续费订单
				if(changeOrder != null){//拆弹后的机票手续费订单
					//1.feeOrder取消

					//2.changeorder更新第三方标志已经订单订单状态
					planeTicket.getChangeInfo().setFeeOrderNo(changeOrder.getSn());
					changeOrder.setOrderStatus(OrderStatus.completed);//已完成
					OrderItem changeOrderOi = getPlaneItem(changeOrder);//order.getOrderItems().get(0);
					oi.setJdPrice(oi.getPrice());
					oi.setVirtualProductOrderInfo(null);
					orderService.update(changeOrder);
				} else {
					planeTicket.getChangeInfo().setFeeOrderNo(null);
				}

			}

			planeTicket.getSuccessChangeInfos().add(planeTicket.getChangeInfo());
			planeTicket.setChangeInfo(null);

		}else if(status==-1){
			if(planeTicket.getStatus()==3||planeTicket.getStatus()==4){
				LOGGER.info("{}退款申请失败---------修改状态", orderNo);
				planeTicket.setStatus(2);
				List<Passenger> passengers = planeTicket.getPassengers();
				for(Passenger passenger : passengers){
					if(passenger.getPassengerStatus()==3){
						passenger.setPassengerStatus(5);
					}
				}
				//发送退票失败短信
				sendSms(planeTicket, order, PlaneConfig.SMS_PLANE_REFUND_FAIL);
			}else{
				LOGGER.info("{}改签申请失败---------{}", orderNo, status);
				if(planeTicket.getChangeInfo()==null){
					LOGGER.info("{}不存在改签申请------------", orderNo);
					return;
				}
				if(planeTicket.getChangeInfo().getStatus()!=0&&planeTicket.getChangeInfo().getStatus()!=1){
					LOGGER.info("{}修改状态不正确---------现在状态{}", orderNo, planeTicket.getChangeInfo().getStatus());
					return;
				}
				LOGGER.info("{}改签费用---------{}", orderNo, planeTicket.getChangeInfo().getAllFee());
				if(Double.parseDouble(planeTicket.getChangeInfo().getAllFee())>0){//改签费用大于零退款
					LOGGER.info("{}改签申请失败---------退钱", orderNo);
					Order feeOrder = orderService.findBySn(planeTicket.getChangeInfo().getFeeOrderNo());
					Order parentOrder=feeOrder.getParentId();
					//防止从父单退钱
					feeOrder.setParentId(null);
					feeOrder.setSupplierMemo("改签失败取消订单");
					orderService.cancelOrderRefunds(feeOrder, feeOrder.getMember());
					feeOrder.setParentId(parentOrder);
					//order.setOrderStatus(OrderStatus.completed);

					if(feeOrder.getCompanyId() != null && !StringUtils.isEmpty(feeOrder.getCompanyId().getCoinConfKey())){//第三方积分配置
						//第三方积分回退
						coinAmountService.thirdCoinRefunds(feeOrder.getCompanyId(), feeOrder.getMember(), feeOrder, null, null);
					}
					LOGGER.info("{}改签申请失败---------退钱完成", orderNo);
				}
				planeTicket.getChangeInfo().setStatus(-1);
				planeTicket.getSuccessChangeInfos().add(planeTicket.getChangeInfo());
				planeTicket.setChangeInfo(null);
				//发送改签失败短信
				sendSms(planeTicket, order, PlaneConfig.SMS_PLANE_CHANGE_FAIL);
			}
		}
		oi.setVirtualProductOrderInfo(JSON.toJSONString(planeTicket));
		orderService.update(order);
	}
	/**
	 * 改签支付关联支付订单
	 * @param qunaerOrderNo 改签去哪订单号
	 * @param feeOrder 支付订单
	 * @return
	 */
	public CreateOrderMsg changePayChangeStatus(String qunaerOrderNo,Order feeOrder){
		CreateOrderMsg orderMsg=new CreateOrderMsg(null, false, "机票改签支付订单生成失败");
		//Order parentOrder=this.findBySn(changeInfo.getOrderNo());
		//获取改签订单
		try {
			Order changeOrder=orderDao.findByJdOrderId(qunaerOrderNo);
			if(changeOrder==null){
				throw new ApiException("订单不存在 改签订单未拆单");
			}
			OrderItem oi=getPlaneItem(changeOrder);//changeOrder.getOrderItems().get(0);

			String virtualInfo = oi.getVirtualProductOrderInfo();
			PlaneTicket planeTicket = JSON.parseObject(virtualInfo, PlaneTicket.class);
			planeTicket.getChangeInfo().setFeeOrderNo(feeOrder.getSn());

			if(feeOrder.getPaymentStatus()==PaymentStatus.paid){//并且已经支付
				LOGGER.info(qunaerOrderNo+"改签支付---------");
				ChangePayrResponse res=changePay(planeTicket.getChangeInfo());
				if(res!=null){
					if(res.getCode()==PlaneConfig.SUCCESS_CODE&&res.getResult().getCode()==0){
						LOGGER.info(qunaerOrderNo+"改签支付申请---成功------");
						planeTicket.getChangeInfo().setStatus(1);
					}else{

						LOGGER.info(qunaerOrderNo+"改签申请失败---------退钱");
						orderMsg.setResultMsg("机票改签支付失败");
						return orderMsg;
						/*

						//防止从父单退钱
						feeOrder.setParentId(null);
						orderService.cancelOrderRefunds(feeOrder, feeOrder.getMember());
						feeOrder.setParentId(changeOrder);

						changeOrder.setOrderStatus(OrderStatus.completed);
						LOGGER.info(qunaerOrderNo+"改签申请失败---------退钱完成");
						LOGGER.info(qunaerOrderNo+" 失败--------改签支付申请---失败------");
						planeTicket.getChangeInfo().setStatus(-1);
						planeTicket.getSuccessChangeInfos().add(planeTicket.getChangeInfo());
						planeTicket.setChangeInfo(null);*/
					}
				}
			}
			oi.setVirtualProductOrderInfo(JSON.toJSONString(planeTicket));
			orderDao.merge(changeOrder);


			orderMsg=new CreateOrderMsg(null, true, "机票改签支付申请成功");
			feeOrder.setParentId(changeOrder);
			feeOrder.getOrderItems().get(0).setVirtualProductOrderInfo(null);
			feeOrder.setOrderStatus(OrderStatus.completed);
			orderDao.merge(feeOrder);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return orderMsg;

	}

	/**
	 * 拆分改签单
	 * @param qunaerOrderNo 去哪儿订单号
	 */
	@Transactional
	public PlaneInsuranceVo changeSplitOrder( String qunaerOrderNo,PlaneTicket planeTicket){
		return changeSplitOrder(qunaerOrderNo, planeTicket, null);
	}
	/**
	 *
	 * 方法: 拆分改签单<br/>
	 * 实现流程  : <br/>
	 * @param qunaerOrderNo	去哪儿订单号
	 * @param planeTicket	机票信息
	 * @param paymentLockKey	用户积分支付redis异步锁
	 * 编码作者 : 吴锭超
	 * 完成日期 : 2020-04-15 12:05:35
	 */
	@Transactional
	public PlaneInsuranceVo changeSplitOrder( String qunaerOrderNo, PlaneTicket planeTicket, String paymentLockKey) {

		PlaneInsuranceVo insurance = null;
		LOGGER.info(qunaerOrderNo+"开始改签拆单----------");
		// 去查订单详情接口	
		if(orderDao.count(Filter.eq("jdOrderId", qunaerOrderNo))>0){
			LOGGER.error(qunaerOrderNo+"订单已存在不需要拆单----------");
			return insurance;
		}
		String response = planeApi.getOrderDetail(qunaerOrderNo);
		if(response != null){
			OrderDetailResponse orderDetailResponse = JacksonUtil.decode(response, OrderDetailResponse.class);
			if(orderDetailResponse != null){
				Detail detail = orderDetailResponse.getResult().getDetail();
				if(detail !=null){

					if(detail.getParentOrderNo()==null){
						LOGGER.error("没有父订单说明不需要拆单----------");
						return insurance;
					}
					Order parentOrder=orderService.findByJdOrderId(detail.getParentOrderNo());
					if(parentOrder==null||!parentOrder.getEnabledFlag()||parentOrder.getOrderStatus()==OrderStatus.cancelled){
						LOGGER.error("订单不存在-或已删除或已取消---------"+parentOrder);
						return insurance;
					}
					OrderItem oi=getPlaneItem(parentOrder);//parentOrder.getOrderItems().get(0);
					if(planeTicket==null){
						String virtualInfo=oi.getVirtualProductOrderInfo();
						planeTicket = JSON.parseObject(virtualInfo, PlaneTicket.class);
					}
					/** 订单状态  1.出票中 2.出票成功 3.部分出票，部分退款待确认（会显示出票成功，但是里面的单会有退款待确认单，不能点击退票）  4.全部退款待确认  5.退票成功*/
					int status=planeTicket.getStatus();
					//只有部分出票，部分退款待确认或者改签的订单才需要拆单
					if(status!=3&&planeTicket.getChangeInfo()==null){
						LOGGER.error("改签拆单数据错误---------status-"+status+"planeTicket.getChangeInfo()---"+planeTicket.getChangeInfo());
						return insurance;
					}
					if(planeTicket.getChangeInfo()!=null){
						if(planeTicket.getChangeInfo().getQunaerOrderNo()==null||
								!qunaerOrderNo.equals(planeTicket.getChangeInfo().getQunaerOrderNo())||
								!planeTicket.isNeedSplit()){
							LOGGER.error("改签拆单数据错误----------");
							return insurance;
						}
					}
					// 获取兄弟节点订单号
					String siblingOrderNo = detail.getSiblingOrderNo();
					// 先取消原来的订单
					Member paymember = parentOrder.getMember();
					if(StringUtils.isNotBlank(paymentLockKey)){//同一请求的锁
						paymember.setPaymentLockKey(paymentLockKey);
					}
					orderService.cancelOrderRefunds(parentOrder, paymember,false);
					parentOrder.setEnabledFlag(false);
					//获取退款之后的员工发放积分明细
					Map<String,List<RechargeRecord>> rechargeRecords=rechargeRecordService.getRefundRechargeRecord(parentOrder);
					//拆单,生成两个新单
					// 生成要改签的订单
					//获取父订单全部乘客
					List<Passenger> passengers=planeTicket.getPassengers();

					//改签的乘客
					PlaneTicket changePlaneTicket = (PlaneTicket)planeTicket.clone();
					List<Passenger> changePassengers = new ArrayList<Passenger>();
					//未改签的乘客
					List<Passenger> noChangePassengers = new ArrayList<Passenger>();
					PlaneTicket noChangePlaneTicket = (PlaneTicket)planeTicket.clone();

					List<String> changeCardNos = new ArrayList<String>();
					List<String> noChangeCardNos = new ArrayList<String>();
					String changeName="";
					String noChangeName="";
					if(planeTicket.getChangeInfo()!=null){//改签
						for(Passenger passenger:passengers){
							if(planeTicket.getChangeInfo().getPassengers().indexOf(passenger.getCardNo())!=-1){
								changePassengers.add(passenger);
								changeCardNos.add(passenger.getCardNo());//需要修改的乘客证件号
								if("".equals(changeName)){
									changeName+=passenger.getName();
								}else{
									changeName+=","+passenger.getName();
								}
							}else{
								noChangePassengers.add(passenger);
								noChangeCardNos.add(passenger.getCardNo());//不用改的乘客证件号
								if("".equals(noChangeName)){
									noChangeName+=passenger.getName();
								}else{
									noChangeName+=","+passenger.getName();
								}
							}
						}
					}else{//退款待确认
						//退款的乘客
						List<Passenger> apiPassengers=orderDetailResponse.getResult().getPassengers();
						for(Passenger passenger : passengers){
							api:for(Passenger passengerApi:apiPassengers){
								if(passenger.getName().equals(passengerApi.getName())&&this.verifyCardNo(passenger.getCardNo(), passengerApi.getCardNum(),(PlaneConfig.PASSPORT_CARD_TYPE.equals(passenger.getCardType())))){
									changePassengers.add(passenger);
									changeCardNos.add(passenger.getCardNo());//需要修改的乘客证件号
									if("".equals(changeName)){
										changeName+=passenger.getName();
									}else{
										changeName+=","+passenger.getName();
									}
									break api;
								}
							}
						}
						noChangePassengers =passengers;
						noChangePassengers.removeAll(changePassengers);
						for(Passenger passenger : noChangePassengers){
							noChangeCardNos.add(passenger.getCardNo());//不用改的乘客证件号
							if("".equals(noChangeName)){
								noChangeName+=passenger.getName();
							}else{
								noChangeName+=","+passenger.getName();
							}
						}
					}

					changePlaneTicket.setChangeInfo(planeTicket.getChangeInfo());
					changePlaneTicket.setPassengers(changePassengers);
					changePlaneTicket.setQty(changePassengers.size());
					LOGGER.info("changePlaneTicket =={}", JSONObject.toJSONString(changePlaneTicket));
					Long productId  = productService.findByJdSku(PlaneConfig.SKU_PLANE_TICKET).getId();
					// 机票总价
					// 计算总价
					int adultPrice = planeTicket.getArf() + planeTicket.getTof()+planeTicket.getPrice();
					int childPrice = planeTicket.getChildtof() + planeTicket.getChildPrice();
					//保存改签订单
					//Order changeOrder =createOrder(parentOrder,changePassengers.size());
					//父订单积分加白条支付金额
					BigDecimal coinAmount=parentOrder.getCoinAmount().add(parentOrder.getWhiteBarAmount());
					//需支付现金
					BigDecimal payCashAmount=BigDecimal.ZERO;
					//需支付积分
					//BigDecimal payCoinAmount=new BigDecimal(price).multiply(new BigDecimal(changePassengers.size()));
					BigDecimal payAmount = getPayCoinAmount(childPrice, adultPrice, changePassengers,planeTicket);
					BigDecimal payCoinAmount = payAmount;
					//父订单支付积分白条小于需支付积分
					if(coinAmount.compareTo(payCoinAmount)==-1){
						payCashAmount=payCoinAmount.subtract(coinAmount);
						payCoinAmount=coinAmount;
						coinAmount=BigDecimal.ZERO;
					}else{
						coinAmount=coinAmount.subtract(payCoinAmount);
					}
					//计算成本
					BigDecimal jdPrice = getOrderCost(changePlaneTicket,changePassengers,payAmount);
					CreateOrderMsg changeOrderMsg=createOrder(rechargeRecords.get("integral"),rechargeRecords.get("whiteBar"),parentOrder.getMember() , productId, JSONObject.toJSONString(changePlaneTicket), payCoinAmount,payCashAmount, qunaerOrderNo,null,parentOrder,jdPrice);
					Order changeOrder =changeOrderMsg.getOrder();
					OrderItem changeOrderItem=getPlaneItem(changeOrder);//changeOrder.getOrderItems().get(0);
					//changeOrderItem.setVirtualProductOrderInfo(JSONObject.toJSONString(changePlaneTicket));
					//changeOrder.setJdOrderId(qunaerOrderNo);
					changeOrderItem.setName(oi.getProduct().getName()+"("+changeName+")");
					changeOrderItem.setFullName(changeOrderItem.getName());
					changeOrder.setParentId(parentOrder);
					changeOrder.setOrderStatus(OrderStatus.completed);
					try{
						if(changePlaneTicket.getChangeInfo().getCompanyFee() != null){// 企业服务费不为空
							changeOrder.setFee(BigDecimal.valueOf(Double.valueOf(changePlaneTicket.getChangeInfo().getCompanyFee())));
						}
					}catch(Exception e){
						LOGGER.info("回填企业服务费异常", e);
					}
					createSuccessOperate(changeOrder);


					//保存未改签订单
					noChangePlaneTicket.setChangeInfo(null);
					noChangePlaneTicket.setPassengers(noChangePassengers);
					noChangePlaneTicket.setQty(noChangePassengers.size());
					//Order noChangeOrder =createOrder(parentOrder,noChangePassengers.size());
					//需支付现金
					payCashAmount=BigDecimal.ZERO;
					//需支付积分
					//payCoinAmount=new BigDecimal(price).multiply(new BigDecimal(noChangePassengers.size()));;
					BigDecimal nPayAmount=getPayCoinAmount(childPrice, adultPrice, noChangePassengers,planeTicket);
					payCoinAmount = nPayAmount;
					//父订单支付积分白条小于需支付积分
					if(coinAmount.compareTo(payCoinAmount)==-1){
						payCashAmount=payCoinAmount.subtract(coinAmount);
						payCoinAmount=coinAmount;
					}
					//计算成本
					BigDecimal noChangeCost = getOrderCost(noChangePlaneTicket,noChangePassengers,nPayAmount);
					CreateOrderMsg noChangeOrderMsg=createOrder(rechargeRecords.get("integral"),rechargeRecords.get("whiteBar"),parentOrder.getMember() , productId, JSONObject.toJSONString(noChangePlaneTicket), payCoinAmount,payCashAmount, siblingOrderNo,null,parentOrder,noChangeCost);
					Order noChangeOrder =noChangeOrderMsg.getOrder();
					OrderItem noChangeOrderItem=getPlaneItem(noChangeOrder);//noChangeOrder.getOrderItems().get(0);
					//noChangeOrderItem.setVirtualProductOrderInfo();
					//noChangeOrder.setJdOrderId(siblingOrderNo);
					noChangeOrderItem.setName(oi.getProduct().getName()+"("+noChangeName+")");
					noChangeOrderItem.setFullName(noChangeOrderItem.getName());
					noChangeOrder.setParentId(parentOrder);
					noChangeOrder.setOrderStatus(OrderStatus.completed);
					createSuccessOperate(noChangeOrder);
					LOGGER.error("{}改签拆单-----success-", qunaerOrderNo);

					try{
						String insuranceOrderSn = null;
						if(planeTicket.getSuccessChangeInfo() != null){//有改签记录 
							insuranceOrderSn = planeTicket.getSuccessChangeInfo().getInsuranceOrderSn();
						} else {
							insuranceOrderSn = planeTicket.getInsuranceOrderSn();
						}

						if(StringUtils.isNotBlank(insuranceOrderSn)){
							LOGGER.info("{}原机票订单有保险信息,需要拆保险单{}", qunaerOrderNo, insuranceOrderSn);
							insurance = new PlaneInsuranceVo();
							insurance.setChangeCardIds(changeCardNos);//改签的证件信息
							insurance.setNoChangeCardIds(noChangeCardNos);//不改钱的证件信息
							insurance.setParentSn(insuranceOrderSn);//原保险单
							insurance.setChangeSn(changeOrder.getSn());//改签的订单sn
							insurance.setNoChangeSn(noChangeOrder.getSn());//不改签的订单Sn
						}
					} catch(Exception e){
						LOGGER.error("{}改签拆单---处理保险拆单异常：{}", qunaerOrderNo, e);
					}
				}
			}
		}
		return insurance;
	}
	
	//计算订单成本
	private BigDecimal getOrderCost(PlaneTicket planeTicket,List<Passenger> passengers,BigDecimal payAmount) {
		BigDecimal feeTotal=BigDecimal.ZERO;
		try {
			for(Passenger passenger:passengers) {
				if(passenger.getAgeType()==1) {//儿童票
					feeTotal=feeTotal.add(BigDecimal.valueOf(planeTicket.getChildFee()));
					if(planeTicket.getChildServiceCharge()!=null) {
						feeTotal=feeTotal.add(planeTicket.getChildServiceCharge());
					}
				}else {//成人
					feeTotal=feeTotal.add(new BigDecimal(planeTicket.getFee()));
					if(planeTicket.getServiceCharge()!=null) {
						feeTotal=feeTotal.add(planeTicket.getServiceCharge());
					}
				}
			}
		}catch(Exception e) {
			LOGGER.error("计算订单成本异常:",e);
		}
		//成本=支付金额-服务费和企业加价
		return payAmount.subtract(feeTotal);
	}
	/**
	 * 计算支付积分金额
	 * @param childPrice 儿童票价格
	 * @param adultPrice 成人票价格
	 * @param passengers 乘客
	 * @return
	 */
	private BigDecimal getPayCoinAmount(int childPrice,int adultPrice,List<Passenger> passengers,PlaneTicket planeTicket) {
		BigDecimal payCoinAmount=BigDecimal.ZERO;
		for(Passenger passenger:passengers) {
			if(passenger.getAgeType()==1) {//儿童票
				payCoinAmount=payCoinAmount.add(new BigDecimal(childPrice));
				if(planeTicket.getChildServiceCharge()!=null) {
					payCoinAmount=payCoinAmount.add(planeTicket.getChildServiceCharge());
				}
			}else {//儿童票
				payCoinAmount=payCoinAmount.add(new BigDecimal(adultPrice));
				if(planeTicket.getServiceCharge()!=null) {
					payCoinAmount=payCoinAmount.add(planeTicket.getServiceCharge());
				}
			}
		}
		return payCoinAmount;
	}
	/**
	 * 设置而突破价格
	 * @param priceInfo 信息
	 * @param planeTicket 机票信息
	 */
	public void setChildPrice(BookingPriceInfo priceInfo,PlaneTicket planeTicket){
		if(StringUtils.isNotBlank(priceInfo.getChildPriceType()) && !"0".equals(priceInfo.getChildPriceType())) {
			Map<String, List<PackageInfo>> priceTag=priceInfo.getPriceTag();
			List<PackageInfo> pis=priceTag.get("CHI");
			if(CollectionUtils.isNotEmpty(pis)) {
				PackageInfo pi=	pis.get(0);
				if(pi!=null) {
					Company company=memberService.getCurrent().getCompanyId();
					Supplier supplier=supplierService.findByProperties(Filter.eq("isOweOrder", 25),Filter.eq("enabledFlag", true));
					String rule=company.getSupplierPriceRuleBySupplierId(supplier.getId());
					planeTicket.setChildPrice(calculatePrice(new BigDecimal(pi.getBarePrice()),rule).intValue());
					planeTicket.setChildCost(pi.getBarePrice().intValue());
				}
			}
		}
	}
	/**
	 * 计算机票价格
	 * @param price 机票价格
	 * @param rule 计算规则
	 * @return 返回加价后的机票价格
	 */
	public BigDecimal calculatePrice(BigDecimal price,String rule){
		try {
			//	LOGGER.info("价格："+price+"规则："+rule+"--------------");
			//价格为空直接返回
			if(price==null){
				return price;
			}
			//计算规则为空获取默认规则
			if(StringUtils.isBlank(rule)){
				//获取默认配置
				SysCode sysCode=sysCodeService.findbyCode(SysCode.SYS_GROUP, PLANE_PRICE_DISCIPLINE_KEY_NAME);
				//规则不存在直接返回
				if(sysCode==null||StringUtils.isBlank(sysCode.getValue())){
					return price;
				}
				rule=sysCode.getValue();
			}
			String[] ruleArray=rule.split(";");
			if(ruleArray==null||ruleArray.length==0){
				return price;
			}
			//开始匹配规则
			for (String value : ruleArray) {
				//表达式如 [100,200]add(10)  [100,200]mul(1.03)
				//[价格区间]add 为加mul为乘

				//表达式为空
				if(StringUtils.isBlank(value)){
					break;
				}
				//获取价格区间
				String priceSection=CommUtil.getSubUtilSimple(value,PRICE_DISCIPLINE_RGEX );
				//价格区间为空
				if(StringUtils.isBlank(priceSection)){
					break;
				}
				String[] priceArray=priceSection.split(",");
				//判断价格是否符合价格区间
				if(priceArray==null||priceArray.length!=2){
					break;
				}
				//机票价格在区间内
				if(price.compareTo(new BigDecimal(priceArray[0].trim()))!=-1&&price.compareTo(new BigDecimal(priceArray[1].trim()))!=1){
					//加
					if(value.indexOf(PLANE_ADD)!=-1){
						//获取手续费
						String fee=CommUtil.getSubUtilSimple(value,PRICE_ADD_RGEX ).trim();
						price=price.add(new BigDecimal(fee)).setScale(0,BigDecimal.ROUND_UP);
						//乘	
					}else if(value.indexOf(PLANE_MULTIPLY)!=-1){
						//获取手续费
						String ratio=CommUtil.getSubUtilSimple(value, PRICE_MUL_RGEX).trim();
						price=price.multiply(new BigDecimal(ratio)).setScale(0,BigDecimal.ROUND_UP);
					}
					return price;
				}
			}
		} catch (Exception e) {
			// TODO: handle exception
			LOGGER.error("价格规则获取解析失败--------直接返回机票原价", e);
			e.printStackTrace();
		}
		return  price;
	}

	/**
	 * 发送短信
	 * @param planeTicket 机票信息
	 * @param order 订单
	 * @param type 类型
	 * @return
	 */
	private boolean sendSms(PlaneTicket planeTicket,Order order,String type){
		try {
			String flightInfo="["+planeTicket.getCarrierName()+planeTicket.getFlightNum()+"]";
			String sn=order.getSn();
			String passengerNames="";
			List<Passenger> passengers=planeTicket.getPassengers();
			for(Passenger passenger:passengers){
				if(!"".equals(passengerNames)){
					passengerNames+="、";
				}
				passengerNames+=passenger.getName();
			}
			String deptDate=planeTicket.getDptDate()+" "+planeTicket.getDptTime();
			String[] parameters=new String[]{sn,passengerNames,deptDate,flightInfo};
			return smsService.send(order.getCompanyId(), planeTicket.getContactMob(), type, parameters);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return false;
	}
	/**
	 * 检查航班
	 * 1.检查乘客是否在同样的航班同样的时间已有机票订单
	 * @param planeTicket 机票信息
	 * @return 是否可以下单
	 */
	public Message checkRepetitionFlight(PlaneTicket planeTicket){
		try {
			if(planeTicket==null){
				return Message.success("");
			}
			List<Filter> filters=new ArrayList<Filter>();
			//查询机票供应商
			List<Integer> isOweOrderValues = new ArrayList<Integer>();
			isOweOrderValues.add(10);
			isOweOrderValues.add(16);
			filters.add(Filter.in("isOweOrder", isOweOrderValues));
			filters.add(Filter.eq("enabledFlag", true));
			List<Supplier> suppliers=supplierService.findList(null, filters, null);
			//机票供应商为空
			if(CollectionUtils.isEmpty(suppliers)){
				return Message.success("");
			}
			//查询机票订单
			filters=new ArrayList<Filter>();
			List<OrderStatus> orderStatuss=new ArrayList<OrderStatus>();
			orderStatuss.add( OrderStatus.unpaid);
			orderStatuss.add( OrderStatus.unconfirmed);
			orderStatuss.add( OrderStatus.confirmed);
			orderStatuss.add( OrderStatus.shipped);
			orderStatuss.add( OrderStatus.completed);
			filters.add(Filter.in("orderStatus",orderStatuss));
			filters.add(Filter.in("supplierId", suppliers));
			filters.add(Filter.isNotNull("jdOrderId"));
			//查询半小时之内订单
			filters.add(Filter.dateGe("createDate", new Date( System.currentTimeMillis() - 30 * 60 * 1000)));
			List<Order> orders=orderService.findList(null, filters, null);
			//订单为空
			if(CollectionUtils.isEmpty(orders)){
				return Message.success("");
			}
			//开始比对
			for(Order order:orders){

				String virtualProductOrderInfo=order.getOrderItems().get(0).getVirtualProductOrderInfo();
				OrderItem oi = getPlaneItem(order);//获取机票item
				if(oi != null){
					virtualProductOrderInfo=oi.getVirtualProductOrderInfo();
				}
				if(StringUtils.isNotBlank(virtualProductOrderInfo)){
					PlaneTicket orderInfo=JSON.parseObject(virtualProductOrderInfo, PlaneTicket.class);
					//对比航班和出发日期 
					if(orderInfo!=null
							&&orderInfo.getFlightNum().equals(planeTicket.getFlightNum())
							&&orderInfo.getDptDate().equals(planeTicket.getDptDate())
							&&orderInfo.getDptTime().equals(planeTicket.getDptTime())){
						List<Passenger> passengers=planeTicket.getPassengers();
						for(Passenger passenger:passengers){
							//对比证件号
							if(virtualProductOrderInfo.indexOf(passenger.getCardNo())!=-1){
								return Message.error("乘机人"+passenger.getName()+"（"+passenger.getCardNo()+"）已预定同一航班机票，请勿重复购票");
							}
						}
					}
				}
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return Message.success("");
	}

	/**
	 * 提交订单 tq
	 * @param order
	 * @param map
	 * @param rechargeRecords
	 * @param whiteBarRecords
	 * @param rechargeRecordAmount
	 * @param whiteBarAmount
	 * @param request
	 * @return
	 */
	@Override
	public CreateOrderMsg submitOrder(Order order, Map<String, Object> map, List<RechargeRecord> rechargeRecords, List<RechargeRecord> whiteBarRecords, BigDecimal rechargeRecordAmount, BigDecimal whiteBarAmount, HttpServletRequest request) {
		CreateOrderMsg orderMsg = null;
		if(CollectionUtils.isNotEmpty(rechargeRecords) || CollectionUtils.isNotEmpty(whiteBarRecords)){
			orderService.deductCoin(rechargeRecords, whiteBarRecords, order); //扣减白条
		}
		BigDecimal totalAmount=order.getToatlAmount(); //订单总金额
		LOGGER.info("orderSn={},totalAmount={}",order.getSn(),totalAmount);
		if(order.getCouponDiscount()!=null){
			totalAmount=totalAmount.subtract(order.getCouponDiscount());
			LOGGER.info("orderSn={},couponDiscount={}",order.getSn(),order.getCouponDiscount());
		}
		LOGGER.info("orderSn={},amountPaid={}",order.getSn(),order.getAmountPaid());
		if (order.getAmountPaid() != null && order.getAmountPaid().compareTo(totalAmount) >= 0) { //订单已支付完成
			LOGGER.info("orderSn={},已支付完成开始向去哪儿提交订单",order.getAmountPaid());
			OrderItem oi = getPlaneItem(order);//order.getOrderItems().get(0);

			String virtualProductOrderInfo = oi.getVirtualProductOrderInfo();
			PlaneTicket planeTicket = FastJsonUtils.toBean(virtualProductOrderInfo, PlaneTicket.class);
			if(PlaneConfig.SKU_CHANGE_PLANE_TICKET.equals(oi.getProduct().getJdSku())){  //  改签
				String allFee = planeTicket.getChangeInfo().getAllFee();
				if (allFee == null || allFee.equals(0)){
					orderService.cancelOrderRefunds(order,order.getMember());
				}
				/*
				ChangePayrResponse changePayrResponse = changePay(planeTicket.getChangeInfo());
				if (changePayrResponse.getResult().getResults() != null && changePayrResponse.getResult().getResults().size()>0){
					PayFormResult payFormResult = changePayrResponse.getResult().getResults().get(0);
					if (payFormResult.getPayStatus().equals("SUCCESS")){
						orderMsg = new CreateOrderMsg(order,true,changePayrResponse.getMessage());
					}else {
						orderMsg = new CreateOrderMsg(order,false,payFormResult.getErrMsg());
					}
				}else {
					orderMsg = new CreateOrderMsg(order,false,changePayrResponse.getMessage());
				}*/
				orderMsg = new CreateOrderMsg(order,true,"订单支付完成");
			}else{
				orderMsg = pay(planeTicket,order);    //申请出票
			}
			return orderMsg;
		}else{
			orderMsg = new CreateOrderMsg(order,false,"订单支付未完成");
			return orderMsg;
		}
	}


	/**
	 *
	 * 方法:
	 *   1.取消订单重试 tq
	 * 实现流程  : <br/>
	 * @param order
	 *      订单号
	 * 编码作者 :
	 * 完成日期 : 2019-03-15 19:02:53
	 */
	public void retryCancel(Order order) {
		if (order.getOrderStatus() != Order.OrderStatus.cancelled) {
			List<Long> orderIds = new ArrayList<>();
			orderIds.add(order.getId());
			BigDecimal returndCredit = coinLogService.findReturndCredit(orderIds);
			if (returndCredit.compareTo(new BigDecimal(0)) <= 0) { //未退
				orderService.cancelOrderRefunds(order, order.getMember());       //需取消订单

				if(order.getCompanyId() != null && !StringUtils.isEmpty(order.getCompanyId().getCoinConfKey())){//第三方积分配置
					//第三方积分回退
					coinAmountService.thirdCoinRefunds(order.getCompanyId(), order.getMember(), order, null, null);
				}
			}
		}
	}


	/**
	 * 方法: 订单支付时校验票价和服务费 tq
	 * 实现流程:
	 *
	 * @param order
	 * @return
	 */
	@Override
	public boolean checkPrice(Order order) {
		String virtualProductOrderInfo = order.getOrderItems().get(0).getVirtualProductOrderInfo();
		OrderItem oi = getPlaneItem(order);//获取机票item
		if(oi != null){
			virtualProductOrderInfo=oi.getVirtualProductOrderInfo();
		}
		BigDecimal totalTicketPrice=new BigDecimal(0);
		BigDecimal totalServiceCharge=new BigDecimal(0);
		PlaneTicket planeTicket = FastJsonUtils.toBean(virtualProductOrderInfo, PlaneTicket.class);
		BigDecimal gqFee = new BigDecimal(0);
		if (planeTicket.getChangeInfo() != null){   //添加改签费用
			String allFee = planeTicket.getChangeInfo().getAllFee();
			gqFee = new BigDecimal(allFee);
		}
		totalTicketPrice = new BigDecimal(planeTicket.getTotalPrice());
		totalServiceCharge = planeTicket.getServiceCharge();
		BigDecimal totalPrice=totalTicketPrice.add(totalServiceCharge).add(gqFee);
		if(order.getToatlAmount().compareTo(totalPrice)<0){ // 订单价格小于票价加服务费
			return false;
		}
		return true;
	}

	/**
	 * 方法: 取消航意险保单<br/>
	 * 实现流程  : <br/>
	 * @param planeParentOrder	原飞机订单
	 * @param cardId			改签/退票的乘客证件号
	 * @param planeOrder		改签后的飞机订单
	 * 编码作者 : 吴锭超
	 * 完成日期 : 2020-04-02 10:00:23
	 */
	@Transactional
	public void cancelInsuranceOrder(Order planeParentOrder, List<String> cardId, Order planeOrder, String orderSn){//取消航意险订单
		try {//try-catch模块,确保原有流程不受影响

			Order insuranceOrder = null;//保险订单
			Supplier supplier = supplierService.getSupplierByPlatform(34);//保险供应商
			if(StringUtils.isNotBlank(orderSn)){
				insuranceOrder = orderDao.findBySn(orderSn);//保险订单
			} else if(planeParentOrder != null && planeParentOrder.getParentId() != null){//机票订单不为空且父单不为空
				if(supplier != null){//保险供应商不为空
					insuranceOrder = orderDao.findInsuranceOrder(planeParentOrder.getParentId().getId(), supplier.getId());
				} else {//保险供应商不存在
					LOGGER.info("保险供应商不存在~");
					return;
				}
			}
//			if(insuranceOrder != null && insuranceOrder.getOrderStatus() == OrderStatus.completed){//航意险订单不为空且为完成状态
			if(insuranceOrder != null && insuranceOrder.getOrderStatus() != OrderStatus.cancelled && insuranceOrder.getOrderStatus() != OrderStatus.unpaid){//航意险订单不为空且为没取消状态
				LOGGER.info("{}有航意险信息,需要退款航意险订单", planeParentOrder.getSn());
				//1.调接口取消
				Message message = insuranceService.cancleOrder(insuranceOrder.getSn(), insuranceOrder.getJdOrderId(), cardId);//调接口取消订单并退款

				if(message.getType() == Message.Type.success){//退保成功
					//2.取消原来订单并退款
					orderService.cancelOrderRefunds(insuranceOrder, insuranceOrder.getMember(), true);//取消订单并退款

					if(!planeParentOrder.getEnabledFlag()){//原保险单关联的机票订单为无效状态，同步保险单也为无效状态
						insuranceOrder.setEnabledFlag(false);
					}
					if(cardId != null && cardId.size() > 0 && planeOrder != null){//只取消某个人的订单

						//获取退款之后的员工发放积分明细
						Map<String,List<RechargeRecord>> rechargeRecords = rechargeRecordService.getRefundRechargeRecord(insuranceOrder);

						CreateOrderMsg msg = insuranceService.createOrder(rechargeRecords.get("integral"), rechargeRecords.get("whiteBar"), cardId, insuranceOrder);
						Order order = msg.getOrder();//获取创建的订单
						order.setParentId(planeOrder.getParentId());//和机票定案挂同一个父单号
						order.setFreezingId(insuranceOrder.getFreezingId());//保存最开始的保险单号--方便追溯
						order.setOrderStatus(OrderStatus.completed);//设置订单为已完成
						orderService.update(order);//更新订单

					} else if(planeOrder == null){//使用原来的订单--则直接取消保险订单--

					}
				} else {
					LOGGER.info("{}有航意险信息,退款航意险订单失败：{}", planeParentOrder.getSn(), message.getContent());
				}
			} else {
				LOGGER.info("{}订单没找到关联度航意险保单{}", planeParentOrder.getSn(), orderSn);
			}
		} catch(Exception e){
			LOGGER.error("航意险保单取消失败", e);
		}
	}

	/**
	 *
	 * 方法: 获取机票orderItem项<br/>
	 * 实现流程  : <br/>
	 * @param order
	 * @return
	 * 编码作者 : 吴锭超
	 * 完成日期 : 2020-03-30 18:03:03
	 */
	private OrderItem getPlaneItem(Order order){
		OrderItem planeItem = null;
		if(order != null){//订单不为空
			if(order.getOrderItems().size() > 1){//订单项2个以上，说明有犀牛保险信息
				for(OrderItem item : order.getOrderItems()){
					if(item.getProduct().getSupplierId() == order.getSupplierId()){//机票供应商
						planeItem = item;
						break;
					}
				}
			} else {
				planeItem = order.getOrderItems().get(0);//去第一个item
			}
		}

		return planeItem;
	}

}
